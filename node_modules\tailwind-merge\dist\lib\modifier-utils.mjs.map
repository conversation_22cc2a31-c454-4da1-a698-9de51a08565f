{"version": 3, "file": "modifier-utils.mjs", "sources": ["../../src/lib/modifier-utils.ts"], "sourcesContent": ["import { Config } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport function createSplitModifiers(config: Config) {\n    const separator = config.separator || ':'\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className: string) {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport function sortModifiers(modifiers: string[]) {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n"], "names": ["IMPORTANT_MODIFIER", "createSplitModifiers", "config", "separator", "isSeparatorSingleCharacter", "length", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "splitModifiers", "className", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "slice", "push", "baseClassNameWithImportantModifier", "substring", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "undefined", "sortModifiers", "sortedModifiers", "unsortedModifiers", "for<PERSON>ach", "modifier", "isArbitraryVariant", "sort"], "mappings": "AAEO,IAAMA,kBAAkB,GAAG,IAAG;AAE/B,SAAUC,oBAAoB,CAACC,MAAc,EAAA;AAC/C,EAAA,IAAMC,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI,GAAG,CAAA;AACzC,EAAA,IAAMC,0BAA0B,GAAGD,SAAS,CAACE,MAAM,KAAK,CAAC,CAAA;AACzD,EAAA,IAAMC,uBAAuB,GAAGH,SAAS,CAAC,CAAC,CAAC,CAAA;AAC5C,EAAA,IAAMI,eAAe,GAAGJ,SAAS,CAACE,MAAM,CAAA;AAExC;AACA,EAAA,OAAO,SAASG,cAAc,CAACC,SAAiB,EAAA;IAC5C,IAAMC,SAAS,GAAG,EAAE,CAAA;IAEpB,IAAIC,YAAY,GAAG,CAAC,CAAA;IACpB,IAAIC,aAAa,GAAG,CAAC,CAAA;AACrB,IAAA,IAAIC,uBAA2C,CAAA;AAE/C,IAAA,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,SAAS,CAACJ,MAAM,EAAES,KAAK,EAAE,EAAE;AACnD,MAAA,IAAIC,gBAAgB,GAAGN,SAAS,CAACK,KAAK,CAAC,CAAA;MAEvC,IAAIH,YAAY,KAAK,CAAC,EAAE;AACpB,QAAA,IACII,gBAAgB,KAAKT,uBAAuB,KAC3CF,0BAA0B,IACvBK,SAAS,CAACO,KAAK,CAACF,KAAK,EAAEA,KAAK,GAAGP,eAAe,CAAC,KAAKJ,SAAS,CAAC,EACpE;UACEO,SAAS,CAACO,IAAI,CAACR,SAAS,CAACO,KAAK,CAACJ,aAAa,EAAEE,KAAK,CAAC,CAAC,CAAA;UACrDF,aAAa,GAAGE,KAAK,GAAGP,eAAe,CAAA;AACvC,UAAA,SAAA;AACH,SAAA;QAED,IAAIQ,gBAAgB,KAAK,GAAG,EAAE;AAC1BF,UAAAA,uBAAuB,GAAGC,KAAK,CAAA;AAC/B,UAAA,SAAA;AACH,SAAA;AACJ,OAAA;MAED,IAAIC,gBAAgB,KAAK,GAAG,EAAE;AAC1BJ,QAAAA,YAAY,EAAE,CAAA;AACjB,OAAA,MAAM,IAAII,gBAAgB,KAAK,GAAG,EAAE;AACjCJ,QAAAA,YAAY,EAAE,CAAA;AACjB,OAAA;AACJ,KAAA;AAED,IAAA,IAAMO,kCAAkC,GACpCR,SAAS,CAACL,MAAM,KAAK,CAAC,GAAGI,SAAS,GAAGA,SAAS,CAACU,SAAS,CAACP,aAAa,CAAC,CAAA;AAC3E,IAAA,IAAMQ,oBAAoB,GACtBF,kCAAkC,CAACG,UAAU,CAACrB,kBAAkB,CAAC,CAAA;IACrE,IAAMsB,aAAa,GAAGF,oBAAoB,GACpCF,kCAAkC,CAACC,SAAS,CAAC,CAAC,CAAC,GAC/CD,kCAAkC,CAAA;AAExC,IAAA,IAAMK,4BAA4B,GAC9BV,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAa,GAC5DC,uBAAuB,GAAGD,aAAa,GACvCY,SAAS,CAAA;IAEnB,OAAO;AACHd,MAAAA,SAAS,EAATA,SAAS;AACTU,MAAAA,oBAAoB,EAApBA,oBAAoB;AACpBE,MAAAA,aAAa,EAAbA,aAAa;AACbC,MAAAA,4BAA4B,EAA5BA,4BAAAA;KACH,CAAA;GACJ,CAAA;AACL,CAAA;AAEA;;;;AAIG;AACG,SAAUE,aAAa,CAACf,SAAmB,EAAA;AAC7C,EAAA,IAAIA,SAAS,CAACL,MAAM,IAAI,CAAC,EAAE;AACvB,IAAA,OAAOK,SAAS,CAAA;AACnB,GAAA;EAED,IAAMgB,eAAe,GAAa,EAAE,CAAA;EACpC,IAAIC,iBAAiB,GAAa,EAAE,CAAA;AAEpCjB,EAAAA,SAAS,CAACkB,OAAO,CAAC,UAACC,QAAQ,EAAI;AAC3B,IAAA,IAAMC,kBAAkB,GAAGD,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;AAE9C,IAAA,IAAIC,kBAAkB,EAAE;MACpBJ,eAAe,CAACT,IAAI,CAAA,KAAA,CAApBS,eAAe,EAASC,iBAAiB,CAACI,IAAI,EAAE,CAAEF,MAAAA,CAAAA,CAAAA,QAAQ,CAAC,CAAA,CAAA,CAAA;AAC3DF,MAAAA,iBAAiB,GAAG,EAAE,CAAA;AACzB,KAAA,MAAM;AACHA,MAAAA,iBAAiB,CAACV,IAAI,CAACY,QAAQ,CAAC,CAAA;AACnC,KAAA;AACL,GAAC,CAAC,CAAA;EAEFH,eAAe,CAACT,IAAI,CAApBS,KAAAA,CAAAA,eAAe,EAASC,iBAAiB,CAACI,IAAI,EAAE,CAAC,CAAA;AAEjD,EAAA,OAAOL,eAAe,CAAA;AAC1B;;;;"}