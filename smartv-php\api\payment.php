<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['customerName']) || !isset($input['customerEmail']) || 
    !isset($input['customerDocument']) || !isset($input['planType']) || !isset($input['amount'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

try {
    // Validate document (CPF/CNPJ)
    $document = preg_replace('/\D/', '', $input['customerDocument']);
    if (!validateDocument($document)) {
        throw new Exception('Invalid document number');
    }

    // Validate amount
    if ($input['amount'] <= 0) {
        throw new Exception('Invalid amount');
    }

    // Get Mercado Pago credentials
    $accessToken = getMercadoPagoAccessToken();
    
    // Create PIX payment using Mercado Pago API directly
    $paymentData = [
        'transaction_amount' => floatval($input['amount']),
        'description' => 'SmartV IPTV - Plano ' . $input['planType'],
        'payment_method_id' => 'pix',
        'payer' => [
            'email' => $input['customerEmail'],
            'first_name' => explode(' ', $input['customerName'])[0],
            'last_name' => implode(' ', array_slice(explode(' ', $input['customerName']), 1)) ?: explode(' ', $input['customerName'])[0],
            'identification' => [
                'type' => strlen($document) === 11 ? 'CPF' : 'CNPJ',
                'number' => $document
            ]
        ],
        'external_reference' => 'SMARTV_' . $input['planType'] . '_' . time(),
        'notification_url' => 'https://smartv.shop/webhook/mercadopago',
        'date_of_expiration' => date('c', time() + 15 * 60) // 15 minutes from now
    ];

    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken,
                'X-Idempotency-Key: SMARTV_' . $input['planType'] . '_' . time()
            ],
            'content' => json_encode($paymentData),
            'timeout' => 30
        ]
    ]);

    $response = file_get_contents('https://api.mercadopago.com/v1/payments', false, $context);

    if ($response === false) {
        throw new Exception('Mercado Pago API request failed');
    }

    $paymentResponse = json_decode($response, true);
    
    if (!$paymentResponse || !isset($paymentResponse['id'])) {
        throw new Exception('Failed to create Mercado Pago payment');
    }

    // Extract PIX information
    $pixInfo = $paymentResponse['point_of_interaction']['transaction_data'] ?? null;
    
    if (!$pixInfo || !isset($pixInfo['qr_code']) || !isset($pixInfo['qr_code_base64'])) {
        throw new Exception('PIX QR Code not generated');
    }

    echo json_encode([
        'success' => true,
        'id' => strval($paymentResponse['id']),
        'qrCode' => $pixInfo['qr_code'],
        'qrCodeUrl' => 'data:image/png;base64,' . $pixInfo['qr_code_base64'],
        'pixKey' => $pixInfo['qr_code'],
        'amount' => floatval($input['amount']),
        'transactionAmount' => floatval($paymentResponse['transaction_amount'] ?? $input['amount']),
        'description' => $paymentResponse['description'] ?? 'SmartV IPTV - Plano ' . $input['planType'],
        'expiresAt' => $paymentResponse['date_of_expiration'] ?? date('c', time() + 15 * 60),
        'status' => $paymentResponse['status'] ?? 'pending'
    ]);

} catch (Exception $e) {
    error_log('Error in payment API: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create PIX payment: ' . $e->getMessage()
    ]);
}

function getMercadoPagoAccessToken() {
    // Using the provided test token
    return 'TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072';
}

function validateDocument($document) {
    if (strlen($document) === 11) {
        return validateCPF($document);
    } elseif (strlen($document) === 14) {
        return validateCNPJ($document);
    }
    return false;
}

function validateCPF($cpf) {
    if (strlen($cpf) !== 11 || preg_match('/^(\d)\1{10}$/', $cpf)) {
        return false;
    }
    
    $sum = 0;
    for ($i = 0; $i < 9; $i++) {
        $sum += intval($cpf[$i]) * (10 - $i);
    }
    $digit1 = ($sum * 10) % 11;
    if ($digit1 === 10) $digit1 = 0;
    
    $sum = 0;
    for ($i = 0; $i < 10; $i++) {
        $sum += intval($cpf[$i]) * (11 - $i);
    }
    $digit2 = ($sum * 10) % 11;
    if ($digit2 === 10) $digit2 = 0;
    
    return intval($cpf[9]) === $digit1 && intval($cpf[10]) === $digit2;
}

function validateCNPJ($cnpj) {
    if (strlen($cnpj) !== 14 || preg_match('/^(\d)\1{13}$/', $cnpj)) {
        return false;
    }
    
    $weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    $weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    
    $sum = 0;
    for ($i = 0; $i < 12; $i++) {
        $sum += intval($cnpj[$i]) * $weights1[$i];
    }
    $digit1 = ($sum % 11) < 2 ? 0 : 11 - ($sum % 11);
    
    $sum = 0;
    for ($i = 0; $i < 13; $i++) {
        $sum += intval($cnpj[$i]) * $weights2[$i];
    }
    $digit2 = ($sum % 11) < 2 ? 0 : 11 - ($sum % 11);
    
    return intval($cnpj[12]) === $digit1 && intval($cnpj[13]) === $digit2;
}
?>
