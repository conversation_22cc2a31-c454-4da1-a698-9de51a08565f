/* Estilos customizados para SweetAlert2 - Padrões organizados */

/* Remover todas as barras de rolagem do SweetAlert2 */
.swal2-container,
.swal2-container *,
.swal2-popup,
.swal2-popup *,
.swal2-html-container,
.swal2-content,
.swal2-modal {
  overflow: hidden !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Webkit browsers (Chrome, Safari, Edge) */
.swal2-container::-webkit-scrollbar,
.swal2-container *::-webkit-scrollbar,
.swal2-popup::-webkit-scrollbar,
.swal2-popup *::-webkit-scrollbar,
.swal2-html-container::-webkit-scrollbar,
.swal2-content::-webkit-scrollbar,
.swal2-modal::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Configurações gerais do popup */
.swal-clean-theme {
  border-radius: 15px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  overflow: hidden !important;
}

/* Título dos alertas */
.swal-clean-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 15px !important;
}

/* Conteúdo dos alertas */
.swal-clean-content {
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #4b5563 !important;
  overflow: hidden !important;
}

/* Remover barras de rolagem de todos os elementos SweetAlert2 */
.swal2-popup {
  overflow: hidden !important;
}

/* Estilos específicos para o modal de resultado de teste */
.test-result-modal .test-result-content {
  max-height: 300px !important;
  overflow-y: auto !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Webkit browsers (Chrome, Safari, Edge) - esconder barra de rolagem */
.test-result-modal .test-result-content::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Garantir que o conteúdo seja rolável mas sem barra visível */
.test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.test-result-content::-webkit-scrollbar {
  display: none !important;
}

/* Sobrescrever estilos de scrollbar do sweetalert2-custom.css para o modal de teste */
.test-result-modal .swal2-html-container::-webkit-scrollbar,
.test-result-modal .test-result-content::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.test-result-modal .swal2-html-container,
.test-result-modal .test-result-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.swal2-html-container {
  overflow: hidden !important;
  max-height: none !important;
}

.swal2-content {
  overflow: hidden !important;
}

.swal2-modal {
  overflow: hidden !important;
}

/* Botão principal (confirmar) */
.swal-clean-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: white !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.swal-clean-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15) !important;
}

.swal-clean-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

/* Botão cancelar */
.swal-clean-cancel-button {
  background: #6b7280 !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: white !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.swal-clean-cancel-button:hover {
  background: #4b5563 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15) !important;
}

/* Spinner de loading customizado - apenas um círculo */
.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ocultar o spinner padrão do SweetAlert2 */
.swal2-loader {
  display: none !important;
}

/* Inputs customizados para formulários */
.swal2-input {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: border-color 0.2s ease !important;
  background: #ffffff !important;
}

.swal2-input:focus {
  border-color: #667eea !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.swal2-input::placeholder {
  color: #9ca3af !important;
}

/* Validação de erro */
.swal2-validation-message {
  background: #fef2f2 !important;
  color: #dc2626 !important;
  border: 1px solid #fecaca !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  margin-top: 8px !important;
}

/* Responsividade aprimorada */
@media (max-width: 480px) {
  .swal-clean-theme {
    margin: 8px !important;
    width: calc(100% - 16px) !important;
    max-width: none !important;
    min-height: auto !important;
  }

  .swal-clean-title {
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin-bottom: 12px !important;
  }

  .swal-clean-content {
    font-size: 13px !important;
    line-height: 1.5 !important;
    padding: 0 8px !important;
  }

  .swal-clean-button,
  .swal-clean-cancel-button {
    padding: 8px 16px !important;
    font-size: 12px !important;
    min-width: 80px !important;
  }

  .swal2-actions {
    flex-direction: column !important;
    gap: 8px !important;
    width: 100% !important;
  }

  .swal2-actions button {
    width: 100% !important;
    margin: 0 !important;
  }
}

@media (max-width: 640px) and (min-width: 481px) {
  .swal-clean-theme {
    margin: 12px !important;
    width: calc(100% - 24px) !important;
  }

  .swal-clean-title {
    font-size: 18px !important;
  }

  .swal-clean-button,
  .swal-clean-cancel-button {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 768px) and (min-width: 641px) {
  .swal-clean-theme {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-width: 500px !important;
  }
}

/* Cores específicas para cada tipo de alerta */

/* Sucesso - Verde */
.swal-success .swal-clean-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.swal-success .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3) !important;
}

/* Erro - Vermelho */
.swal-error .swal-clean-button {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.swal-error .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3) !important;
}

/* Informação - Ciano */
.swal-info .swal-clean-button {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

.swal-info .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.3) !important;
}

/* Aviso - Laranja */
.swal-warning .swal-clean-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.swal-warning .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3) !important;
}

/* Confirmação - Azul */
.swal-confirm .swal-clean-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.swal-confirm .swal-clean-button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* Estilos para modal de tutoriais */
.tutorials-modal {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.tutorials-modal .swal2-html-container {
  padding: 0 !important;
  margin: 0 !important;
  max-height: 70vh !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar {
  width: 6px !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-track {
  background: transparent !important;
}

.tutorials-modal .swal2-html-container::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5) !important;
  border-radius: 20px !important;
  border: transparent !important;
}

.tutorials-modal .swal2-popup {
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

/* ===== RESPONSIVIDADE MOBILE ===== */

/* Configurações gerais para mobile */
@media (max-width: 480px) {
  /* Todos os modais em mobile */
  .swal2-popup {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 85vh !important;
    margin: 10px !important;
    padding: 15px !important;
  }

  /* Títulos menores em mobile */
  .swal2-title {
    font-size: 18px !important;
    line-height: 1.3 !important;
    margin-bottom: 15px !important;
  }

  /* Conteúdo com scroll em mobile */
  .swal2-html-container {
    max-height: 60vh !important;
    overflow-y: auto !important;
    padding: 10px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  /* Botões menores em mobile */
  .swal2-actions {
    flex-direction: column !important;
    gap: 10px !important;
    width: 100% !important;
  }

  .swal2-confirm,
  .swal2-cancel {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important;
    margin: 0 !important;
  }
}

/* Modal de teste específico para mobile */
@media (max-width: 480px) {
  .test-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .test-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }
}

/* Modal de tutoriais específico para mobile */
@media (max-width: 480px) {
  .tutorials-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .tutorials-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 10px !important;
  }
}

/* Modal PIX específico para mobile */
@media (max-width: 480px) {
  .pix-modal .swal2-popup {
    width: 95vw !important;
    max-height: 85vh !important;
  }

  .pix-modal .swal2-html-container {
    max-height: 65vh !important;
    padding: 15px !important;
  }
}

/* ===== ESTILOS DOS INPUTS DOS MODAIS ===== */

/* Inputs gerais dos modais */
.swal2-html-container input[type="text"],
.swal2-html-container input[type="email"],
.swal2-html-container select {
  width: 100% !important;
  padding: 14px 16px !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  font-size: 15px !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  background: #ffffff !important;
  color: #1a202c !important;
  box-sizing: border-box !important;
  transition: all 0.3s ease !important;
  outline: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Hover nos inputs */
.swal2-html-container input[type="text"]:hover,
.swal2-html-container input[type="email"]:hover,
.swal2-html-container select:hover {
  border-color: #cbd5e0 !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

/* Focus nos inputs */
.swal2-html-container input[type="text"]:focus,
.swal2-html-container input[type="email"]:focus,
.swal2-html-container select:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  background: #ffffff !important;
}

/* Placeholder styling */
.swal2-html-container input[type="text"]::placeholder,
.swal2-html-container input[type="email"]::placeholder {
  color: #a0aec0 !important;
  font-size: 14px !important;
}

/* Labels dos inputs */
.swal2-html-container label {
  display: block !important;
  margin-bottom: 8px !important;
  font-weight: 600 !important;
  color: #2d3748 !important;
  font-size: 14px !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Select específico */
.swal2-html-container select {
  cursor: pointer !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
  background-position: right 12px center !important;
  background-repeat: no-repeat !important;
  background-size: 16px !important;
  padding-right: 40px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

/* Inputs em mobile */
@media (max-width: 480px) {
  .swal2-html-container input[type="text"],
  .swal2-html-container input[type="email"],
  .swal2-html-container select {
    padding: 16px 18px !important;
    font-size: 16px !important;
    border-radius: 14px !important;
  }

  .swal2-html-container label {
    font-size: 15px !important;
    margin-bottom: 10px !important;
  }
}

/* Estilos para abas do modal de tutoriais */
.tutorials-modal .tab-button {
  transition: all 0.3s ease !important;
  border: none !important;
  outline: none !important;
  font-family: inherit !important;
}

.tutorials-modal .tab-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.tutorials-modal .tab-content {
  animation: fadeIn 0.3s ease-in-out !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para marcas de TV */
.tutorials-modal .brand-item {
  border-left: 3px solid #059669 !important;
  padding-left: 8px !important;
  margin-bottom: 12px !important;
}

.tutorials-modal .compatibility-badge {
  display: inline-block !important;
  background: #10b981 !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}
