<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartV - IPTV Premium</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configuração do Tailwind para suprimir avisos de produção
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "var(--border)",
                        input: "var(--input)",
                        ring: "var(--ring)",
                        background: "var(--background)",
                        foreground: "var(--foreground)",
                        primary: {
                            DEFAULT: "var(--primary)",
                            foreground: "var(--primary-foreground)",
                        },
                        secondary: {
                            DEFAULT: "var(--secondary)",
                            foreground: "var(--secondary-foreground)",
                        },
                        destructive: {
                            DEFAULT: "var(--destructive)",
                            foreground: "var(--destructive-foreground)",
                        },
                        muted: {
                            DEFAULT: "var(--muted)",
                            foreground: "var(--muted-foreground)",
                        },
                        accent: {
                            DEFAULT: "var(--accent)",
                            foreground: "var(--accent-foreground)",
                        },
                        popover: {
                            DEFAULT: "var(--popover)",
                            foreground: "var(--popover-foreground)",
                        },
                        card: {
                            DEFAULT: "var(--card)",
                            foreground: "var(--card-foreground)",
                        },
                    },
                    borderRadius: {
                        lg: "var(--radius)",
                        md: "calc(var(--radius) - 2px)",
                        sm: "calc(var(--radius) - 4px)",
                    },
                }
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            --radius: 0.625rem;
            --background: #fafafa;
            --foreground: #1a1a1a;
            --card: #ffffff;
            --card-foreground: #1a1a1a;
            --popover: #ffffff;
            --popover-foreground: #1a1a1a;
            --primary: #2563eb;
            --primary-foreground: #ffffff;
            --secondary: #f4f4f5;
            --secondary-foreground: #1a1a1a;
            --muted: #f4f4f5;
            --muted-foreground: #71717a;
            --accent: #f4f4f5;
            --accent-foreground: #1a1a1a;
            --destructive: #dc2626;
            --border: #e4e4e7;
            --input: #e4e4e7;
            --ring: #2563eb;
        }

        .dark {
            --background: #0a0a0a;
            --foreground: #fafafa;
            --card: #1a1a1a;
            --card-foreground: #fafafa;
            --popover: #1a1a1a;
            --popover-foreground: #fafafa;
            --primary: #3b82f6;
            --primary-foreground: #ffffff;
            --secondary: #262626;
            --secondary-foreground: #fafafa;
            --muted: #262626;
            --muted-foreground: #a1a1aa;
            --accent: #404040;
            --accent-foreground: #fafafa;
            --destructive: #ef4444;
            --border: #404040;
            --input: #404040;
            --ring: #3b82f6;
        }

        body {
            background: var(--background);
            color: var(--foreground);
        }

        .bg-background { background-color: var(--background); }
        .bg-card { background-color: var(--card); }
        .bg-primary { background-color: var(--primary); }
        .bg-secondary { background-color: var(--secondary); }
        .bg-muted { background-color: var(--muted); }
        .bg-accent { background-color: var(--accent); }
        .bg-destructive { background-color: var(--destructive); }

        .text-foreground { color: var(--foreground); }
        .text-card-foreground { color: var(--card-foreground); }
        .text-primary { color: var(--primary); }
        .text-primary-foreground { color: var(--primary-foreground); }
        .text-secondary-foreground { color: var(--secondary-foreground); }
        .text-muted-foreground { color: var(--muted-foreground); }
        .text-accent-foreground { color: var(--accent-foreground); }
        .text-destructive-foreground { color: var(--destructive-foreground); }

        .border-border { border-color: var(--border); }
        .border-input { border-color: var(--input); }
        .border-ring { border-color: var(--ring); }

        .rounded-lg { border-radius: var(--radius); }
        .rounded-md { border-radius: calc(var(--radius) - 2px); }
        .rounded-sm { border-radius: calc(var(--radius) - 4px); }

        .gradient-bg {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #581c87 100%);
            position: relative;
        }

        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            pointer-events: none;
        }

        .card-hover {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .dark .card-hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }

        .dark .card-hover:hover {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        .pulse-animation {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Additional styles for better appearance */
        .btn-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--foreground);
            border: 2px solid var(--border);
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background-color: var(--accent);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .grid-cols-1 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }

            .md\\:grid-cols-3 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }
        }

        @media (min-width: 768px) {
            .md\\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }
    </style>
</head>
<body class="bg-background text-foreground min-h-screen">
    <!-- Navigation -->
    <nav class="bg-card border-b border-border">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i data-lucide="tv" class="w-8 h-8 text-primary"></i>
                    <span class="text-2xl font-bold text-primary">SmartV</span>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#home" class="text-muted-foreground hover:text-foreground transition-colors">Início</a>
                    <a href="#plans" class="text-muted-foreground hover:text-foreground transition-colors">Planos</a>
                    <a href="#test" class="text-muted-foreground hover:text-foreground transition-colors">Teste Grátis</a>
                    <a href="#contact" class="text-muted-foreground hover:text-foreground transition-colors">Contato</a>
                </div>
                <button id="theme-toggle" class="p-2 rounded-md hover:bg-accent">
                    <i data-lucide="sun" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                IPTV Premium
                <span class="block text-yellow-300">SmartV</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Acesso completo a milhares de canais, filmes e séries em alta qualidade. 
                Experimente agora com nosso teste gratuito!
            </p>
            <div class="flex flex-col md:flex-row gap-4 justify-center">
                <button onclick="scrollToSection('test')" class="bg-yellow-400 hover:bg-yellow-500 text-black px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i data-lucide="play-circle" class="w-5 h-5 inline mr-2"></i>
                    Teste Grátis 4h
                </button>
                <button onclick="scrollToSection('plans')" class="bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                    <i data-lucide="credit-card" class="w-5 h-5 inline mr-2"></i>
                    Ver Planos
                </button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-muted">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12">Por que escolher o SmartV?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-card p-8 rounded-lg card-hover border border-border">
                    <div class="text-primary mb-4">
                        <i data-lucide="zap" class="w-12 h-12"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Ultra Rápido</h3>
                    <p class="text-muted-foreground">Streaming em alta velocidade sem travamentos ou buffering.</p>
                </div>
                <div class="bg-card p-8 rounded-lg card-hover border border-border">
                    <div class="text-primary mb-4">
                        <i data-lucide="shield-check" class="w-12 h-12"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">100% Seguro</h3>
                    <p class="text-muted-foreground">Conexão segura e criptografada para sua proteção.</p>
                </div>
                <div class="bg-card p-8 rounded-lg card-hover border border-border">
                    <div class="text-primary mb-4">
                        <i data-lucide="headphones" class="w-12 h-12"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Suporte 24/7</h3>
                    <p class="text-muted-foreground">Atendimento especializado sempre disponível para você.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Section -->
    <section id="test" class="py-20">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <h2 class="text-4xl font-bold text-center mb-8">Teste Grátis por 4 Horas</h2>
                <p class="text-center text-muted-foreground mb-8">
                    Experimente nossa qualidade premium sem compromisso. Receba acesso instantâneo!
                </p>
                
                <div class="bg-card p-8 rounded-lg border border-border">
                    <form id="test-form" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">Nome Completo</label>
                            <input type="text" id="test-name" required 
                                   class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">E-mail</label>
                            <input type="email" id="test-email" required 
                                   class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Plano de Interesse</label>
                            <select id="test-plan" required 
                                    class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring">
                                <option value="">Selecione um plano</option>
                                <option value="basico">Básico - R$ 19,90</option>
                                <option value="premium">Premium - R$ 29,90</option>
                                <option value="ultra">Ultra - R$ 39,90</option>
                            </select>
                        </div>
                        <button type="submit" id="test-submit" 
                                class="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-md font-semibold transition-colors">
                            <i data-lucide="play-circle" class="w-5 h-5 inline mr-2"></i>
                            Gerar Teste Grátis
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Plans Section -->
    <section id="plans" class="py-20 bg-muted">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12">Escolha seu Plano</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Plano Básico -->
                <div class="bg-card p-8 rounded-lg border border-border card-hover">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Básico</h3>
                        <div class="text-4xl font-bold text-primary mb-2">R$ 19,90</div>
                        <p class="text-muted-foreground mb-6">/mês</p>
                        <ul class="space-y-3 mb-8 text-left">
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Mais de 5.000 canais
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Filmes e séries
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Qualidade HD
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                1 conexão simultânea
                            </li>
                        </ul>
                        <button onclick="selectPlan('basico', 19.90)"
                                class="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-md font-semibold transition-colors">
                            Escolher Plano
                        </button>
                    </div>
                </div>

                <!-- Plano Premium -->
                <div class="bg-card p-8 rounded-lg border-2 border-primary card-hover relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-semibold">
                            Mais Popular
                        </span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Premium</h3>
                        <div class="text-4xl font-bold text-primary mb-2">R$ 29,90</div>
                        <p class="text-muted-foreground mb-6">/mês</p>
                        <ul class="space-y-3 mb-8 text-left">
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Mais de 10.000 canais
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Filmes e séries premium
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Qualidade Full HD
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                2 conexões simultâneas
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Suporte prioritário
                            </li>
                        </ul>
                        <button onclick="selectPlan('premium', 29.90)"
                                class="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-md font-semibold transition-colors">
                            Escolher Plano
                        </button>
                    </div>
                </div>

                <!-- Plano Ultra -->
                <div class="bg-card p-8 rounded-lg border border-border card-hover">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Ultra</h3>
                        <div class="text-4xl font-bold text-primary mb-2">R$ 39,90</div>
                        <p class="text-muted-foreground mb-6">/mês</p>
                        <ul class="space-y-3 mb-8 text-left">
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Mais de 15.000 canais
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Filmes e séries 4K
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Qualidade 4K Ultra HD
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                3 conexões simultâneas
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Suporte VIP 24/7
                            </li>
                            <li class="flex items-center">
                                <i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>
                                Canais exclusivos
                            </li>
                        </ul>
                        <button onclick="selectPlan('ultra', 39.90)"
                                class="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-md font-semibold transition-colors">
                            Escolher Plano
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-4xl font-bold text-center mb-12">Entre em Contato</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                    <div>
                        <h3 class="text-2xl font-semibold mb-6">Fale Conosco</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <i data-lucide="message-circle" class="w-6 h-6 text-primary mr-4"></i>
                                <div>
                                    <p class="font-semibold">WhatsApp</p>
                                    <p class="text-muted-foreground">+55 (11) 99999-9999</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <i data-lucide="mail" class="w-6 h-6 text-primary mr-4"></i>
                                <div>
                                    <p class="font-semibold">E-mail</p>
                                    <p class="text-muted-foreground"><EMAIL></p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <i data-lucide="clock" class="w-6 h-6 text-primary mr-4"></i>
                                <div>
                                    <p class="font-semibold">Horário de Atendimento</p>
                                    <p class="text-muted-foreground">24 horas por dia, 7 dias por semana</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-card p-8 rounded-lg border border-border">
                        <form id="contact-form" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium mb-2">Nome</label>
                                <input type="text" required
                                       class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">E-mail</label>
                                <input type="email" required
                                       class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Mensagem</label>
                                <textarea rows="4" required
                                          class="w-full px-4 py-3 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"></textarea>
                            </div>
                            <button type="submit"
                                    class="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-md font-semibold transition-colors">
                                Enviar Mensagem
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-card border-t border-border py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <i data-lucide="tv" class="w-8 h-8 text-primary"></i>
                        <span class="text-2xl font-bold text-primary">SmartV</span>
                    </div>
                    <p class="text-muted-foreground">
                        A melhor experiência em IPTV premium com qualidade superior e suporte especializado.
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Serviços</h4>
                    <ul class="space-y-2 text-muted-foreground">
                        <li><a href="#" class="hover:text-foreground transition-colors">IPTV Premium</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Teste Grátis</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Suporte Técnico</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Aplicativos</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Suporte</h4>
                    <ul class="space-y-2 text-muted-foreground">
                        <li><a href="#" class="hover:text-foreground transition-colors">Central de Ajuda</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">WhatsApp</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Tutoriais</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-muted-foreground">
                        <li><a href="#" class="hover:text-foreground transition-colors">Termos de Uso</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Política de Privacidade</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Reembolso</a></li>
                        <li><a href="#" class="hover:text-foreground transition-colors">Contato</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
                <p>&copy; 2024 SmartV. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;

        // Check for saved theme preference or default to light mode
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            body.classList.add('dark');
            themeToggle.innerHTML = '<i data-lucide="moon" class="w-5 h-5"></i>';
        }

        themeToggle.addEventListener('click', () => {
            body.classList.toggle('dark');
            const isDark = body.classList.contains('dark');

            // Update icon
            themeToggle.innerHTML = isDark
                ? '<i data-lucide="moon" class="w-5 h-5"></i>'
                : '<i data-lucide="sun" class="w-5 h-5"></i>';

            // Save preference
            localStorage.setItem('theme', isDark ? 'dark' : 'light');

            // Reinitialize icons
            lucide.createIcons();
        });

        // Smooth scrolling function
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Test form submission
        document.getElementById('test-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = document.getElementById('test-submit');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<div class="loading-spinner inline-block mr-2"></div> Gerando teste...';
            submitBtn.disabled = true;

            const formData = {
                name: document.getElementById('test-name').value,
                email: document.getElementById('test-email').value,
                plan: document.getElementById('test-plan').value
            };

            try {
                const response = await fetch('api/test.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    // Show success with test details
                    let testDetails = `
                        <div class="text-left space-y-4">
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                                <h3 class="font-bold text-green-800 dark:text-green-200 mb-2">✅ Credenciais de Acesso</h3>
                                <p><strong>Usuário:</strong> ${result.credentials.username}</p>
                                <p><strong>Senha:</strong> ${result.credentials.password}</p>
                            </div>
                    `;

                    if (result.accessDetails) {
                        const details = result.accessDetails;

                        if (details.dnsStb) {
                            testDetails += `
                                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                    <h3 class="font-bold text-blue-800 dark:text-blue-200 mb-2">📺 DNS STB</h3>
                                    <p>${details.dnsStb}</p>
                                </div>
                            `;
                        }

                        if (details.linkM3u) {
                            testDetails += `
                                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                                    <h3 class="font-bold text-purple-800 dark:text-purple-200 mb-2">🟢 Link M3U</h3>
                                    <p class="break-all text-sm">${details.linkM3u}</p>
                                </div>
                            `;
                        }

                        if (details.linkHls) {
                            testDetails += `
                                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                                    <h3 class="font-bold text-yellow-800 dark:text-yellow-200 mb-2">🟡 Link HLS</h3>
                                    <p class="break-all text-sm">${details.linkHls}</p>
                                </div>
                            `;
                        }

                        if (details.webPlayers && details.webPlayers.length > 0) {
                            testDetails += `
                                <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                                    <h3 class="font-bold text-indigo-800 dark:text-indigo-200 mb-2">📺 Web Players</h3>
                                    ${details.webPlayers.map(player => `<p class="break-all text-sm">${player}</p>`).join('')}
                                </div>
                            `;
                        }

                        if (details.expiresAt) {
                            testDetails += `
                                <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                                    <h3 class="font-bold text-red-800 dark:text-red-200 mb-2">🗓️ Vencimento</h3>
                                    <p>${details.expiresAt}</p>
                                </div>
                            `;
                        }
                    }

                    testDetails += '</div>';

                    Swal.fire({
                        title: 'Teste Liberado!',
                        html: testDetails,
                        icon: 'success',
                        confirmButtonText: 'Entendi',
                        width: '600px',
                        customClass: {
                            popup: 'text-left'
                        }
                    });

                    // Reset form
                    document.getElementById('test-form').reset();
                } else {
                    throw new Error(result.message || 'Erro ao gerar teste');
                }
            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Erro!',
                    text: 'Não foi possível gerar o teste. Tente novamente.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            } finally {
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // Plan selection function
        function selectPlan(planType, price) {
            Swal.fire({
                title: 'Dados para Pagamento',
                html: `
                    <form id="payment-form" class="space-y-4 text-left">
                        <div>
                            <label class="block text-sm font-medium mb-2">Nome Completo</label>
                            <input type="text" id="customer-name" required
                                   class="w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">E-mail</label>
                            <input type="email" id="customer-email" required
                                   class="w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">CPF/CNPJ</label>
                            <input type="text" id="customer-doc" required
                                   class="w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="000.000.000-00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Telefone</label>
                            <input type="tel" id="customer-phone" required
                                   class="w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="(11) 99999-9999">
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg">
                            <p><strong>Plano:</strong> ${planType.charAt(0).toUpperCase() + planType.slice(1)}</p>
                            <p><strong>Valor:</strong> R$ ${price.toFixed(2)}</p>
                        </div>
                    </form>
                `,
                showCancelButton: true,
                confirmButtonText: 'Gerar PIX',
                cancelButtonText: 'Cancelar',
                width: '500px',
                preConfirm: function() {
                    const nameField = document.getElementById('customer-name');
                    const emailField = document.getElementById('customer-email');
                    const docField = document.getElementById('customer-doc');
                    const phoneField = document.getElementById('customer-phone');

                    const name = nameField ? nameField.value.trim() : '';
                    const email = emailField ? emailField.value.trim() : '';
                    const doc = docField ? docField.value.trim() : '';
                    const phone = phoneField ? phoneField.value.trim() : '';

                    if (!name || !email || !doc || !phone) {
                        Swal.showValidationMessage('Preencha todos os campos');
                        return false;
                    }

                    return { name: name, email: email, document: doc, phone: phone };
                }
            }).then(async (result) => {
                if (result.isConfirmed) {
                    await generatePix({
                        customerName: result.value.name,
                        customerEmail: result.value.email,
                        customerDocument: result.value.document,
                        customerPhone: result.value.phone,
                        planType: planType,
                        amount: price
                    });
                }
            });
        }

        // Generate PIX payment
        async function generatePix(paymentData) {
            try {
                Swal.fire({
                    title: 'Gerando PIX...',
                    text: 'Aguarde enquanto processamos seu pagamento',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                const response = await fetch('api/payment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paymentData)
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire({
                        title: 'PIX Gerado!',
                        html: `
                            <div class="text-center space-y-4">
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h3 class="font-bold text-green-800 mb-2">Pagamento PIX</h3>
                                    <p class="text-sm text-gray-600 mb-4">Escaneie o QR Code ou copie o código PIX</p>
                                    <img src="${result.qrCodeUrl}" alt="QR Code PIX" class="mx-auto mb-4" style="max-width: 200px;">
                                    <div class="bg-gray-100 p-3 rounded text-xs break-all">
                                        ${result.qrCode}
                                    </div>
                                    <button onclick="navigator.clipboard.writeText('${result.qrCode}')"
                                            class="mt-2 bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600">
                                        Copiar Código PIX
                                    </button>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <p><strong>Valor:</strong> R$ ${result.amount.toFixed(2)}</p>
                                    <p><strong>Vencimento:</strong> ${new Date(result.expiresAt).toLocaleString('pt-BR')}</p>
                                </div>
                            </div>
                        `,
                        icon: 'success',
                        confirmButtonText: 'Entendi',
                        width: '500px'
                    });
                } else {
                    throw new Error(result.message || 'Erro ao gerar PIX');
                }
            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Erro!',
                    text: 'Não foi possível gerar o PIX. Tente novamente.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }

        // Contact form submission
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            Swal.fire({
                title: 'Mensagem Enviada!',
                text: 'Entraremos em contato em breve.',
                icon: 'success',
                confirmButtonText: 'OK'
            });
            e.target.reset();
        });

        // Initialize icons after DOM load
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();

            // Suprimir aviso do Tailwind CDN no console
            const originalWarn = console.warn;
            console.warn = function(...args) {
                if (args[0] && args[0].includes && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
                    return;
                }
                originalWarn.apply(console, args);
            };
        });
    </script>
</body>
</html>
