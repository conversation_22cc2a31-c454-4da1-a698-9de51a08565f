{"version": 3, "file": "validators.mjs", "sources": ["../../src/lib/validators.ts"], "sourcesContent": ["const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\n// Shadow always begins with x and y offset separated by underscore\nconst shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\n\nexport function isLength(value: string) {\n    return (\n        isNumber(value) ||\n        stringLengths.has(value) ||\n        fractionRegex.test(value) ||\n        isArbitraryLength(value)\n    )\n}\n\nexport function isArbitraryLength(value: string) {\n    return getIsArbitraryValue(value, 'length', isLengthOnly)\n}\n\nexport function isArbitrarySize(value: string) {\n    return getIsArbitraryValue(value, 'size', isNever)\n}\n\nexport function isArbitraryPosition(value: string) {\n    return getIsArbitraryValue(value, 'position', isNever)\n}\n\nexport function isArbitraryUrl(value: string) {\n    return getIsArbitraryValue(value, 'url', isUrl)\n}\n\nexport function isArbitraryNumber(value: string) {\n    return getIsArbitraryValue(value, 'number', isNumber)\n}\n\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nexport const isArbitraryWeight = isArbitraryNumber\n\nexport function isNumber(value: string) {\n    return !Number.isNaN(Number(value))\n}\n\nexport function isPercent(value: string) {\n    return value.endsWith('%') && isNumber(value.slice(0, -1))\n}\n\nexport function isInteger(value: string) {\n    return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly)\n}\n\nexport function isArbitraryValue(value: string) {\n    return arbitraryValueRegex.test(value)\n}\n\nexport function isAny() {\n    return true\n}\n\nexport function isTshirtSize(value: string) {\n    return tshirtUnitRegex.test(value)\n}\n\nexport function isArbitraryShadow(value: string) {\n    return getIsArbitraryValue(value, '', isShadow)\n}\n\nfunction getIsArbitraryValue(value: string, label: string, testValue: (value: string) => boolean) {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return result[1] === label\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nfunction isLengthOnly(value: string) {\n    return lengthUnitRegex.test(value)\n}\n\nfunction isNever() {\n    return false\n}\n\nfunction isUrl(value: string) {\n    return value.startsWith('url(')\n}\n\nfunction isIntegerOnly(value: string) {\n    return Number.isInteger(Number(value))\n}\n\nfunction isShadow(value: string) {\n    return shadowRegex.test(value)\n}\n"], "names": ["arbitraryValueRegex", "fractionRegex", "stringLengths", "Set", "tshirtUnitRegex", "lengthUnitRegex", "shadowRegex", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNumber", "has", "test", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isArbitrarySize", "isNever", "isArbitraryPosition", "isArbitraryUrl", "isUrl", "isArbitraryNumber", "isArbitraryWeight", "Number", "isNaN", "isPercent", "endsWith", "slice", "isInteger", "isIntegerOnly", "isArbitraryValue", "isAny", "isTshirtSize", "isArbitraryShadow", "is<PERSON><PERSON>ow", "label", "testValue", "result", "exec", "startsWith"], "mappings": "AAAA,IAAMA,mBAAmB,GAAG,4BAA4B,CAAA;AACxD,IAAMC,aAAa,GAAG,YAAY,CAAA;AAClC,IAAMC,aAAa,gBAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;AACvD,IAAMC,eAAe,GAAG,kCAAkC,CAAA;AAC1D,IAAMC,eAAe,GACjB,2HAA2H,CAAA;AAC/H;AACA,IAAMC,WAAW,GAAG,wDAAwD,CAAA;AAEtE,SAAUC,QAAQ,CAACC,KAAa,EAAA;EAClC,OACIC,QAAQ,CAACD,KAAK,CAAC,IACfN,aAAa,CAACQ,GAAG,CAACF,KAAK,CAAC,IACxBP,aAAa,CAACU,IAAI,CAACH,KAAK,CAAC,IACzBI,iBAAiB,CAACJ,KAAK,CAAC,CAAA;AAEhC,CAAA;AAEM,SAAUI,iBAAiB,CAACJ,KAAa,EAAA;AAC3C,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEM,YAAY,CAAC,CAAA;AAC7D,CAAA;AAEM,SAAUC,eAAe,CAACP,KAAa,EAAA;AACzC,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,MAAM,EAAEQ,OAAO,CAAC,CAAA;AACtD,CAAA;AAEM,SAAUC,mBAAmB,CAACT,KAAa,EAAA;AAC7C,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,UAAU,EAAEQ,OAAO,CAAC,CAAA;AAC1D,CAAA;AAEM,SAAUE,cAAc,CAACV,KAAa,EAAA;AACxC,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,KAAK,EAAEW,KAAK,CAAC,CAAA;AACnD,CAAA;AAEM,SAAUC,iBAAiB,CAACZ,KAAa,EAAA;AAC3C,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEC,QAAQ,CAAC,CAAA;AACzD,CAAA;AAEA;;AAEG;AACI,IAAMY,iBAAiB,GAAGD,kBAAiB;AAE5C,SAAUX,QAAQ,CAACD,KAAa,EAAA;EAClC,OAAO,CAACc,MAAM,CAACC,KAAK,CAACD,MAAM,CAACd,KAAK,CAAC,CAAC,CAAA;AACvC,CAAA;AAEM,SAAUgB,SAAS,CAAChB,KAAa,EAAA;AACnC,EAAA,OAAOA,KAAK,CAACiB,QAAQ,CAAC,GAAG,CAAC,IAAIhB,QAAQ,CAACD,KAAK,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9D,CAAA;AAEM,SAAUC,SAAS,CAACnB,KAAa,EAAA;AACnC,EAAA,OAAOoB,aAAa,CAACpB,KAAK,CAAC,IAAIK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEoB,aAAa,CAAC,CAAA;AACtF,CAAA;AAEM,SAAUC,gBAAgB,CAACrB,KAAa,EAAA;AAC1C,EAAA,OAAOR,mBAAmB,CAACW,IAAI,CAACH,KAAK,CAAC,CAAA;AAC1C,CAAA;SAEgBsB,KAAK,GAAA;AACjB,EAAA,OAAO,IAAI,CAAA;AACf,CAAA;AAEM,SAAUC,YAAY,CAACvB,KAAa,EAAA;AACtC,EAAA,OAAOJ,eAAe,CAACO,IAAI,CAACH,KAAK,CAAC,CAAA;AACtC,CAAA;AAEM,SAAUwB,iBAAiB,CAACxB,KAAa,EAAA;AAC3C,EAAA,OAAOK,mBAAmB,CAACL,KAAK,EAAE,EAAE,EAAEyB,QAAQ,CAAC,CAAA;AACnD,CAAA;AAEA,SAASpB,mBAAmB,CAACL,KAAa,EAAE0B,KAAa,EAAEC,SAAqC,EAAA;AAC5F,EAAA,IAAMC,MAAM,GAAGpC,mBAAmB,CAACqC,IAAI,CAAC7B,KAAK,CAAC,CAAA;AAE9C,EAAA,IAAI4B,MAAM,EAAE;AACR,IAAA,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;AACX,MAAA,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAKF,KAAK,CAAA;AAC7B,KAAA;AAED,IAAA,OAAOC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAA;AAC/B,GAAA;AAED,EAAA,OAAO,KAAK,CAAA;AAChB,CAAA;AAEA,SAAStB,YAAY,CAACN,KAAa,EAAA;AAC/B,EAAA,OAAOH,eAAe,CAACM,IAAI,CAACH,KAAK,CAAC,CAAA;AACtC,CAAA;AAEA,SAASQ,OAAO,GAAA;AACZ,EAAA,OAAO,KAAK,CAAA;AAChB,CAAA;AAEA,SAASG,KAAK,CAACX,KAAa,EAAA;AACxB,EAAA,OAAOA,KAAK,CAAC8B,UAAU,CAAC,MAAM,CAAC,CAAA;AACnC,CAAA;AAEA,SAASV,aAAa,CAACpB,KAAa,EAAA;EAChC,OAAOc,MAAM,CAACK,SAAS,CAACL,MAAM,CAACd,KAAK,CAAC,CAAC,CAAA;AAC1C,CAAA;AAEA,SAASyB,QAAQ,CAACzB,KAAa,EAAA;AAC3B,EAAA,OAAOF,WAAW,CAACK,IAAI,CAACH,KAAK,CAAC,CAAA;AAClC;;;;"}