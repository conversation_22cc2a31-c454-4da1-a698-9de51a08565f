# 🚀 Deploy SmartV.shop - <PERSON><PERSON><PERSON>

## 📋 Pré-requisitos no Servidor

1. **Node.js** (versão 18 ou superior)
2. **NPM** ou **Yarn**
3. **PM2** (para gerenciamento de processos)
4. **Nginx** (já configurado conforme fornecido)

## 📁 Estrutura de Diretórios no Servidor

```
/www/wwwroot/smarttv/
├── api/
│   ├── simple-server.cjs
│   └── package.json
├── dist/                 # Build do frontend
├── proxy-server.cjs
├── package.json
├── ecosystem.config.js
└── logs/
```

## 🔧 Passos para Deploy

### 1. Preparar o Projeto Localmente

```bash
# Build do projeto
npm run build

# Testar localmente
npm run preview
```

### 2. Upload para o Servidor

Faça upload dos seguintes arquivos/pastas para `/www/wwwroot/smarttv/`:

- `dist/` (pasta completa do build)
- `api/` (pasta completa da API)
- `proxy-server.cjs`
- `package.json`
- `ecosystem.config.js`
- `node_modules/` (ou rode npm install no servidor)

### 3. Configurar no Servidor

```bash
# Navegar para o diretório
cd /www/wwwroot/smarttv/

# Instalar dependências (se não fez upload do node_modules)
npm install

# Instalar PM2 globalmente (se não tiver)
npm install -g pm2

# Criar diretório de logs
mkdir -p logs

# Dar permissões aos scripts
chmod +x start-production.sh
chmod +x stop-production.sh
```

### 4. Iniciar os Serviços

```bash
# Opção 1: Usando PM2 (Recomendado)
pm2 start ecosystem.config.js

# Opção 2: Usando scripts bash
./start-production.sh

# Verificar status
pm2 status
```

### 5. Configurar Auto-start

```bash
# Salvar configuração atual do PM2
pm2 save

# Configurar para iniciar automaticamente no boot
pm2 startup
```

## 🌐 Configuração Nginx

O arquivo de configuração Nginx fornecido já está correto:

- **Frontend**: Proxy para porta 5173
- **SSL**: Configurado para smartv.shop
- **Logs**: `/www/wwwlogs/ssmartv.log`

## 📊 Monitoramento

### Verificar Status dos Serviços

```bash
# Status do PM2
pm2 status

# Logs em tempo real
pm2 logs

# Logs específicos
pm2 logs smartv-backend
pm2 logs smartv-proxy

# Monitoramento
pm2 monit
```

### Verificar Portas

```bash
# Verificar se as portas estão sendo usadas
netstat -tlnp | grep :4000  # Backend
netstat -tlnp | grep :3001  # Proxy
netstat -tlnp | grep :5173  # Frontend
```

## 🔄 Comandos Úteis

### Reiniciar Serviços

```bash
# Reiniciar todos
pm2 restart all

# Reiniciar específico
pm2 restart smartv-backend
pm2 restart smartv-proxy
```

### Parar Serviços

```bash
# Parar todos
pm2 stop all

# Parar específico
pm2 stop smartv-backend

# Remover do PM2
pm2 delete all
```

### Atualizar Aplicação

```bash
# 1. Fazer novo build localmente
npm run build

# 2. Upload da pasta dist/

# 3. Reiniciar apenas se necessário
pm2 restart smartv-backend  # Se mudou API
# Frontend não precisa restart, apenas novo build
```

## 🐛 Troubleshooting

### Problema: Porta em uso

```bash
# Verificar processo usando a porta
lsof -i :4000
lsof -i :3001
lsof -i :5173

# Matar processo específico
kill -9 <PID>
```

### Problema: Permissões

```bash
# Dar permissões corretas
chown -R www:www /www/wwwroot/smarttv/
chmod -R 755 /www/wwwroot/smarttv/
```

### Problema: API não responde

```bash
# Verificar logs
pm2 logs smartv-backend

# Testar API diretamente
curl http://localhost:4000/health
curl http://localhost:3001/health
```

## 📱 URLs de Acesso

- **Produção**: https://smartv.shop
- **API Backend**: http://localhost:4000
- **Proxy**: http://localhost:3001
- **Health Check**: http://localhost:4000/health

## 🔐 Variáveis de Ambiente

As seguintes variáveis podem ser configuradas:

- `NODE_ENV=production`
- `PORT=4000` (backend)
- `PROXY_PORT=3001`
- `FRONTEND_PORT=5173`

## 📝 Logs

Todos os logs são salvos em:

- **Backend**: `/www/wwwlogs/smartv-backend.log`
- **Proxy**: `/www/wwwlogs/smartv-proxy.log`
- **Nginx**: `/www/wwwlogs/ssmartv.log`
