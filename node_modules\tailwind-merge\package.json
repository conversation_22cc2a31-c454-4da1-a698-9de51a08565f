{"name": "tailwind-merge", "version": "1.14.0", "description": "Merge Tailwind CSS classes without style conflicts", "keywords": ["tailwindcss", "tailwind", "css", "classes", "className", "classList", "merge", "conflict", "override"], "homepage": "https://github.com/dcastil/tailwind-merge", "bugs": {"url": "https://github.com/dcastil/tailwind-merge/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}, "license": "MIT", "author": "<PERSON><PERSON>", "files": ["dist", "src"], "source": "src/index.ts", "exports": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/tailwind-merge.mjs", "default": "./dist/tailwind-merge.mjs"}, "module": "dist/tailwind-merge.mjs", "main": "dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/dcastil/tailwind-merge.git"}, "sideEffects": false, "scripts": {"build": "dts build", "test": "dts test", "test:watch": "dts test --watch", "test:exports": "node scripts/test-built-package-exports.cjs && node scripts/test-built-package-exports.mjs", "lint": "eslint --max-warnings 0 '**'", "size": "size-limit", "preversion": "if [ -n \"$DANYS_MACHINE\" ]; then git checkout main && git pull; fi", "version": "zx scripts/update-readme.mjs", "postversion": "if [ -n \"$DANYS_MACHINE\" ]; then git push --follow-tags && open https://github.com/dcastil/tailwind-merge/releases; fi"}, "devDependencies": {"@size-limit/preset-small-lib": "^8.2.6", "@types/jest": "^29.5.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "dts-cli": "^2.0.3", "eslint": "^8.44.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "globby": "^11.1.0", "prettier": "^2.8.8", "size-limit": "^8.2.6", "ts-jest": "^29.1.1", "typescript": "^5.1.6", "zx": "^7.2.2"}, "publishConfig": {"provenance": true}}