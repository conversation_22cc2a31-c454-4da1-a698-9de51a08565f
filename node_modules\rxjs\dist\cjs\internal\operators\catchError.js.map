{"version": 3, "file": "catchError.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/catchError.ts"], "names": [], "mappings": ";;;AAIA,qDAAoD;AACpD,2DAAgE;AAChE,qCAAuC;AAkGvC,SAAgB,UAAU,CACxB,QAAgD;IAEhD,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,QAAQ,GAAwB,IAAI,CAAC;QACzC,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,aAA6C,CAAC;QAElD,QAAQ,GAAG,MAAM,CAAC,SAAS,CACzB,6CAAwB,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAC,GAAG;YAC7D,aAAa,GAAG,qBAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACvE,IAAI,QAAQ,EAAE;gBACZ,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACvB,QAAQ,GAAG,IAAI,CAAC;gBAChB,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACrC;iBAAM;gBAGL,SAAS,GAAG,IAAI,CAAC;aAClB;QACH,CAAC,CAAC,CACH,CAAC;QAEF,IAAI,SAAS,EAAE;YAMb,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,QAAQ,GAAG,IAAI,CAAC;YAChB,aAAc,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACtC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAlCD,gCAkCC"}