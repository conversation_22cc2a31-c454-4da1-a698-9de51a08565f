{"version": 3, "file": "tailwind-merge.cjs.development.js", "sources": ["../src/lib/tw-join.ts", "../src/lib/class-utils.ts", "../src/lib/lru-cache.ts", "../src/lib/modifier-utils.ts", "../src/lib/config-utils.ts", "../src/lib/merge-classlist.ts", "../src/lib/create-tailwind-merge.ts", "../src/lib/from-theme.ts", "../src/lib/validators.ts", "../src/lib/default-config.ts", "../src/lib/merge-configs.ts", "../src/lib/extend-tailwind-merge.ts", "../src/lib/tw-merge.ts", "../src/index.ts"], "sourcesContent": ["/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nfunction toValue(mix: ClassNameArray | string) {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { ClassGroup, ClassGroupId, ClassValidator, Config, ThemeGetter, ThemeObject } from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: ClassGroupId\n}\n\ninterface ClassValidatorObject {\n    classGroupId: ClassGroupId\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport function createClassUtils(config: Config) {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers = {} } = config\n\n    function getClassGroupId(className: string) {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    function getConflictingClassGroupIds(classGroupId: ClassGroupId, hasPostfixModifier: boolean) {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nfunction getGroupRecursive(\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): ClassGroupId | undefined {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nfunction getGroupIdForArbitraryProperty(className: string) {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport function createClassMap(config: Config) {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nfunction processClassesRecursively(\n    classGroup: ClassGroup,\n    classPartObject: ClassPartObject,\n    classGroupId: ClassGroupId,\n    theme: ThemeObject,\n) {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nfunction getPart(classPartObject: ClassPartObject, path: string) {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nfunction isThemeGetter(func: ClassValidator | ThemeGetter): func is ThemeGetter {\n    return (func as ThemeGetter).isThemeGetter\n}\n\nfunction getPrefixedClassGroupEntries(\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup]> {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport function createLruCache<Key, Value>(maxCacheSize: number): LruCache<Key, Value> {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    function update(key: Key, value: Value) {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { Config } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport function createSplitModifiers(config: Config) {\n    const separator = config.separator || ':'\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className: string) {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport function sortModifiers(modifiers: string[]) {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n", "import { createClassUtils } from './class-utils'\nimport { createLruCache } from './lru-cache'\nimport { createSplitModifiers } from './modifier-utils'\nimport { Config } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createConfigUtils(config: Config) {\n    return {\n        cache: createLruCache<string, string>(config.cacheSize),\n        splitModifiers: createSplitModifiers(config),\n        ...createClassUtils(config),\n    }\n}\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './modifier-utils'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport function mergeClassList(classList: string, configUtils: ConfigUtils) {\n    const { splitModifiers, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict = new Set<string>()\n\n    return (\n        classList\n            .trim()\n            .split(SPLIT_CLASSES_REGEX)\n            .map((originalClassName) => {\n                const {\n                    modifiers,\n                    hasImportantModifier,\n                    baseClassName,\n                    maybePostfixModifierPosition,\n                } = splitModifiers(originalClassName)\n\n                let classGroupId = getClassGroupId(\n                    maybePostfixModifierPosition\n                        ? baseClassName.substring(0, maybePostfixModifierPosition)\n                        : baseClassName,\n                )\n\n                let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n\n                if (!classGroupId) {\n                    if (!maybePostfixModifierPosition) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    classGroupId = getClassGroupId(baseClassName)\n\n                    if (!classGroupId) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    hasPostfixModifier = false\n                }\n\n                const variantModifier = sortModifiers(modifiers).join(':')\n\n                const modifierId = hasImportantModifier\n                    ? variantModifier + IMPORTANT_MODIFIER\n                    : variantModifier\n\n                return {\n                    isTailwindClass: true as const,\n                    modifierId,\n                    classGroupId,\n                    originalClassName,\n                    hasPostfixModifier,\n                }\n            })\n            .reverse()\n            // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n            .filter((parsed) => {\n                if (!parsed.isTailwindClass) {\n                    return true\n                }\n\n                const { modifierId, classGroupId, hasPostfixModifier } = parsed\n\n                const classId = modifierId + classGroupId\n\n                if (classGroupsInConflict.has(classId)) {\n                    return false\n                }\n\n                classGroupsInConflict.add(classId)\n\n                getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach((group) =>\n                    classGroupsInConflict.add(modifierId + group),\n                )\n\n                return true\n            })\n            .reverse()\n            .map((parsed) => parsed.originalClassName)\n            .join(' ')\n    )\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { Config } from './types'\n\ntype CreateConfigFirst = () => Config\ntype CreateConfigSubsequent = (config: Config) => Config\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    ...createConfig: [CreateConfigFirst, ...CreateConfigSubsequent[]]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const [firstCreateConfig, ...restCreateConfig] = createConfig\n\n        const config = restCreateConfig.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            firstCreateConfig(),\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { ThemeGetter, ThemeObject } from './types'\n\nexport function fromTheme(key: string): ThemeGetter {\n    const themeGetter = (theme: ThemeObject) => theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\n// Shadow always begins with x and y offset separated by underscore\nconst shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\n\nexport function isLength(value: string) {\n    return (\n        isNumber(value) ||\n        stringLengths.has(value) ||\n        fractionRegex.test(value) ||\n        isArbitraryLength(value)\n    )\n}\n\nexport function isArbitraryLength(value: string) {\n    return getIsArbitraryValue(value, 'length', isLengthOnly)\n}\n\nexport function isArbitrarySize(value: string) {\n    return getIsArbitraryValue(value, 'size', isNever)\n}\n\nexport function isArbitraryPosition(value: string) {\n    return getIsArbitraryValue(value, 'position', isNever)\n}\n\nexport function isArbitraryUrl(value: string) {\n    return getIsArbitraryValue(value, 'url', isUrl)\n}\n\nexport function isArbitraryNumber(value: string) {\n    return getIsArbitraryValue(value, 'number', isNumber)\n}\n\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nexport const isArbitraryWeight = isArbitraryNumber\n\nexport function isNumber(value: string) {\n    return !Number.isNaN(Number(value))\n}\n\nexport function isPercent(value: string) {\n    return value.endsWith('%') && isNumber(value.slice(0, -1))\n}\n\nexport function isInteger(value: string) {\n    return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly)\n}\n\nexport function isArbitraryValue(value: string) {\n    return arbitraryValueRegex.test(value)\n}\n\nexport function isAny() {\n    return true\n}\n\nexport function isTshirtSize(value: string) {\n    return tshirtUnitRegex.test(value)\n}\n\nexport function isArbitraryShadow(value: string) {\n    return getIsArbitraryValue(value, '', isShadow)\n}\n\nfunction getIsArbitraryValue(value: string, label: string, testValue: (value: string) => boolean) {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return result[1] === label\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nfunction isLengthOnly(value: string) {\n    return lengthUnitRegex.test(value)\n}\n\nfunction isNever() {\n    return false\n}\n\nfunction isUrl(value: string) {\n    return value.startsWith('url(')\n}\n\nfunction isIntegerOnly(value: string) {\n    return Number.isInteger(Number(value))\n}\n\nfunction isShadow(value: string) {\n    return shadowRegex.test(value)\n}\n", "import { fromTheme } from './from-theme'\nimport { Config } from './types'\nimport {\n    isAny,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryUrl,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport function getDefaultConfig() {\n    const colors = fromTheme('colors')\n    const spacing = fromTheme('spacing')\n    const blur = fromTheme('blur')\n    const brightness = fromTheme('brightness')\n    const borderColor = fromTheme('borderColor')\n    const borderRadius = fromTheme('borderRadius')\n    const borderSpacing = fromTheme('borderSpacing')\n    const borderWidth = fromTheme('borderWidth')\n    const contrast = fromTheme('contrast')\n    const grayscale = fromTheme('grayscale')\n    const hueRotate = fromTheme('hueRotate')\n    const invert = fromTheme('invert')\n    const gap = fromTheme('gap')\n    const gradientColorStops = fromTheme('gradientColorStops')\n    const gradientColorStopPositions = fromTheme('gradientColorStopPositions')\n    const inset = fromTheme('inset')\n    const margin = fromTheme('margin')\n    const opacity = fromTheme('opacity')\n    const padding = fromTheme('padding')\n    const saturate = fromTheme('saturate')\n    const scale = fromTheme('scale')\n    const sepia = fromTheme('sepia')\n    const skew = fromTheme('skew')\n    const space = fromTheme('space')\n    const translate = fromTheme('translate')\n\n    const getOverscroll = () => ['auto', 'contain', 'none'] as const\n    const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing] as const\n    const getSpacingWithArbitrary = () => [isArbitraryValue, spacing] as const\n    const getLengthWithEmpty = () => ['', isLength] as const\n    const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue] as const\n    const getPositions = () =>\n        [\n            'bottom',\n            'center',\n            'left',\n            'left-bottom',\n            'left-top',\n            'right',\n            'right-bottom',\n            'right-top',\n            'top',\n        ] as const\n    const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'] as const\n    const getBlendModes = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n            'plus-lighter',\n        ] as const\n    const getAlign = () =>\n        ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'] as const\n    const getZeroAndEmpty = () => ['', '0', isArbitraryValue] as const\n    const getBreaks = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const getNumber = () => [isNumber, isArbitraryNumber]\n    const getNumberAndArbitrary = () => [isNumber, isArbitraryValue]\n\n    return {\n        cacheSize: 500,\n        theme: {\n            colors: [isAny],\n            spacing: [isLength],\n            blur: ['none', '', isTshirtSize, isArbitraryValue],\n            brightness: getNumber(),\n            borderColor: [colors],\n            borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmpty(),\n            contrast: getNumber(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [colors],\n            gradientColorStopPositions: [isPercent, isArbitraryLength],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumber(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumber(),\n            scale: getNumber(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary(),\n        },\n        classGroups: {\n            // Layout\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [{ aspect: ['auto', 'square', 'video', isArbitraryValue] }],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [{ columns: [isTshirtSize] }],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': getBreaks() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': getBreaks() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: [...getPositions(), isArbitraryValue] }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: getOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': getOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': getOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: getOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': getOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': getOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: [inset] }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': [inset] }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': [inset] }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: [inset] }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: [inset] }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: [inset] }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: [inset] }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: [inset] }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: [inset] }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: ['auto', isInteger] }],\n            // Flexbox and Grid\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [{ basis: getSpacingWithAutoAndArbitrary() }],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['wrap', 'wrap-reverse', 'nowrap'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: ['1', 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: getZeroAndEmpty() }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: getZeroAndEmpty() }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [{ order: ['first', 'last', 'none', isInteger] }],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': [isAny] }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: ['auto', { span: ['full', isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': [isAny] }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: ['auto', { span: [isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: [gap] }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': [gap] }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': [gap] }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: ['normal', ...getAlign()] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': ['start', 'end', 'center', 'stretch'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...getAlign(), 'baseline'] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [{ self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline'] }],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': [...getAlign(), 'baseline'] }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: [padding] }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: [padding] }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: [padding] }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: [padding] }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: [padding] }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: [padding] }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: [padding] }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: [padding] }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: [padding] }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: [margin] }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: [margin] }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: [margin] }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: [margin] }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: [margin] }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: [margin] }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: [margin] }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: [margin] }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: [margin] }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x': [{ 'space-x': [space] }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y': [{ 'space-y': [space] }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y-reverse': ['space-y-reverse'],\n            // Sizing\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: ['auto', 'min', 'max', 'fit', isArbitraryValue, spacing] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [{ 'min-w': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        '0',\n                        'none',\n                        'full',\n                        'min',\n                        'max',\n                        'fit',\n                        'prose',\n                        { screen: [isTshirtSize] },\n                        isTshirtSize,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit'] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit'] }],\n            // Typography\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [{ text: ['base', isTshirtSize, isArbitraryLength] }],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [\n                {\n                    font: [\n                        'thin',\n                        'extralight',\n                        'light',\n                        'normal',\n                        'medium',\n                        'semibold',\n                        'bold',\n                        'extrabold',\n                        'black',\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isAny] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [\n                {\n                    tracking: [\n                        'tighter',\n                        'tight',\n                        'normal',\n                        'wide',\n                        'wider',\n                        'widest',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [{ 'line-clamp': ['none', isNumber, isArbitraryNumber] }],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        'none',\n                        'tight',\n                        'snug',\n                        'normal',\n                        'relaxed',\n                        'loose',\n                        isArbitraryValue,\n                        isLength,\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryValue] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [{ list: ['none', 'disc', 'decimal', isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: [colors] }],\n            /**\n             * Placeholder Opacity\n             * @see https://tailwindcss.com/docs/placeholder-opacity\n             */\n            'placeholder-opacity': [{ 'placeholder-opacity': [opacity] }],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: [colors] }],\n            /**\n             * Text Opacity\n             * @see https://tailwindcss.com/docs/text-opacity\n             */\n            'text-opacity': [{ 'text-opacity': [opacity] }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...getLineStyles(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [{ decoration: ['auto', 'from-font', isLength] }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [{ 'underline-offset': ['auto', isArbitraryValue, isLength] }],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: [colors] }],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: getSpacingWithArbitrary() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryValue] }],\n            // Backgrounds\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Opacity\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/background-opacity\n             */\n            'bg-opacity': [{ 'bg-opacity': [opacity] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: [...getPositions(), isArbitraryPosition] }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: ['no-repeat', { repeat: ['', 'x', 'y', 'round', 'space'] }] }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: ['auto', 'cover', 'contain', isArbitrarySize] }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        { 'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                        isArbitraryUrl,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: [colors] }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: [gradientColorStops] }],\n            // Borders\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: [borderRadius] }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': [borderRadius] }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': [borderRadius] }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': [borderRadius] }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': [borderRadius] }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': [borderRadius] }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': [borderRadius] }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': [borderRadius] }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': [borderRadius] }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': [borderRadius] }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': [borderRadius] }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': [borderRadius] }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': [borderRadius] }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': [borderRadius] }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': [borderRadius] }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: [borderWidth] }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': [borderWidth] }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': [borderWidth] }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': [borderWidth] }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': [borderWidth] }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': [borderWidth] }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': [borderWidth] }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': [borderWidth] }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': [borderWidth] }],\n            /**\n             * Border Opacity\n             * @see https://tailwindcss.com/docs/border-opacity\n             */\n            'border-opacity': [{ 'border-opacity': [opacity] }],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...getLineStyles(), 'hidden'] }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x': [{ 'divide-x': [borderWidth] }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y': [{ 'divide-y': [borderWidth] }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Divide Opacity\n             * @see https://tailwindcss.com/docs/divide-opacity\n             */\n            'divide-opacity': [{ 'divide-opacity': [opacity] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/divide-style\n             */\n            'divide-style': [{ divide: getLineStyles() }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: [borderColor] }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': [borderColor] }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': [borderColor] }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': [borderColor] }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': [borderColor] }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': [borderColor] }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': [borderColor] }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: [borderColor] }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: ['', ...getLineStyles()] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [{ 'outline-offset': [isArbitraryValue, isLength] }],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [{ outline: [isLength] }],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: [colors] }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w': [{ ring: getLengthWithEmpty() }],\n            /**\n             * Ring Width Inset\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/ring-color\n             */\n            'ring-color': [{ ring: [colors] }],\n            /**\n             * Ring Opacity\n             * @see https://tailwindcss.com/docs/ring-opacity\n             */\n            'ring-opacity': [{ 'ring-opacity': [opacity] }],\n            /**\n             * Ring Offset Width\n             * @see https://tailwindcss.com/docs/ring-offset-width\n             */\n            'ring-offset-w': [{ 'ring-offset': [isLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://tailwindcss.com/docs/ring-offset-color\n             */\n            'ring-offset-color': [{ 'ring-offset': [colors] }],\n            // Effects\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [{ shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow] }],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow-color\n             */\n            'shadow-color': [{ shadow: [isAny] }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [opacity] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': getBlendModes() }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': getBlendModes() }],\n            // Filters\n            /**\n             * Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [{ filter: ['', 'none'] }],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: [blur] }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [brightness] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [contrast] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [{ 'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue] }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: [grayscale] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [hueRotate] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: [invert] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [saturate] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: [sepia] }],\n            /**\n             * Backdrop Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [{ 'backdrop-filter': ['', 'none'] }],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': [blur] }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [{ 'backdrop-brightness': [brightness] }],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [{ 'backdrop-contrast': [contrast] }],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [{ 'backdrop-grayscale': [grayscale] }],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [{ 'backdrop-hue-rotate': [hueRotate] }],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [{ 'backdrop-invert': [invert] }],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [{ 'backdrop-opacity': [opacity] }],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [{ 'backdrop-saturate': [saturate] }],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [{ 'backdrop-sepia': [sepia] }],\n            // Tables\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': [borderSpacing] }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': [borderSpacing] }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': [borderSpacing] }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n            // Transitions and Animation\n            /**\n             * Tranisition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        'none',\n                        'all',\n                        '',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: getNumberAndArbitrary() }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [{ ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue] }],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: getNumberAndArbitrary() }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue] }],\n            // Transforms\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [{ transform: ['', 'gpu', 'none'] }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: [scale] }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': [scale] }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': [scale] }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: [isInteger, isArbitraryValue] }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': [translate] }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': [translate] }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': [skew] }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': [skew] }],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [\n                {\n                    origin: [\n                        'center',\n                        'top',\n                        'top-right',\n                        'right',\n                        'bottom-right',\n                        'bottom',\n                        'bottom-left',\n                        'left',\n                        'top-left',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            // Interactivity\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: ['auto', colors] }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: ['appearance-none'],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: [colors] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['none', 'auto'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', 'y', 'x', ''] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'pinch-zoom',\n                        'manipulation',\n                        { pan: ['x', 'left', 'right', 'y', 'up', 'down'] },\n                    ],\n                },\n            ],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                { 'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue] },\n            ],\n            // SVG\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: [colors, 'none'] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [{ stroke: [isLength, isArbitraryNumber] }],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: [colors, 'none'] }],\n            // Accessibility\n            /**\n             * Screen Readers\n             * @see https://tailwindcss.com/docs/screen-readers\n             */\n            sr: ['sr-only', 'not-sr-only'],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n    } as const satisfies Config\n}\n", "import { Config } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport function mergeConfigs(baseConfig: Config, configExtension: Partial<Config>) {\n    for (const key in configExtension) {\n        mergePropertyRecursively(baseConfig as any, key, configExtension[key as keyof Config])\n    }\n\n    return baseConfig\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst overrideTypes = new Set(['string', 'number', 'boolean'])\n\nfunction mergePropertyRecursively(\n    baseObject: Record<string, unknown>,\n    mergeKey: string,\n    mergeValue: unknown,\n) {\n    if (\n        !hasOwnProperty.call(baseObject, mergeKey) ||\n        overrideTypes.has(typeof mergeValue) ||\n        mergeValue === null\n    ) {\n        baseObject[mergeKey] = mergeValue\n        return\n    }\n\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = (baseObject[mergeKey] as unknown[]).concat(mergeValue)\n        return\n    }\n\n    if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue\n            return\n        }\n\n        for (const nextKey in mergeValue) {\n            mergePropertyRecursively(\n                baseObject[mergeKey] as Record<string, unknown>,\n                nextKey,\n                mergeValue[nextKey as keyof object],\n            )\n        }\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { Config } from './types'\n\ntype CreateConfigSubsequent = (config: Config) => Config\n\nexport function extendTailwindMerge(\n    configExtension: Partial<Config> | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) {\n    return typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n", "import { twJoin } from './lib/tw-join'\n\nexport { createTailwindMerge } from './lib/create-tailwind-merge'\nexport { getDefaultConfig } from './lib/default-config'\nexport { extendTailwindMerge } from './lib/extend-tailwind-merge'\nexport { fromTheme } from './lib/from-theme'\nexport { mergeConfigs } from './lib/merge-configs'\nexport { twJoin, type ClassNameValue } from './lib/tw-join'\nexport { twMerge } from './lib/tw-merge'\nexport type { Config } from './lib/types'\nexport * as validators from './lib/validators'\n\n/**\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\n */\nexport const join = twJoin\n"], "names": ["twJoin", "index", "argument", "resolvedValue", "string", "arguments", "length", "toValue", "mix", "k", "CLASS_PART_SEPARATOR", "createClassUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "prefix", "Map", "prefixedClassGroupEntries", "getPrefixedClassGroupEntries", "Object", "entries", "classGroups", "for<PERSON>ach", "classGroup", "processClassesRecursively", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "classGroupEntries", "map", "prefixedClassGroup", "fromEntries", "value", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "IMPORTANT_MODIFIER", "createSplitModifiers", "separator", "isSeparatorSingleCharacter", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "splitModifiers", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "currentCharacter", "baseClassNameWithImportantModifier", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "sortModifiers", "sortedModifiers", "unsortedModifiers", "modifier", "isArbitraryVariant", "sort", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "classGroupsInConflict", "Set", "trim", "originalClassName", "Boolean", "isTailwindClass", "variantModifier", "modifierId", "reverse", "filter", "parsed", "classId", "add", "group", "createTailwindMerge", "createConfig", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "firstCreateConfig", "restCreateConfig", "reduce", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "result", "callTailwindMerge", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "fractionRegex", "stringLengths", "tshirtUnitRegex", "lengthUnitRegex", "shadowRegex", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isArbitrarySize", "isNever", "isArbitraryPosition", "isArbitraryUrl", "isUrl", "isArbitraryNumber", "isArbitraryWeight", "Number", "isNaN", "isPercent", "endsWith", "isInteger", "isIntegerOnly", "isArbitraryValue", "isAny", "isTshirtSize", "isArbitraryShadow", "is<PERSON><PERSON>ow", "label", "testValue", "getDefaultConfig", "colors", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getOverscroll", "getOverflow", "getSpacingWithAutoAndArbitrary", "getSpacingWithArbitrary", "getLengthWithEmpty", "getNumberWithAutoAndArbitrary", "getPositions", "getLineStyles", "getBlendModes", "getAlign", "getZeroAndEmpty", "getBreaks", "getNumber", "getNumberAndArbitrary", "aspect", "container", "columns", "box", "display", "clear", "isolation", "object", "overflow", "overscroll", "position", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "indent", "align", "whitespace", "hyphens", "bg", "repeat", "from", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "table", "caption", "transition", "duration", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "pan", "select", "fill", "stroke", "sr", "mergeConfigs", "baseConfig", "configExtension", "mergePropertyRecursively", "hasOwnProperty", "prototype", "overrideTypes", "baseObject", "mergeKey", "mergeValue", "call", "Array", "isArray", "concat", "<PERSON><PERSON><PERSON>", "extendTailwindMerge", "twMerge"], "mappings": ";;;;AAAA;;;;;;;;AAQG;SAMaA,MAAM,GAAA;EAClB,IAAIC,KAAK,GAAG,CAAC,CAAA;AACb,EAAA,IAAIC,QAAwB,CAAA;AAC5B,EAAA,IAAIC,aAAqB,CAAA;EACzB,IAAIC,MAAM,GAAG,EAAE,CAAA;AAEf,EAAA,OAAOH,KAAK,GAAGI,SAAS,CAACC,MAAM,EAAE;AAC7B,IAAA,IAAKJ,QAAQ,GAAGG,SAAS,CAACJ,KAAK,EAAE,CAAC,EAAG;AACjC,MAAA,IAAKE,aAAa,GAAGI,OAAO,CAACL,QAAQ,CAAC,EAAG;AACrCE,QAAAA,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC,CAAA;AACzBA,QAAAA,MAAM,IAAID,aAAa,CAAA;AAC1B,OAAA;AACJ,KAAA;AACJ,GAAA;AACD,EAAA,OAAOC,MAAM,CAAA;AACjB,CAAA;AAEA,SAASG,OAAO,CAACC,GAA4B,EAAA;AACzC,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;AACzB,IAAA,OAAOA,GAAG,CAAA;AACb,GAAA;AAED,EAAA,IAAIL,aAAqB,CAAA;EACzB,IAAIC,MAAM,GAAG,EAAE,CAAA;AAEf,EAAA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACF,MAAM,EAAEG,CAAC,EAAE,EAAE;AACjC,IAAA,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;MACR,IAAKN,aAAa,GAAGI,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;AAC9DL,QAAAA,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC,CAAA;AACzBA,QAAAA,MAAM,IAAID,aAAa,CAAA;AAC1B,OAAA;AACJ,KAAA;AACJ,GAAA;AAED,EAAA,OAAOC,MAAM,CAAA;AACjB;;ACpCA,IAAMM,oBAAoB,GAAG,GAAG,CAAA;AAE1B,SAAUC,gBAAgB,CAACC,MAAc,EAAA;AAC3C,EAAA,IAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC,CAAA;AACvC,EAAA,IAAQG,sBAAsB,GAA0CH,MAAM,CAAtEG,sBAAsB;IAAA,qBAA0CH,GAAAA,MAAM,CAA9CI,8BAA8B;IAA9BA,8BAA8B,GAAA,qBAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,qBAAA,CAAA;EAEnE,SAASC,eAAe,CAACC,SAAiB,EAAA;AACtC,IAAA,IAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACV,oBAAoB,CAAC,CAAA;AAExD;AACA,IAAA,IAAIS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACb,MAAM,KAAK,CAAC,EAAE;MACjDa,UAAU,CAACE,KAAK,EAAE,CAAA;AACrB,KAAA;IAED,OAAOC,iBAAiB,CAACH,UAAU,EAAEN,QAAQ,CAAC,IAAIU,8BAA8B,CAACL,SAAS,CAAC,CAAA;AAC/F,GAAA;AAEA,EAAA,SAASM,2BAA2B,CAACC,YAA0B,EAAEC,kBAA2B,EAAA;AACxF,IAAA,IAAMC,SAAS,GAAGZ,sBAAsB,CAACU,YAAY,CAAC,IAAI,EAAE,CAAA;AAE5D,IAAA,IAAIC,kBAAkB,IAAIV,8BAA8B,CAACS,YAAY,CAAC,EAAE;AACpE,MAAA,OAAA,EAAA,CAAA,MAAA,CAAWE,SAAS,EAAKX,8BAA8B,CAACS,YAAY,CAAE,CAAA,CAAA;AACzE,KAAA;AAED,IAAA,OAAOE,SAAS,CAAA;AACpB,GAAA;EAEA,OAAO;AACHV,IAAAA,eAAe,EAAfA,eAAe;AACfO,IAAAA,2BAA2B,EAA3BA,2BAAAA;GACH,CAAA;AACL,CAAA;AAEA,SAASF,iBAAiB,CACtBH,UAAoB,EACpBS,eAAgC,EAAA;AAEhC,EAAA,IAAIT,UAAU,CAACb,MAAM,KAAK,CAAC,EAAE;IACzB,OAAOsB,eAAe,CAACH,YAAY,CAAA;AACtC,GAAA;AAED,EAAA,IAAMI,gBAAgB,GAAGV,UAAU,CAAC,CAAC,CAAE,CAAA;EACvC,IAAMW,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC,CAAA;AAC1E,EAAA,IAAMI,2BAA2B,GAAGH,mBAAmB,GACjDR,iBAAiB,CAACH,UAAU,CAACe,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAC,GAC3DK,SAAS,CAAA;AAEf,EAAA,IAAIF,2BAA2B,EAAE;AAC7B,IAAA,OAAOA,2BAA2B,CAAA;AACrC,GAAA;AAED,EAAA,IAAIL,eAAe,CAACQ,UAAU,CAAC9B,MAAM,KAAK,CAAC,EAAE;AACzC,IAAA,OAAO6B,SAAS,CAAA;AACnB,GAAA;AAED,EAAA,IAAME,SAAS,GAAGlB,UAAU,CAACmB,IAAI,CAAC5B,oBAAoB,CAAC,CAAA;AAEvD,EAAA,OAAOkB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,UAAA,IAAA,EAAA;IAAA,IAAGC,SAAS,QAATA,SAAS,CAAA;IAAA,OAAOA,SAAS,CAACH,SAAS,CAAC,CAAA;AAAA,GAAA,CAAC,EAAEZ,YAAY,CAAA;AACjG,CAAA;AAEA,IAAMgB,sBAAsB,GAAG,YAAY,CAAA;AAE3C,SAASlB,8BAA8B,CAACL,SAAiB,EAAA;AACrD,EAAA,IAAIuB,sBAAsB,CAACC,IAAI,CAACxB,SAAS,CAAC,EAAE;IACxC,IAAMyB,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC1B,SAAS,CAAE,CAAC,CAAC,CAAC,CAAA;AAC7E,IAAA,IAAM2B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C,CAAA;AAED,IAAA,IAAIF,QAAQ,EAAE;AACV;MACA,OAAO,aAAa,GAAGA,QAAQ,CAAA;AAClC,KAAA;AACJ,GAAA;AACL,CAAA;AAEA;;AAEG;AACG,SAAU/B,cAAc,CAACF,MAAc,EAAA;AACzC,EAAA,IAAQoC,KAAK,GAAapC,MAAM,CAAxBoC,KAAK;IAAEC,MAAM,GAAKrC,MAAM,CAAjBqC,MAAM,CAAA;AACrB,EAAA,IAAMpC,QAAQ,GAAoB;IAC9BkB,QAAQ,EAAE,IAAImB,GAAG,EAA2B;AAC5Cd,IAAAA,UAAU,EAAE,EAAA;GACf,CAAA;AAED,EAAA,IAAMe,yBAAyB,GAAGC,4BAA4B,CAC1DC,MAAM,CAACC,OAAO,CAAC1C,MAAM,CAAC2C,WAAW,CAAC,EAClCN,MAAM,CACT,CAAA;EAEDE,yBAAyB,CAACK,OAAO,CAAC,UAA+B,KAAA,EAAA;AAAA,IAAA,IAA7B/B,YAAY,GAAA,KAAA,CAAA,CAAA,CAAA;MAAEgC,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACxDC,yBAAyB,CAACD,UAAU,EAAE5C,QAAQ,EAAEY,YAAY,EAAEuB,KAAK,CAAC,CAAA;AACxE,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOnC,QAAQ,CAAA;AACnB,CAAA;AAEA,SAAS6C,yBAAyB,CAC9BD,UAAsB,EACtB7B,eAAgC,EAChCH,YAA0B,EAC1BuB,KAAkB,EAAA;AAElBS,EAAAA,UAAU,CAACD,OAAO,CAAC,UAACG,eAAe,EAAI;AACnC,IAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;AACrC,MAAA,IAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAG/B,eAAe,GAAGiC,OAAO,CAACjC,eAAe,EAAE+B,eAAe,CAAC,CAAA;MACxFC,qBAAqB,CAACnC,YAAY,GAAGA,YAAY,CAAA;AACjD,MAAA,OAAA;AACH,KAAA;AAED,IAAA,IAAI,OAAOkC,eAAe,KAAK,UAAU,EAAE;AACvC,MAAA,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;QAChCD,yBAAyB,CACrBC,eAAe,CAACX,KAAK,CAAC,EACtBpB,eAAe,EACfH,YAAY,EACZuB,KAAK,CACR,CAAA;AACD,QAAA,OAAA;AACH,OAAA;AAEDpB,MAAAA,eAAe,CAACQ,UAAU,CAAC2B,IAAI,CAAC;AAC5BvB,QAAAA,SAAS,EAAEmB,eAAe;AAC1BlC,QAAAA,YAAY,EAAZA,YAAAA;AACH,OAAA,CAAC,CAAA;AAEF,MAAA,OAAA;AACH,KAAA;IAED4B,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACH,OAAO,CAAC,UAAsB,KAAA,EAAA;AAAA,MAAA,IAApBQ,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA;QAAEP,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACrDC,MAAAA,yBAAyB,CACrBD,UAAU,EACVI,OAAO,CAACjC,eAAe,EAAEoC,GAAG,CAAC,EAC7BvC,YAAY,EACZuB,KAAK,CACR,CAAA;AACL,KAAC,CAAC,CAAA;AACN,GAAC,CAAC,CAAA;AACN,CAAA;AAEA,SAASa,OAAO,CAACjC,eAAgC,EAAEqC,IAAY,EAAA;EAC3D,IAAIC,sBAAsB,GAAGtC,eAAe,CAAA;EAE5CqC,IAAI,CAAC7C,KAAK,CAACV,oBAAoB,CAAC,CAAC8C,OAAO,CAAC,UAACW,QAAQ,EAAI;IAClD,IAAI,CAACD,sBAAsB,CAACnC,QAAQ,CAACqC,GAAG,CAACD,QAAQ,CAAC,EAAE;AAChDD,MAAAA,sBAAsB,CAACnC,QAAQ,CAACsC,GAAG,CAACF,QAAQ,EAAE;QAC1CpC,QAAQ,EAAE,IAAImB,GAAG,EAAE;AACnBd,QAAAA,UAAU,EAAE,EAAA;AACf,OAAA,CAAC,CAAA;AACL,KAAA;IAED8B,sBAAsB,GAAGA,sBAAsB,CAACnC,QAAQ,CAACC,GAAG,CAACmC,QAAQ,CAAE,CAAA;AAC3E,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOD,sBAAsB,CAAA;AACjC,CAAA;AAEA,SAASJ,aAAa,CAACQ,IAAkC,EAAA;EACrD,OAAQA,IAAoB,CAACR,aAAa,CAAA;AAC9C,CAAA;AAEA,SAASV,4BAA4B,CACjCmB,iBAAwE,EACxEtB,MAA0B,EAAA;EAE1B,IAAI,CAACA,MAAM,EAAE;AACT,IAAA,OAAOsB,iBAAiB,CAAA;AAC3B,GAAA;AAED,EAAA,OAAOA,iBAAiB,CAACC,GAAG,CAAC,UAA+B,KAAA,EAAA;AAAA,IAAA,IAA7B/C,YAAY,GAAA,KAAA,CAAA,CAAA,CAAA;MAAEgC,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACnD,IAAMgB,kBAAkB,GAAGhB,UAAU,CAACe,GAAG,CAAC,UAACb,eAAe,EAAI;AAC1D,MAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;QACrC,OAAOV,MAAM,GAAGU,eAAe,CAAA;AAClC,OAAA;AAED,MAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;AACrC,QAAA,OAAON,MAAM,CAACqB,WAAW,CACrBrB,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACa,GAAG,CAAC,UAAA,KAAA,EAAA;AAAA,UAAA,IAAER,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA;YAAEW,KAAK,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,UAAA,OAAM,CAAC1B,MAAM,GAAGe,GAAG,EAAEW,KAAK,CAAC,CAAA;AAAA,SAAA,CAAC,CAC/E,CAAA;AACJ,OAAA;AAED,MAAA,OAAOhB,eAAe,CAAA;AAC1B,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,CAAClC,YAAY,EAAEgD,kBAAkB,CAAC,CAAA;AAC7C,GAAC,CAAC,CAAA;AACN;;ACnMA;AACM,SAAUG,cAAc,CAAaC,YAAoB,EAAA;EAC3D,IAAIA,YAAY,GAAG,CAAC,EAAE;IAClB,OAAO;AACH7C,MAAAA,GAAG,EAAE,SAAA,GAAA,GAAA;AAAA,QAAA,OAAMG,SAAS,CAAA;AAAA,OAAA;MACpBkC,GAAG,EAAE,eAAK,EAAE;KACf,CAAA;AACJ,GAAA;EAED,IAAIS,SAAS,GAAG,CAAC,CAAA;AACjB,EAAA,IAAIC,KAAK,GAAG,IAAI7B,GAAG,EAAc,CAAA;AACjC,EAAA,IAAI8B,aAAa,GAAG,IAAI9B,GAAG,EAAc,CAAA;AAEzC,EAAA,SAAS+B,MAAM,CAACjB,GAAQ,EAAEW,KAAY,EAAA;AAClCI,IAAAA,KAAK,CAACV,GAAG,CAACL,GAAG,EAAEW,KAAK,CAAC,CAAA;AACrBG,IAAAA,SAAS,EAAE,CAAA;IAEX,IAAIA,SAAS,GAAGD,YAAY,EAAE;AAC1BC,MAAAA,SAAS,GAAG,CAAC,CAAA;AACbE,MAAAA,aAAa,GAAGD,KAAK,CAAA;MACrBA,KAAK,GAAG,IAAI7B,GAAG,EAAE,CAAA;AACpB,KAAA;AACL,GAAA;EAEA,OAAO;IACHlB,GAAG,EAAA,SAAA,GAAA,CAACgC,GAAG,EAAA;AACH,MAAA,IAAIW,KAAK,GAAGI,KAAK,CAAC/C,GAAG,CAACgC,GAAG,CAAC,CAAA;MAE1B,IAAIW,KAAK,KAAKxC,SAAS,EAAE;AACrB,QAAA,OAAOwC,KAAK,CAAA;AACf,OAAA;MACD,IAAI,CAACA,KAAK,GAAGK,aAAa,CAAChD,GAAG,CAACgC,GAAG,CAAC,MAAM7B,SAAS,EAAE;AAChD8C,QAAAA,MAAM,CAACjB,GAAG,EAAEW,KAAK,CAAC,CAAA;AAClB,QAAA,OAAOA,KAAK,CAAA;AACf,OAAA;KACJ;AACDN,IAAAA,GAAG,EAACL,SAAAA,GAAAA,CAAAA,GAAG,EAAEW,KAAK,EAAA;AACV,MAAA,IAAII,KAAK,CAACX,GAAG,CAACJ,GAAG,CAAC,EAAE;AAChBe,QAAAA,KAAK,CAACV,GAAG,CAACL,GAAG,EAAEW,KAAK,CAAC,CAAA;AACxB,OAAA,MAAM;AACHM,QAAAA,MAAM,CAACjB,GAAG,EAAEW,KAAK,CAAC,CAAA;AACrB,OAAA;AACL,KAAA;GACH,CAAA;AACL;;ACjDO,IAAMO,kBAAkB,GAAG,GAAG,CAAA;AAE/B,SAAUC,oBAAoB,CAACvE,MAAc,EAAA;AAC/C,EAAA,IAAMwE,SAAS,GAAGxE,MAAM,CAACwE,SAAS,IAAI,GAAG,CAAA;AACzC,EAAA,IAAMC,0BAA0B,GAAGD,SAAS,CAAC9E,MAAM,KAAK,CAAC,CAAA;AACzD,EAAA,IAAMgF,uBAAuB,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAA;AAC5C,EAAA,IAAMG,eAAe,GAAGH,SAAS,CAAC9E,MAAM,CAAA;AAExC;AACA,EAAA,OAAO,SAASkF,cAAc,CAACtE,SAAiB,EAAA;IAC5C,IAAMuE,SAAS,GAAG,EAAE,CAAA;IAEpB,IAAIC,YAAY,GAAG,CAAC,CAAA;IACpB,IAAIC,aAAa,GAAG,CAAC,CAAA;AACrB,IAAA,IAAIC,uBAA2C,CAAA;AAE/C,IAAA,KAAK,IAAI3F,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGiB,SAAS,CAACZ,MAAM,EAAEL,KAAK,EAAE,EAAE;AACnD,MAAA,IAAI4F,gBAAgB,GAAG3E,SAAS,CAACjB,KAAK,CAAC,CAAA;MAEvC,IAAIyF,YAAY,KAAK,CAAC,EAAE;AACpB,QAAA,IACIG,gBAAgB,KAAKP,uBAAuB,KAC3CD,0BAA0B,IACvBnE,SAAS,CAACgB,KAAK,CAACjC,KAAK,EAAEA,KAAK,GAAGsF,eAAe,CAAC,KAAKH,SAAS,CAAC,EACpE;UACEK,SAAS,CAAC1B,IAAI,CAAC7C,SAAS,CAACgB,KAAK,CAACyD,aAAa,EAAE1F,KAAK,CAAC,CAAC,CAAA;UACrD0F,aAAa,GAAG1F,KAAK,GAAGsF,eAAe,CAAA;AACvC,UAAA,SAAA;AACH,SAAA;QAED,IAAIM,gBAAgB,KAAK,GAAG,EAAE;AAC1BD,UAAAA,uBAAuB,GAAG3F,KAAK,CAAA;AAC/B,UAAA,SAAA;AACH,SAAA;AACJ,OAAA;MAED,IAAI4F,gBAAgB,KAAK,GAAG,EAAE;AAC1BH,QAAAA,YAAY,EAAE,CAAA;AACjB,OAAA,MAAM,IAAIG,gBAAgB,KAAK,GAAG,EAAE;AACjCH,QAAAA,YAAY,EAAE,CAAA;AACjB,OAAA;AACJ,KAAA;AAED,IAAA,IAAMI,kCAAkC,GACpCL,SAAS,CAACnF,MAAM,KAAK,CAAC,GAAGY,SAAS,GAAGA,SAAS,CAAC4B,SAAS,CAAC6C,aAAa,CAAC,CAAA;AAC3E,IAAA,IAAMI,oBAAoB,GACtBD,kCAAkC,CAACE,UAAU,CAACd,kBAAkB,CAAC,CAAA;IACrE,IAAMe,aAAa,GAAGF,oBAAoB,GACpCD,kCAAkC,CAAChD,SAAS,CAAC,CAAC,CAAC,GAC/CgD,kCAAkC,CAAA;AAExC,IAAA,IAAMI,4BAA4B,GAC9BN,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAa,GAC5DC,uBAAuB,GAAGD,aAAa,GACvCxD,SAAS,CAAA;IAEnB,OAAO;AACHsD,MAAAA,SAAS,EAATA,SAAS;AACTM,MAAAA,oBAAoB,EAApBA,oBAAoB;AACpBE,MAAAA,aAAa,EAAbA,aAAa;AACbC,MAAAA,4BAA4B,EAA5BA,4BAAAA;KACH,CAAA;GACJ,CAAA;AACL,CAAA;AAEA;;;;AAIG;AACG,SAAUC,aAAa,CAACV,SAAmB,EAAA;AAC7C,EAAA,IAAIA,SAAS,CAACnF,MAAM,IAAI,CAAC,EAAE;AACvB,IAAA,OAAOmF,SAAS,CAAA;AACnB,GAAA;EAED,IAAMW,eAAe,GAAa,EAAE,CAAA;EACpC,IAAIC,iBAAiB,GAAa,EAAE,CAAA;AAEpCZ,EAAAA,SAAS,CAACjC,OAAO,CAAC,UAAC8C,QAAQ,EAAI;AAC3B,IAAA,IAAMC,kBAAkB,GAAGD,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;AAE9C,IAAA,IAAIC,kBAAkB,EAAE;MACpBH,eAAe,CAACrC,IAAI,CAAA,KAAA,CAApBqC,eAAe,EAASC,iBAAiB,CAACG,IAAI,EAAE,CAAEF,MAAAA,CAAAA,CAAAA,QAAQ,CAAC,CAAA,CAAA,CAAA;AAC3DD,MAAAA,iBAAiB,GAAG,EAAE,CAAA;AACzB,KAAA,MAAM;AACHA,MAAAA,iBAAiB,CAACtC,IAAI,CAACuC,QAAQ,CAAC,CAAA;AACnC,KAAA;AACL,GAAC,CAAC,CAAA;EAEFF,eAAe,CAACrC,IAAI,CAApBqC,KAAAA,CAAAA,eAAe,EAASC,iBAAiB,CAACG,IAAI,EAAE,CAAC,CAAA;AAEjD,EAAA,OAAOJ,eAAe,CAAA;AAC1B;;ACvFM,SAAUK,iBAAiB,CAAC7F,MAAc,EAAA;EAC5C,OAAO;AACHmE,IAAAA,KAAK,EAAEH,cAAc,CAAiBhE,MAAM,CAACkE,SAAS,CAAC;AACvDU,IAAAA,cAAc,EAAEL,oBAAoB,CAACvE,MAAM,CAAC;IAC5C,GAAGD,gBAAgB,CAACC,MAAM,CAAA;GAC7B,CAAA;AACL;;ACVA,IAAM8F,mBAAmB,GAAG,KAAK,CAAA;AAEjB,SAAAC,cAAc,CAACC,SAAiB,EAAEC,WAAwB,EAAA;AACtE,EAAA,IAAQrB,cAAc,GAAmDqB,WAAW,CAA5ErB,cAAc;IAAEvE,eAAe,GAAkC4F,WAAW,CAA5D5F,eAAe;IAAEO,2BAA2B,GAAKqF,WAAW,CAA3CrF,2BAA2B,CAAA;AAEpE;;;;;;AAMG;AACH,EAAA,IAAMsF,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAE/C,EAAA,OACIH,SAAS,CACJI,IAAI,EAAE,CACN5F,KAAK,CAACsF,mBAAmB,CAAC,CAC1BlC,GAAG,CAAC,UAACyC,iBAAiB,EAAI;IACvB,IAKIzB,eAAAA,GAAAA,cAAc,CAACyB,iBAAiB,CAAC;AAJjCxB,MAAAA,SAAS,mBAATA,SAAS;AACTM,MAAAA,oBAAoB,mBAApBA,oBAAoB;AACpBE,MAAAA,aAAa,mBAAbA,aAAa;AACbC,MAAAA,4BAA4B,mBAA5BA,4BAA4B,CAAA;AAGhC,IAAA,IAAIzE,YAAY,GAAGR,eAAe,CAC9BiF,4BAA4B,GACtBD,aAAa,CAACnD,SAAS,CAAC,CAAC,EAAEoD,4BAA4B,CAAC,GACxDD,aAAa,CACtB,CAAA;AAED,IAAA,IAAIvE,kBAAkB,GAAGwF,OAAO,CAAChB,4BAA4B,CAAC,CAAA;IAE9D,IAAI,CAACzE,YAAY,EAAE;MACf,IAAI,CAACyE,4BAA4B,EAAE;QAC/B,OAAO;AACHiB,UAAAA,eAAe,EAAE,KAAc;AAC/BF,UAAAA,iBAAiB,EAAjBA,iBAAAA;SACH,CAAA;AACJ,OAAA;AAEDxF,MAAAA,YAAY,GAAGR,eAAe,CAACgF,aAAa,CAAC,CAAA;MAE7C,IAAI,CAACxE,YAAY,EAAE;QACf,OAAO;AACH0F,UAAAA,eAAe,EAAE,KAAc;AAC/BF,UAAAA,iBAAiB,EAAjBA,iBAAAA;SACH,CAAA;AACJ,OAAA;AAEDvF,MAAAA,kBAAkB,GAAG,KAAK,CAAA;AAC7B,KAAA;IAED,IAAM0F,eAAe,GAAGjB,aAAa,CAACV,SAAS,CAAC,CAACnD,IAAI,CAAC,GAAG,CAAC,CAAA;IAE1D,IAAM+E,UAAU,GAAGtB,oBAAoB,GACjCqB,eAAe,GAAGlC,kBAAkB,GACpCkC,eAAe,CAAA;IAErB,OAAO;AACHD,MAAAA,eAAe,EAAE,IAAa;AAC9BE,MAAAA,UAAU,EAAVA,UAAU;AACV5F,MAAAA,YAAY,EAAZA,YAAY;AACZwF,MAAAA,iBAAiB,EAAjBA,iBAAiB;AACjBvF,MAAAA,kBAAkB,EAAlBA,kBAAAA;KACH,CAAA;GACJ,CAAC,CACD4F,OAAO,EAAA;AACR;AAAA,GACCC,MAAM,CAAC,UAACC,MAAM,EAAI;AACf,IAAA,IAAI,CAACA,MAAM,CAACL,eAAe,EAAE;AACzB,MAAA,OAAO,IAAI,CAAA;AACd,KAAA;AAED,IAAA,IAAQE,UAAU,GAAuCG,MAAM,CAAvDH,UAAU;MAAE5F,YAAY,GAAyB+F,MAAM,CAA3C/F,YAAY;MAAEC,kBAAkB,GAAK8F,MAAM,CAA7B9F,kBAAkB,CAAA;AAEpD,IAAA,IAAM+F,OAAO,GAAGJ,UAAU,GAAG5F,YAAY,CAAA;AAEzC,IAAA,IAAIqF,qBAAqB,CAAC1C,GAAG,CAACqD,OAAO,CAAC,EAAE;AACpC,MAAA,OAAO,KAAK,CAAA;AACf,KAAA;AAEDX,IAAAA,qBAAqB,CAACY,GAAG,CAACD,OAAO,CAAC,CAAA;IAElCjG,2BAA2B,CAACC,YAAY,EAAEC,kBAAkB,CAAC,CAAC8B,OAAO,CAAC,UAACmE,KAAK,EAAA;AAAA,MAAA,OACxEb,qBAAqB,CAACY,GAAG,CAACL,UAAU,GAAGM,KAAK,CAAC,CAAA;KAChD,CAAA,CAAA;AAED,IAAA,OAAO,IAAI,CAAA;GACd,CAAC,CACDL,OAAO,EAAE,CACT9C,GAAG,CAAC,UAACgD,MAAM,EAAA;IAAA,OAAKA,MAAM,CAACP,iBAAiB,CAAA;AAAA,GAAA,CAAC,CACzC3E,IAAI,CAAC,GAAG,CAAC,CAAA;AAEtB;;ACxFgB,SAAAsF,mBAAmB,GACkC;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAA9DC,YAA8D,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAA9DA,YAA8D,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;AAEjE,EAAA,IAAIhB,WAAwB,CAAA;AAC5B,EAAA,IAAIiB,QAAqC,CAAA;AACzC,EAAA,IAAIC,QAAqC,CAAA;EACzC,IAAIC,cAAc,GAAGC,iBAAiB,CAAA;EAEtC,SAASA,iBAAiB,CAACrB,SAAiB,EAAA;IACxC,IAAOsB,iBAAiB,GAAyBL,YAAY,CAAA,CAAA,CAAA;AAAhCM,MAAAA,gBAAgB,GAAIN,YAAY,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IAE7D,IAAMjH,MAAM,GAAGuH,gBAAgB,CAACC,MAAM,CAClC,UAACC,cAAc,EAAEC,mBAAmB,EAAA;MAAA,OAAKA,mBAAmB,CAACD,cAAc,CAAC,CAAA;KAC5EH,EAAAA,iBAAiB,EAAE,CACtB,CAAA;AAEDrB,IAAAA,WAAW,GAAGJ,iBAAiB,CAAC7F,MAAM,CAAC,CAAA;AACvCkH,IAAAA,QAAQ,GAAGjB,WAAW,CAAC9B,KAAK,CAAC/C,GAAG,CAAA;AAChC+F,IAAAA,QAAQ,GAAGlB,WAAW,CAAC9B,KAAK,CAACV,GAAG,CAAA;AAChC2D,IAAAA,cAAc,GAAGO,aAAa,CAAA;IAE9B,OAAOA,aAAa,CAAC3B,SAAS,CAAC,CAAA;AACnC,GAAA;EAEA,SAAS2B,aAAa,CAAC3B,SAAiB,EAAA;AACpC,IAAA,IAAM4B,YAAY,GAAGV,QAAQ,CAAClB,SAAS,CAAC,CAAA;AAExC,IAAA,IAAI4B,YAAY,EAAE;AACd,MAAA,OAAOA,YAAY,CAAA;AACtB,KAAA;AAED,IAAA,IAAMC,MAAM,GAAG9B,cAAc,CAACC,SAAS,EAAEC,WAAW,CAAC,CAAA;AACrDkB,IAAAA,QAAQ,CAACnB,SAAS,EAAE6B,MAAM,CAAC,CAAA;AAE3B,IAAA,OAAOA,MAAM,CAAA;AACjB,GAAA;EAEA,OAAO,SAASC,iBAAiB,GAAA;IAC7B,OAAOV,cAAc,CAAChI,MAAM,CAAC2I,KAAK,CAAC,IAAI,EAAEtI,SAAgB,CAAC,CAAC,CAAA;GAC9D,CAAA;AACL;;AChDM,SAAUuI,SAAS,CAAC5E,GAAW,EAAA;AACjC,EAAA,IAAM6E,WAAW,GAAG,SAAdA,WAAW,CAAI7F,KAAkB,EAAA;AAAA,IAAA,OAAKA,KAAK,CAACgB,GAAG,CAAC,IAAI,EAAE,CAAA;AAAA,GAAA,CAAA;EAE5D6E,WAAW,CAAC/E,aAAa,GAAG,IAAa,CAAA;AAEzC,EAAA,OAAO+E,WAAW,CAAA;AACtB;;ACRA,IAAMC,mBAAmB,GAAG,4BAA4B,CAAA;AACxD,IAAMC,aAAa,GAAG,YAAY,CAAA;AAClC,IAAMC,aAAa,gBAAG,IAAIjC,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;AACvD,IAAMkC,eAAe,GAAG,kCAAkC,CAAA;AAC1D,IAAMC,eAAe,GACjB,2HAA2H,CAAA;AAC/H;AACA,IAAMC,WAAW,GAAG,wDAAwD,CAAA;AAEtE,SAAUC,QAAQ,CAACzE,KAAa,EAAA;EAClC,OACI0E,QAAQ,CAAC1E,KAAK,CAAC,IACfqE,aAAa,CAAC5E,GAAG,CAACO,KAAK,CAAC,IACxBoE,aAAa,CAACrG,IAAI,CAACiC,KAAK,CAAC,IACzB2E,iBAAiB,CAAC3E,KAAK,CAAC,CAAA;AAEhC,CAAA;AAEM,SAAU2E,iBAAiB,CAAC3E,KAAa,EAAA;AAC3C,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,QAAQ,EAAE6E,YAAY,CAAC,CAAA;AAC7D,CAAA;AAEM,SAAUC,eAAe,CAAC9E,KAAa,EAAA;AACzC,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,MAAM,EAAE+E,OAAO,CAAC,CAAA;AACtD,CAAA;AAEM,SAAUC,mBAAmB,CAAChF,KAAa,EAAA;AAC7C,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,UAAU,EAAE+E,OAAO,CAAC,CAAA;AAC1D,CAAA;AAEM,SAAUE,cAAc,CAACjF,KAAa,EAAA;AACxC,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,KAAK,EAAEkF,KAAK,CAAC,CAAA;AACnD,CAAA;AAEM,SAAUC,iBAAiB,CAACnF,KAAa,EAAA;AAC3C,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,QAAQ,EAAE0E,QAAQ,CAAC,CAAA;AACzD,CAAA;AAEA;;AAEG;AACI,IAAMU,iBAAiB,GAAGD,iBAAiB,CAAA;AAE5C,SAAUT,QAAQ,CAAC1E,KAAa,EAAA;EAClC,OAAO,CAACqF,MAAM,CAACC,KAAK,CAACD,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAA;AACvC,CAAA;AAEM,SAAUuF,SAAS,CAACvF,KAAa,EAAA;AACnC,EAAA,OAAOA,KAAK,CAACwF,QAAQ,CAAC,GAAG,CAAC,IAAId,QAAQ,CAAC1E,KAAK,CAACzC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9D,CAAA;AAEM,SAAUkI,SAAS,CAACzF,KAAa,EAAA;AACnC,EAAA,OAAO0F,aAAa,CAAC1F,KAAK,CAAC,IAAI4E,mBAAmB,CAAC5E,KAAK,EAAE,QAAQ,EAAE0F,aAAa,CAAC,CAAA;AACtF,CAAA;AAEM,SAAUC,gBAAgB,CAAC3F,KAAa,EAAA;AAC1C,EAAA,OAAOmE,mBAAmB,CAACpG,IAAI,CAACiC,KAAK,CAAC,CAAA;AAC1C,CAAA;SAEgB4F,KAAK,GAAA;AACjB,EAAA,OAAO,IAAI,CAAA;AACf,CAAA;AAEM,SAAUC,YAAY,CAAC7F,KAAa,EAAA;AACtC,EAAA,OAAOsE,eAAe,CAACvG,IAAI,CAACiC,KAAK,CAAC,CAAA;AACtC,CAAA;AAEM,SAAU8F,iBAAiB,CAAC9F,KAAa,EAAA;AAC3C,EAAA,OAAO4E,mBAAmB,CAAC5E,KAAK,EAAE,EAAE,EAAE+F,QAAQ,CAAC,CAAA;AACnD,CAAA;AAEA,SAASnB,mBAAmB,CAAC5E,KAAa,EAAEgG,KAAa,EAAEC,SAAqC,EAAA;AAC5F,EAAA,IAAMnC,MAAM,GAAGK,mBAAmB,CAAClG,IAAI,CAAC+B,KAAK,CAAC,CAAA;AAE9C,EAAA,IAAI8D,MAAM,EAAE;AACR,IAAA,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;AACX,MAAA,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAKkC,KAAK,CAAA;AAC7B,KAAA;AAED,IAAA,OAAOC,SAAS,CAACnC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAA;AAC/B,GAAA;AAED,EAAA,OAAO,KAAK,CAAA;AAChB,CAAA;AAEA,SAASe,YAAY,CAAC7E,KAAa,EAAA;AAC/B,EAAA,OAAOuE,eAAe,CAACxG,IAAI,CAACiC,KAAK,CAAC,CAAA;AACtC,CAAA;AAEA,SAAS+E,OAAO,GAAA;AACZ,EAAA,OAAO,KAAK,CAAA;AAChB,CAAA;AAEA,SAASG,KAAK,CAAClF,KAAa,EAAA;AACxB,EAAA,OAAOA,KAAK,CAACqB,UAAU,CAAC,MAAM,CAAC,CAAA;AACnC,CAAA;AAEA,SAASqE,aAAa,CAAC1F,KAAa,EAAA;EAChC,OAAOqF,MAAM,CAACI,SAAS,CAACJ,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAA;AAC1C,CAAA;AAEA,SAAS+F,QAAQ,CAAC/F,KAAa,EAAA;AAC3B,EAAA,OAAOwE,WAAW,CAACzG,IAAI,CAACiC,KAAK,CAAC,CAAA;AAClC;;;;;;;;;;;;;;;;;;;;SCrFgBkG,gBAAgB,GAAA;AAC5B,EAAA,IAAMC,MAAM,GAAGlC,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAMmC,OAAO,GAAGnC,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAMoC,IAAI,GAAGpC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9B,EAAA,IAAMqC,UAAU,GAAGrC,SAAS,CAAC,YAAY,CAAC,CAAA;AAC1C,EAAA,IAAMsC,WAAW,GAAGtC,SAAS,CAAC,aAAa,CAAC,CAAA;AAC5C,EAAA,IAAMuC,YAAY,GAAGvC,SAAS,CAAC,cAAc,CAAC,CAAA;AAC9C,EAAA,IAAMwC,aAAa,GAAGxC,SAAS,CAAC,eAAe,CAAC,CAAA;AAChD,EAAA,IAAMyC,WAAW,GAAGzC,SAAS,CAAC,aAAa,CAAC,CAAA;AAC5C,EAAA,IAAM0C,QAAQ,GAAG1C,SAAS,CAAC,UAAU,CAAC,CAAA;AACtC,EAAA,IAAM2C,SAAS,GAAG3C,SAAS,CAAC,WAAW,CAAC,CAAA;AACxC,EAAA,IAAM4C,SAAS,GAAG5C,SAAS,CAAC,WAAW,CAAC,CAAA;AACxC,EAAA,IAAM6C,MAAM,GAAG7C,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAM8C,GAAG,GAAG9C,SAAS,CAAC,KAAK,CAAC,CAAA;AAC5B,EAAA,IAAM+C,kBAAkB,GAAG/C,SAAS,CAAC,oBAAoB,CAAC,CAAA;AAC1D,EAAA,IAAMgD,0BAA0B,GAAGhD,SAAS,CAAC,4BAA4B,CAAC,CAAA;AAC1E,EAAA,IAAMiD,KAAK,GAAGjD,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMkD,MAAM,GAAGlD,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAMmD,OAAO,GAAGnD,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAMoD,OAAO,GAAGpD,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAMqD,QAAQ,GAAGrD,SAAS,CAAC,UAAU,CAAC,CAAA;AACtC,EAAA,IAAMsD,KAAK,GAAGtD,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMuD,KAAK,GAAGvD,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMwD,IAAI,GAAGxD,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9B,EAAA,IAAMyD,KAAK,GAAGzD,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAM0D,SAAS,GAAG1D,SAAS,CAAC,WAAW,CAAC,CAAA;EAExC,IAAM2D,aAAa,GAAG,SAAhBA,aAAa,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAU,CAAA;AAAA,GAAA,CAAA;EAChE,IAAMC,WAAW,GAAG,SAAdA,WAAW,GAAA;IAAA,OAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EAClF,IAAMC,8BAA8B,GAAG,SAAjCA,8BAA8B,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAEnC,gBAAgB,EAAES,OAAO,CAAU,CAAA;AAAA,GAAA,CAAA;EACzF,IAAM2B,uBAAuB,GAAG,SAA1BA,uBAAuB,GAAA;AAAA,IAAA,OAAS,CAACpC,gBAAgB,EAAES,OAAO,CAAU,CAAA;AAAA,GAAA,CAAA;EAC1E,IAAM4B,kBAAkB,GAAG,SAArBA,kBAAkB,GAAA;AAAA,IAAA,OAAS,CAAC,EAAE,EAAEvD,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EACxD,IAAMwD,6BAA6B,GAAG,SAAhCA,6BAA6B,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAEvD,QAAQ,EAAEiB,gBAAgB,CAAU,CAAA;AAAA,GAAA,CAAA;EACzF,IAAMuC,YAAY,GAAG,SAAfA,YAAY,GAAA;AAAA,IAAA,OACd,CACI,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,aAAa,EACb,UAAU,EACV,OAAO,EACP,cAAc,EACd,WAAW,EACX,KAAK,CACC,CAAA;AAAA,GAAA,CAAA;EACd,IAAMC,aAAa,GAAG,SAAhBA,aAAa,GAAA;IAAA,OAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAU,CAAA;AAAA,GAAA,CAAA;EACpF,IAAMC,aAAa,GAAG,SAAhBA,aAAa,GAAA;AAAA,IAAA,OACf,CACI,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,KAAK,EACL,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,cAAc,CACR,CAAA;AAAA,GAAA,CAAA;EACd,IAAMC,QAAQ,GAAG,SAAXA,QAAQ,GAAA;AAAA,IAAA,OACV,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAU,CAAA;AAAA,GAAA,CAAA;EACjF,IAAMC,eAAe,GAAG,SAAlBA,eAAe,GAAA;AAAA,IAAA,OAAS,CAAC,EAAE,EAAE,GAAG,EAAE3C,gBAAgB,CAAU,CAAA;AAAA,GAAA,CAAA;EAClE,IAAM4C,SAAS,GAAG,SAAZA,SAAS,GAAA;AAAA,IAAA,OACX,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EACtF,IAAMC,SAAS,GAAG,SAAZA,SAAS,GAAA;AAAA,IAAA,OAAS,CAAC9D,QAAQ,EAAES,iBAAiB,CAAC,CAAA;AAAA,GAAA,CAAA;EACrD,IAAMsD,qBAAqB,GAAG,SAAxBA,qBAAqB,GAAA;AAAA,IAAA,OAAS,CAAC/D,QAAQ,EAAEiB,gBAAgB,CAAC,CAAA;AAAA,GAAA,CAAA;EAEhE,OAAO;AACHxF,IAAAA,SAAS,EAAE,GAAG;AACd9B,IAAAA,KAAK,EAAE;MACH8H,MAAM,EAAE,CAACP,KAAK,CAAC;MACfQ,OAAO,EAAE,CAAC3B,QAAQ,CAAC;MACnB4B,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,EAAER,YAAY,EAAEF,gBAAgB,CAAC;MAClDW,UAAU,EAAEkC,SAAS,EAAE;MACvBjC,WAAW,EAAE,CAACJ,MAAM,CAAC;MACrBK,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAEX,YAAY,EAAEF,gBAAgB,CAAC;MAClEc,aAAa,EAAEsB,uBAAuB,EAAE;MACxCrB,WAAW,EAAEsB,kBAAkB,EAAE;MACjCrB,QAAQ,EAAE6B,SAAS,EAAE;MACrB5B,SAAS,EAAE0B,eAAe,EAAE;MAC5BzB,SAAS,EAAE4B,qBAAqB,EAAE;MAClC3B,MAAM,EAAEwB,eAAe,EAAE;MACzBvB,GAAG,EAAEgB,uBAAuB,EAAE;MAC9Bf,kBAAkB,EAAE,CAACb,MAAM,CAAC;AAC5Bc,MAAAA,0BAA0B,EAAE,CAAC1B,SAAS,EAAEZ,iBAAiB,CAAC;MAC1DuC,KAAK,EAAEY,8BAA8B,EAAE;MACvCX,MAAM,EAAEW,8BAA8B,EAAE;MACxCV,OAAO,EAAEoB,SAAS,EAAE;MACpBnB,OAAO,EAAEU,uBAAuB,EAAE;MAClCT,QAAQ,EAAEkB,SAAS,EAAE;MACrBjB,KAAK,EAAEiB,SAAS,EAAE;MAClBhB,KAAK,EAAEc,eAAe,EAAE;MACxBb,IAAI,EAAEgB,qBAAqB,EAAE;MAC7Bf,KAAK,EAAEK,uBAAuB,EAAE;AAChCJ,MAAAA,SAAS,EAAEI,uBAAuB,EAAA;KACrC;AACDnJ,IAAAA,WAAW,EAAE;AACT;AACA;;;AAGG;AACH8J,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE/C,gBAAgB,CAAA;OAAG,CAAC;AACnE;;;AAGG;MACHgD,SAAS,EAAE,CAAC,WAAW,CAAC;AACxB;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAAC/C,YAAY,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;AAAE,QAAA,aAAa,EAAE0C,SAAS,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,SAAS,EAAA;OAAI,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,CAAA;OAAG,CAAC;AACrF;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAA;AAAC,OAAE,CAAC;AAC5D;;;AAGG;AACHM,MAAAA,GAAG,EAAE,CAAC;AAAEA,QAAAA,GAAG,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAA;AAAC,OAAE,CAAC;AACrC;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CACL,OAAO,EACP,cAAc,EACd,QAAQ,EACR,MAAM,EACN,aAAa,EACb,OAAO,EACP,cAAc,EACd,eAAe,EACf,YAAY,EACZ,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,MAAM,EACN,aAAa,EACb,UAAU,EACV,WAAW,EACX,QAAQ,CACX;AACD;;;AAGG;AACH,MAAA,OAAA,EAAO,CAAC;AAAE,QAAA,OAAA,EAAO,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAA;OAAG,CAAC;AAC7C;;;AAGG;AACHC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAA;OAAG,CAAC;AACrD;;;AAGG;AACHC,MAAAA,SAAS,EAAE,CAAC,SAAS,EAAE,gBAAgB,CAAC;AACxC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAMf,EAAAA,CAAAA,MAAAA,CAAAA,YAAY,EAAE,EAAA,CAAEvC,gBAAgB,CAAA,CAAA;OAAG,CAAC;AACtE;;;AAGG;AACHuD,MAAAA,QAAQ,EAAE,CAAC;AAAEA,QAAAA,QAAQ,EAAErB,WAAW,EAAA;OAAI,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAEA,WAAW,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAEA,WAAW,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACHsB,MAAAA,UAAU,EAAE,CAAC;AAAEA,QAAAA,UAAU,EAAEvB,aAAa,EAAA;OAAI,CAAC;AAC7C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,aAAa,EAAA;OAAI,CAAC;AACrD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,aAAa,EAAA;OAAI,CAAC;AACrD;;;AAGG;MACHwB,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC/D;;;AAGG;AACHlC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACHmC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACnC,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACHoC,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAACpC,KAAK,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHqC,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAACrC,KAAK,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHsC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACtC,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACHuC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAACvC,KAAK,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACHwC,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAACxC,KAAK,CAAA;AAAC,OAAE,CAAC;AACzB;;;AAGG;AACHyC,MAAAA,UAAU,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;AAChD;;;AAGG;AACHC,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAC,MAAM,EAAEnE,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/B;AACA;;;AAGG;AACHoE,MAAAA,KAAK,EAAE,CAAC;AAAEA,QAAAA,KAAK,EAAE/B,8BAA8B,EAAA;OAAI,CAAC;AACpD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAEgC,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,CAAA;OAAG,CAAC;AAC1E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAA;OAAG,CAAC;AAC3D;;;AAGG;AACHA,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAEnE,gBAAgB,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACHoE,MAAAA,IAAI,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAEzB,eAAe,EAAA;OAAI,CAAC;AACnC;;;AAGG;AACH0B,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE1B,eAAe,EAAA;OAAI,CAAC;AACvC;;;AAGG;AACH2B,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAExE,SAAS,CAAA;OAAG,CAAC;AACxD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACG,KAAK,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAEsE,GAAG,EAAE,CAAC,MAAM,EAAE;AAAEC,UAAAA,IAAI,EAAE,CAAC,MAAM,EAAE1E,SAAS,CAAA;SAAG,EAAEE,gBAAgB,CAAA;AAAC,OAAE,CAAC;AACrF;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEsC,6BAA6B,EAAA;OAAI,CAAC;AAC/D;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;AAAE,QAAA,SAAS,EAAEA,6BAA6B,EAAA;OAAI,CAAC;AAC3D;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACrC,KAAK,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAEwE,GAAG,EAAE,CAAC,MAAM,EAAE;UAAED,IAAI,EAAE,CAAC1E,SAAS,CAAA;AAAC,SAAE,EAAEE,gBAAgB,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEsC,6BAA6B,EAAA;OAAI,CAAC;AAC/D;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;AAAE,QAAA,SAAS,EAAEA,6BAA6B,EAAA;OAAI,CAAC;AAC3D;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAA;OAAG,CAAC;AACjF;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAEtC,gBAAgB,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAEA,gBAAgB,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACHoB,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AACrB;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEsD,QAAAA,OAAO,EAAG,CAAA,QAAQ,CAAKhC,CAAAA,MAAAA,CAAAA,QAAQ,EAAE,CAAA;OAAG,CAAC;AAC3D;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AACnF;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAEiC,QAAAA,OAAO,GAAG,QAAQ,CAAA,CAAA,MAAA,CAAKjC,QAAQ,EAAE,GAAE,UAAU,CAAA,CAAA;OAAG,CAAC;AACrE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAEkC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAA;OAAG,CAAC;AACnF;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAE,QAAA,eAAe,EAAMnC,EAAAA,CAAAA,MAAAA,CAAAA,QAAQ,EAAE,EAAA,CAAE,UAAU,CAAA,CAAA;OAAG,CAAC;AACnE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAA;OAAG,CAAC;AACrF;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AAC/E;AACA;;;AAGG;AACHoC,MAAAA,CAAC,EAAE,CAAC;QAAEA,CAAC,EAAE,CAACpD,OAAO,CAAA;AAAC,OAAE,CAAC;AACrB;;;AAGG;AACHqD,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACrD,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHsD,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACtD,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHuD,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACvD,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHwD,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACxD,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHyD,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACzD,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACH0D,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC1D,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACH2D,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC3D,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACH4D,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC5D,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACH6D,MAAAA,CAAC,EAAE,CAAC;QAAEA,CAAC,EAAE,CAAC/D,MAAM,CAAA;AAAC,OAAE,CAAC;AACpB;;;AAGG;AACHgE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAChE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHiE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACjE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHkE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAClE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHmE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACnE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHoE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACpE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHqE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACrE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHsE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACtE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHuE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACvE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACO,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;MACH,iBAAiB,EAAE,CAAC,iBAAiB,CAAC;AACtC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;MACH,iBAAiB,EAAE,CAAC,iBAAiB,CAAC;AACtC;AACA;;;AAGG;AACHiE,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAEhG,gBAAgB,EAAES,OAAO,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAET,gBAAgB,EAAElB,QAAQ,CAAA;OAAG,CAAC;AACzE;;;AAGG;AACH,MAAA,OAAO,EAAE,CACL;AACI,QAAA,OAAO,EAAE,CACL,GAAG,EACH,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP;UAAEmH,MAAM,EAAE,CAAC/F,YAAY,CAAA;SAAG,EAC1BA,YAAY,EACZF,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACHkG,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAClG,gBAAgB,EAAES,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAET,gBAAgB,EAAElB,QAAQ,CAAA;OAAG,CAAC;AACzE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACkB,gBAAgB,EAAES,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAA;OAAG,CAAC;AACxE;AACA;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE0F,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAEjG,YAAY,EAAElB,iBAAiB,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC,aAAa,EAAE,sBAAsB,CAAC;AACzD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;AACtC;;;AAGG;AACH,MAAA,aAAa,EAAE,CACX;QACIoH,IAAI,EAAE,CACF,MAAM,EACN,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,MAAM,EACN,WAAW,EACX,OAAO,EACP5G,iBAAiB,CAAA;AAExB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE4G,IAAI,EAAE,CAACnG,KAAK,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;MACH,YAAY,EAAE,CAAC,aAAa,CAAC;AAC7B;;;AAGG;MACH,aAAa,EAAE,CAAC,SAAS,CAAC;AAC1B;;;AAGG;MACH,kBAAkB,EAAE,CAAC,cAAc,CAAC;AACpC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;AAC9C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;AACpD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC;AAC1D;;;AAGG;AACHoG,MAAAA,QAAQ,EAAE,CACN;AACIA,QAAAA,QAAQ,EAAE,CACN,SAAS,EACT,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,QAAQ,EACRrG,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAE,CAAC,MAAM,EAAEjB,QAAQ,EAAES,iBAAiB,CAAA;OAAG,CAAC;AACvE;;;AAGG;AACH8G,MAAAA,OAAO,EAAE,CACL;AACIA,QAAAA,OAAO,EAAE,CACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,EACPtG,gBAAgB,EAChBlB,QAAQ,CAAA;AAEf,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAE,CAAC,MAAM,EAAEkB,gBAAgB,CAAA;AAAC,OAAE,CAAC;AAC5D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAEuG,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAEvG,gBAAgB,CAAA;OAAG,CAAC;AAC5E;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;AAAEuG,QAAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAA;AAAC,OAAE,CAAC;AACxD;;;;AAIG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAEC,WAAW,EAAE,CAAChG,MAAM,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACiB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE0E,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAA;OAAG,CAAC;AACpF;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC3F,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAACiB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;MACH,iBAAiB,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,CAAC;AAC5E;;;AAGG;AACH,MAAA,uBAAuB,EAAE,CAAC;AAAEgF,QAAAA,UAAU,EAAMjE,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,EAAE,EAAA,CAAE,MAAM,CAAA,CAAA;OAAG,CAAC;AACvE;;;AAGG;AACH,MAAA,2BAA2B,EAAE,CAAC;AAAEiE,QAAAA,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE3H,QAAQ,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;AAAE,QAAA,kBAAkB,EAAE,CAAC,MAAM,EAAEkB,gBAAgB,EAAElB,QAAQ,CAAA;OAAG,CAAC;AAClF;;;AAGG;AACH,MAAA,uBAAuB,EAAE,CAAC;QAAE2H,UAAU,EAAE,CAACjG,MAAM,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;MACH,gBAAgB,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,CAAC;AACzE;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,WAAW,CAAC;AAC3D;;;AAGG;AACHkG,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAEtE,uBAAuB,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CACd;AACIuE,QAAAA,KAAK,EAAE,CACH,UAAU,EACV,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,aAAa,EACb,KAAK,EACL,OAAO,EACP3G,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH4G,MAAAA,UAAU,EAAE,CACR;AAAEA,QAAAA,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAA;AAAG,OAAA,CACtF;AACD;;;AAGG;AACH,MAAA,OAAA,EAAO,CAAC;AAAE,QAAA,OAAA,EAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AACtD;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAA;OAAG,CAAC;AAClD;;;AAGG;AACHlC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAE3E,gBAAgB,CAAA;AAAC,OAAE,CAAC;AAClD;AACA;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAE8G,QAAAA,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAA;OAAG,CAAC;AACvD;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAA;OAAG,CAAC;AACpE;;;;AAIG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACrF,OAAO,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAA;OAAG,CAAC;AAChE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;AAAEqF,QAAAA,EAAE,EAAMvE,EAAAA,CAAAA,MAAAA,CAAAA,YAAY,EAAE,EAAA,CAAElD,mBAAmB,CAAA,CAAA;OAAG,CAAC;AACjE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAEyH,EAAE,EAAE,CAAC,WAAW,EAAE;UAAEC,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAA;SAAG,CAAA;AAAC,OAAE,CAAC;AAClF;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAED,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE3H,eAAe,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,UAAU,EAAE,CACR;QACI2H,EAAE,EAAE,CACA,MAAM,EACN;AAAE,UAAA,aAAa,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAA;AAAG,SAAA,EAC/DxH,cAAc,CAAA;AAErB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAEwH,EAAE,EAAE,CAACtG,MAAM,CAAA;AAAC,OAAE,CAAC;AAC9B;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAEwG,IAAI,EAAE,CAAC1F,0BAA0B,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE2F,GAAG,EAAE,CAAC3F,0BAA0B,CAAA;AAAC,OAAE,CAAC;AAC3D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAE4F,EAAE,EAAE,CAAC5F,0BAA0B,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE0F,IAAI,EAAE,CAAC3F,kBAAkB,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE4F,GAAG,EAAE,CAAC5F,kBAAkB,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE6F,EAAE,EAAE,CAAC7F,kBAAkB,CAAA;AAAC,OAAE,CAAC;AAC7C;AACA;;;AAGG;AACH8F,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAACtG,YAAY,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAEuG,MAAM,EAAE,CAACrG,WAAW,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACU,OAAO,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE2F,QAAAA,MAAM,EAAM5E,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,EAAE,EAAA,CAAE,QAAQ,CAAA,CAAA;OAAG,CAAC;AAC5D;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAE,UAAU,EAAE,CAACzB,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;MACH,kBAAkB,EAAE,CAAC,kBAAkB,CAAC;AACxC;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;MACH,kBAAkB,EAAE,CAAC,kBAAkB,CAAC;AACxC;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACU,OAAO,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE4F,QAAAA,MAAM,EAAE7E,aAAa,EAAA;OAAI,CAAC;AAC7C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE4E,MAAM,EAAE,CAACxG,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAEyG,MAAM,EAAE,CAACzG,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAE0G,QAAAA,OAAO,EAAG,CAAA,EAAE,CAAK9E,CAAAA,MAAAA,CAAAA,aAAa,EAAE,CAAA;OAAG,CAAC;AACxD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAACxC,gBAAgB,EAAElB,QAAQ,CAAA;AAAC,OAAE,CAAC;AACtE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAEwI,OAAO,EAAE,CAACxI,QAAQ,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAEwI,OAAO,EAAE,CAAC9G,MAAM,CAAA;AAAC,OAAE,CAAC;AACxC;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;AAAE+G,QAAAA,IAAI,EAAElF,kBAAkB,EAAA;OAAI,CAAC;AAC1C;;;AAGG;MACH,cAAc,EAAE,CAAC,YAAY,CAAC;AAC9B;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEkF,IAAI,EAAE,CAAC/G,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAACiB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC3C,QAAQ,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC0B,MAAM,CAAA;AAAC,OAAE,CAAC;AAClD;AACA;;;AAGG;AACHgH,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAEtH,YAAY,EAAEC,iBAAiB,CAAA;OAAG,CAAC;AAC5E;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAEqH,MAAM,EAAE,CAACvH,KAAK,CAAA;AAAC,OAAE,CAAC;AACrC;;;AAGG;AACHwB,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAACA,OAAO,CAAA;AAAC,OAAE,CAAC;AACjC;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEgB,aAAa,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEA,aAAa,EAAA;OAAI,CAAC;AAC7C;AACA;;;;AAIG;AACHxF,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACHyD,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAACA,IAAI,CAAA;AAAC,OAAE,CAAC;AACxB;;;AAGG;AACHC,MAAAA,UAAU,EAAE,CAAC;QAAEA,UAAU,EAAE,CAACA,UAAU,CAAA;AAAC,OAAE,CAAC;AAC1C;;;AAGG;AACHK,MAAAA,QAAQ,EAAE,CAAC;QAAEA,QAAQ,EAAE,CAACA,QAAQ,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC,EAAE,EAAE,MAAM,EAAEd,YAAY,EAAEF,gBAAgB,CAAA;OAAG,CAAC;AAChF;;;AAGG;AACHiB,MAAAA,SAAS,EAAE,CAAC;QAAEA,SAAS,EAAE,CAACA,SAAS,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAACA,MAAM,CAAA;AAAC,OAAE,CAAC;AAC9B;;;AAGG;AACHQ,MAAAA,QAAQ,EAAE,CAAC;QAAEA,QAAQ,EAAE,CAACA,QAAQ,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACHE,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;;AAIG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAE,QAAA,iBAAiB,EAAE,CAAC,EAAE,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AACxD;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAACnB,IAAI,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACC,UAAU,CAAA;AAAC,OAAE,CAAC;AAChE;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,mBAAmB,EAAE,CAACK,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACH,MAAA,oBAAoB,EAAE,CAAC;QAAE,oBAAoB,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAE,iBAAiB,EAAE,CAACC,MAAM,CAAA;AAAC,OAAE,CAAC;AACpD;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACM,OAAO,CAAA;AAAC,OAAE,CAAC;AACvD;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,mBAAmB,EAAE,CAACE,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACE,KAAK,CAAA;AAAC,OAAE,CAAC;AACjD;AACA;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEuF,QAAAA,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACtG,aAAa,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACA,aAAa,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACA,aAAa,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE2G,QAAAA,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AACzC;AACA;;;AAGG;AACHC,MAAAA,UAAU,EAAE,CACR;AACIA,QAAAA,UAAU,EAAE,CACR,MAAM,EACN,KAAK,EACL,EAAE,EACF,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,EACX3H,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH4H,MAAAA,QAAQ,EAAE,CAAC;AAAEA,QAAAA,QAAQ,EAAE9E,qBAAqB,EAAA;OAAI,CAAC;AACjD;;;AAGG;AACH+E,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE7H,gBAAgB,CAAA;OAAG,CAAC;AACrE;;;AAGG;AACH8H,MAAAA,KAAK,EAAE,CAAC;AAAEA,QAAAA,KAAK,EAAEhF,qBAAqB,EAAA;OAAI,CAAC;AAC3C;;;AAGG;AACHiF,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE/H,gBAAgB,CAAA;OAAG,CAAC;AACrF;AACA;;;AAGG;AACHgI,MAAAA,SAAS,EAAE,CAAC;AAAEA,QAAAA,SAAS,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AAC/C;;;AAGG;AACHpG,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACHqG,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAACnI,SAAS,EAAEE,gBAAgB,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAACgC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAACA,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;QAAE,QAAQ,EAAE,CAACF,IAAI,CAAA;AAAC,OAAE,CAAC;AAChC;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;QAAE,QAAQ,EAAE,CAACA,IAAI,CAAA;AAAC,OAAE,CAAC;AAChC;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAChB;QACIoG,MAAM,EAAE,CACJ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,OAAO,EACP,cAAc,EACd,QAAQ,EACR,aAAa,EACb,MAAM,EACN,UAAU,EACVlI,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;AACA;;;AAGG;AACHmI,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAAC,MAAM,EAAE3H,MAAM,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;MACH4H,UAAU,EAAE,CAAC,iBAAiB,CAAC;AAC/B;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CACJ;AACIA,QAAAA,MAAM,EAAE,CACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,aAAa,EACb,MAAM,EACN,cAAc,EACd,UAAU,EACV,MAAM,EACN,WAAW,EACX,eAAe,EACf,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,aAAa,EACb,aAAa,EACb,SAAS,EACT,UAAU,EACVrI,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAEsI,KAAK,EAAE,CAAC9H,MAAM,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACH+H,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAA;OAAG,CAAC;AAC5C;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEC,QAAAA,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEpG,uBAAuB,EAAA;OAAI,CAAC;AACvD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACvD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEqG,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAA;OAAG,CAAC;AACnD;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACHC,MAAAA,KAAK,EAAE,CACH;QACIA,KAAK,EAAE,CACH,MAAM,EACN,MAAM,EACN,YAAY,EACZ,cAAc,EACd;AAAEC,UAAAA,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAA;SAAG,CAAA;AAEzD,OAAA,CACJ;AACD;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AACrD;;;AAGG;AACH,MAAA,aAAa,EAAE,CACX;QAAE,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE5I,gBAAgB,CAAA;AAAG,OAAA,CACnF;AACD;AACA;;;AAGG;AACH6I,MAAAA,IAAI,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAACrI,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAEsI,QAAAA,MAAM,EAAE,CAAChK,QAAQ,EAAEU,iBAAiB,CAAA;AAAC,OAAE,CAAC;AACvD;;;AAGG;AACHsJ,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAACtI,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtC;AACA;;;AAGG;AACHuI,MAAAA,EAAE,EAAE,CAAC,SAAS,EAAE,aAAa,CAAA;KAChC;AACDtS,IAAAA,sBAAsB,EAAE;AACpB8M,MAAAA,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACtCC,MAAAA,UAAU,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;AAC5CjC,MAAAA,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC/E,MAAA,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC5B,MAAA,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC5B4C,MAAAA,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjC/C,MAAAA,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;AACvB0D,MAAAA,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACnDC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBO,MAAAA,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACnDC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MAChB,WAAW,EAAE,CAAC,SAAS,CAAC;MACxB,YAAY,EAAE,CACV,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,aAAa,EACb,cAAc,CACjB;MACD,aAAa,EAAE,CAAC,YAAY,CAAC;MAC7B,kBAAkB,EAAE,CAAC,YAAY,CAAC;MAClC,YAAY,EAAE,CAAC,YAAY,CAAC;MAC5B,aAAa,EAAE,CAAC,YAAY,CAAC;MAC7B,cAAc,EAAE,CAAC,YAAY,CAAC;AAC9B0B,MAAAA,OAAO,EAAE,CACL,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,CACf;AACD,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AAC1D,MAAA,UAAU,EAAE,CACR,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,CACf;AACD,MAAA,YAAY,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAA,YAAY,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;MAC1C,cAAc,EAAE,CACZ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,CACnB;AACD,MAAA,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACtD,MAAA,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACtD,MAAA,UAAU,EAAE,CACR,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACd;AACD,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,UAAU,EAAE,CACR,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACd;AACD,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAA;KACzC;AACDzQ,IAAAA,8BAA8B,EAAE;MAC5B,WAAW,EAAE,CAAC,SAAS,CAAA;AAC1B,KAAA;GACsB,CAAA;AAC/B;;ACpvDA;;;AAGG;AACa,SAAAsS,YAAY,CAACC,UAAkB,EAAEC,eAAgC,EAAA;AAC7E,EAAA,KAAK,IAAMxP,GAAG,IAAIwP,eAAe,EAAE;IAC/BC,wBAAwB,CAACF,UAAiB,EAAEvP,GAAG,EAAEwP,eAAe,CAACxP,GAAmB,CAAC,CAAC,CAAA;AACzF,GAAA;AAED,EAAA,OAAOuP,UAAU,CAAA;AACrB,CAAA;AAEA,IAAMG,cAAc,GAAGrQ,MAAM,CAACsQ,SAAS,CAACD,cAAc,CAAA;AACtD,IAAME,aAAa,gBAAG,IAAI7M,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAA;AAE9D,SAAS0M,wBAAwB,CAC7BI,UAAmC,EACnCC,QAAgB,EAChBC,UAAmB,EAAA;EAEnB,IACI,CAACL,cAAc,CAACM,IAAI,CAACH,UAAU,EAAEC,QAAQ,CAAC,IAC1CF,aAAa,CAACxP,GAAG,CAAC,OAAO2P,UAAU,CAAC,IACpCA,UAAU,KAAK,IAAI,EACrB;AACEF,IAAAA,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU,CAAA;AACjC,IAAA,OAAA;AACH,GAAA;AAED,EAAA,IAAIE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACL,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE;AAClED,IAAAA,UAAU,CAACC,QAAQ,CAAC,GAAID,UAAU,CAACC,QAAQ,CAAe,CAACK,MAAM,CAACJ,UAAU,CAAC,CAAA;AAC7E,IAAA,OAAA;AACH,GAAA;AAED,EAAA,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAI,OAAOF,UAAU,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;AAC5E,IAAA,IAAID,UAAU,CAACC,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC/BD,MAAAA,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU,CAAA;AACjC,MAAA,OAAA;AACH,KAAA;AAED,IAAA,KAAK,IAAMK,OAAO,IAAIL,UAAU,EAAE;AAC9BN,MAAAA,wBAAwB,CACpBI,UAAU,CAACC,QAAQ,CAA4B,EAC/CM,OAAO,EACPL,UAAU,CAACK,OAAuB,CAAC,CACtC,CAAA;AACJ,KAAA;AACJ,GAAA;AACL;;SC3CgBC,mBAAmB,CAC/Bb,eAAyD,EAChB;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAAtC3L,YAAsC,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAAtCA,YAAsC,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;AAEzC,EAAA,OAAO,OAAO2L,eAAe,KAAK,UAAU,GACtC5L,mBAAmB,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAACiD,gBAAgB,EAAE2I,eAAe,CAAK3L,CAAAA,MAAAA,CAAAA,YAAY,CAAC,CAAA,GACvED,mBAAmB,CACf,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,YAAA;AAAA,IAAA,OAAM0L,YAAY,CAACzI,gBAAgB,EAAE,EAAE2I,eAAe,CAAC,CAAA;AAAA,GAAA,CAAA,CAAA,MAAA,CACpD3L,YAAY,CAClB,CAAA,CAAA;AACX;;ICdayM,OAAO,gBAAG1M,mBAAmB,CAACiD,gBAAgB;;ACS3D;;AAEG;AACI,IAAMvI,IAAI,GAAGtC;;;;;;;;;;;;"}