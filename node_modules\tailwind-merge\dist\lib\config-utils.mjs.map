{"version": 3, "file": "config-utils.mjs", "sources": ["../../src/lib/config-utils.ts"], "sourcesContent": ["import { createClassUtils } from './class-utils'\nimport { createLruCache } from './lru-cache'\nimport { createSplitModifiers } from './modifier-utils'\nimport { Config } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createConfigUtils(config: Config) {\n    return {\n        cache: createLruCache<string, string>(config.cacheSize),\n        splitModifiers: createSplitModifiers(config),\n        ...createClassUtils(config),\n    }\n}\n"], "names": ["createConfigUtils", "config", "cache", "createLruCache", "cacheSize", "splitModifiers", "createSplitModifiers", "createClassUtils"], "mappings": ";;;;AAOM,SAAUA,iBAAiB,CAACC,MAAc,EAAA;EAC5C,OAAO;AACHC,IAAAA,KAAK,EAAEC,cAAc,CAAiBF,MAAM,CAACG,SAAS,CAAC;AACvDC,IAAAA,cAAc,EAAEC,oBAAoB,CAACL,MAAM,CAAC;IAC5C,GAAGM,gBAAgB,CAACN,MAAM,CAAA;GAC7B,CAAA;AACL;;;;"}