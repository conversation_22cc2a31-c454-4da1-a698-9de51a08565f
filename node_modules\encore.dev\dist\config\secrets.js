import * as runtime from "../internal/runtime/mod.js";
/**
 * secret is used to load a single {@link Secret} into the application.
 *
 * If you wish to load multiple secrets at once, see {@link secrets}.
 *
 * @example loading a single secret
 *  import {secret} from "encore.dev/config/secrets";
 *  const foo = secret<"foo">();
 */
export function secret(name) {
    // Get the secret implementation from the runtime.
    // It reports null if the secret isn't in the runtime config.
    const impl = runtime.RT.secret(name);
    const secretObj = () => {
        if (impl === null) {
            throw new Error(`secret ${name} is not set`);
        }
        return impl.cached();
    };
    secretObj.toString = () => {
        if (impl === null) {
            return `Secret<${name}>(not set)`;
        }
        return `Secret<${name}>(*********)`;
    };
    Object.defineProperty(secretObj, "name", { value: name });
    return secretObj;
}
//# sourceMappingURL=secrets.js.map