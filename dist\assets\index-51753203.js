function Lh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function wd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var vd={exports:{}},Ui={},yd={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ao=Symbol.for("react.element"),jh=Symbol.for("react.portal"),Oh=Symbol.for("react.fragment"),Rh=Symbol.for("react.strict_mode"),zh=Symbol.for("react.profiler"),_h=Symbol.for("react.provider"),Mh=Symbol.for("react.context"),$h=Symbol.for("react.forward_ref"),Dh=Symbol.for("react.suspense"),Bh=Symbol.for("react.memo"),Fh=Symbol.for("react.lazy"),Eu=Symbol.iterator;function Uh(e){return e===null||typeof e!="object"?null:(e=Eu&&e[Eu]||e["@@iterator"],typeof e=="function"?e:null)}var xd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},bd=Object.assign,Cd={};function ur(e,t,n){this.props=e,this.context=t,this.refs=Cd,this.updater=n||xd}ur.prototype.isReactComponent={};ur.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ur.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function kd(){}kd.prototype=ur.prototype;function sa(e,t,n){this.props=e,this.context=t,this.refs=Cd,this.updater=n||xd}var la=sa.prototype=new kd;la.constructor=sa;bd(la,ur.prototype);la.isPureReactComponent=!0;var Tu=Array.isArray,Sd=Object.prototype.hasOwnProperty,aa={current:null},Ed={key:!0,ref:!0,__self:!0,__source:!0};function Td(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Sd.call(t,r)&&!Ed.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:ao,type:e,key:i,ref:s,props:o,_owner:aa.current}}function Vh(e,t){return{$$typeof:ao,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ua(e){return typeof e=="object"&&e!==null&&e.$$typeof===ao}function Hh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Pu=/\/+/g;function hs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Hh(""+e.key):t.toString(36)}function Wo(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ao:case jh:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+hs(s,0):r,Tu(o)?(n="",e!=null&&(n=e.replace(Pu,"$&/")+"/"),Wo(o,t,n,"",function(u){return u})):o!=null&&(ua(o)&&(o=Vh(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Pu,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Tu(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+hs(i,l);s+=Wo(i,t,n,a,o)}else if(a=Uh(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+hs(i,l++),s+=Wo(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Co(e,t,n){if(e==null)return e;var r=[],o=0;return Wo(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Wh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ie={current:null},Ko={transition:null},Kh={ReactCurrentDispatcher:Ie,ReactCurrentBatchConfig:Ko,ReactCurrentOwner:aa};function Pd(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:Co,forEach:function(e,t,n){Co(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Co(e,function(){t++}),t},toArray:function(e){return Co(e,function(t){return t})||[]},only:function(e){if(!ua(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=ur;D.Fragment=Oh;D.Profiler=zh;D.PureComponent=sa;D.StrictMode=Rh;D.Suspense=Dh;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kh;D.act=Pd;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=bd({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=aa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Sd.call(t,a)&&!Ed.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:ao,type:e.type,key:o,ref:i,props:r,_owner:s}};D.createContext=function(e){return e={$$typeof:Mh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:_h,_context:e},e.Consumer=e};D.createElement=Td;D.createFactory=function(e){var t=Td.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:$h,render:e}};D.isValidElement=ua;D.lazy=function(e){return{$$typeof:Fh,_payload:{_status:-1,_result:e},_init:Wh}};D.memo=function(e,t){return{$$typeof:Bh,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=Ko.transition;Ko.transition={};try{e()}finally{Ko.transition=t}};D.unstable_act=Pd;D.useCallback=function(e,t){return Ie.current.useCallback(e,t)};D.useContext=function(e){return Ie.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return Ie.current.useDeferredValue(e)};D.useEffect=function(e,t){return Ie.current.useEffect(e,t)};D.useId=function(){return Ie.current.useId()};D.useImperativeHandle=function(e,t,n){return Ie.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return Ie.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return Ie.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return Ie.current.useMemo(e,t)};D.useReducer=function(e,t,n){return Ie.current.useReducer(e,t,n)};D.useRef=function(e){return Ie.current.useRef(e)};D.useState=function(e){return Ie.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return Ie.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return Ie.current.useTransition()};D.version="18.3.1";yd.exports=D;var b=yd.exports;const bt=wd(b),Nd=Lh({__proto__:null,default:bt},[b]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qh=b,Gh=Symbol.for("react.element"),Qh=Symbol.for("react.fragment"),Xh=Object.prototype.hasOwnProperty,Yh=qh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zh={key:!0,ref:!0,__self:!0,__source:!0};function Id(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Xh.call(t,r)&&!Zh.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Gh,type:e,key:i,ref:s,props:o,_owner:Yh.current}}Ui.Fragment=Qh;Ui.jsx=Id;Ui.jsxs=Id;vd.exports=Ui;var f=vd.exports,Xs={},Ad={exports:{}},We={},Ld={exports:{}},jd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,L){var _=I.length;I.push(L);e:for(;0<_;){var H=_-1>>>1,q=I[H];if(0<o(q,L))I[H]=L,I[_]=q,_=H;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var L=I[0],_=I.pop();if(_!==L){I[0]=_;e:for(var H=0,q=I.length,ee=q>>>1;H<ee;){var ft=2*(H+1)-1,ms=I[ft],sn=ft+1,bo=I[sn];if(0>o(ms,_))sn<q&&0>o(bo,ms)?(I[H]=bo,I[sn]=_,H=sn):(I[H]=ms,I[ft]=_,H=ft);else if(sn<q&&0>o(bo,_))I[H]=bo,I[sn]=_,H=sn;else break e}}return L}function o(I,L){var _=I.sortIndex-L.sortIndex;return _!==0?_:I.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],h=1,p=null,v=3,g=!1,w=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,c=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(I){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=I)r(u),L.sortIndex=L.expirationTime,t(a,L);else break;L=n(u)}}function C(I){if(y=!1,m(I),!w)if(n(a)!==null)w=!0,ge(S);else{var L=n(u);L!==null&&Qe(C,L.startTime-I)}}function S(I,L){w=!1,y&&(y=!1,c(N),N=-1),g=!0;var _=v;try{for(m(L),p=n(a);p!==null&&(!(p.expirationTime>L)||I&&!K());){var H=p.callback;if(typeof H=="function"){p.callback=null,v=p.priorityLevel;var q=H(p.expirationTime<=L);L=e.unstable_now(),typeof q=="function"?p.callback=q:p===n(a)&&r(a),m(L)}else r(a);p=n(a)}if(p!==null)var ee=!0;else{var ft=n(u);ft!==null&&Qe(C,ft.startTime-L),ee=!1}return ee}finally{p=null,v=_,g=!1}}var T=!1,E=null,N=-1,M=5,O=-1;function K(){return!(e.unstable_now()-O<M)}function $(){if(E!==null){var I=e.unstable_now();O=I;var L=!0;try{L=E(!0,I)}finally{L?Ee():(T=!1,E=null)}}else T=!1}var Ee;if(typeof d=="function")Ee=function(){d($)};else if(typeof MessageChannel<"u"){var z=new MessageChannel,he=z.port2;z.port1.onmessage=$,Ee=function(){he.postMessage(null)}}else Ee=function(){x($,0)};function ge(I){E=I,T||(T=!0,Ee())}function Qe(I,L){N=x(function(){I(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,ge(S))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(I){switch(v){case 1:case 2:case 3:var L=3;break;default:L=v}var _=v;v=L;try{return I()}finally{v=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,L){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var _=v;v=I;try{return L()}finally{v=_}},e.unstable_scheduleCallback=function(I,L,_){var H=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?H+_:H):_=H,I){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=_+q,I={id:h++,callback:L,priorityLevel:I,startTime:_,expirationTime:q,sortIndex:-1},_>H?(I.sortIndex=_,t(u,I),n(a)===null&&I===n(u)&&(y?(c(N),N=-1):y=!0,Qe(C,_-H))):(I.sortIndex=q,t(a,I),w||g||(w=!0,ge(S))),I},e.unstable_shouldYield=K,e.unstable_wrapCallback=function(I){var L=v;return function(){var _=v;v=L;try{return I.apply(this,arguments)}finally{v=_}}}})(jd);Ld.exports=jd;var Jh=Ld.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var eg=b,Ve=Jh;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Od=new Set,Ur={};function Nn(e,t){er(e,t),er(e+"Capture",t)}function er(e,t){for(Ur[e]=t,e=0;e<t.length;e++)Od.add(t[e])}var At=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ys=Object.prototype.hasOwnProperty,tg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Nu={},Iu={};function ng(e){return Ys.call(Iu,e)?!0:Ys.call(Nu,e)?!1:tg.test(e)?Iu[e]=!0:(Nu[e]=!0,!1)}function rg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function og(e,t,n,r){if(t===null||typeof t>"u"||rg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ae(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new Ae(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new Ae(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new Ae(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new Ae(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new Ae(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new Ae(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new Ae(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new Ae(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new Ae(e,5,!1,e.toLowerCase(),null,!1,!1)});var ca=/[\-:]([a-z])/g;function da(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ca,da);ye[t]=new Ae(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ca,da);ye[t]=new Ae(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ca,da);ye[t]=new Ae(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new Ae(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new Ae(e,1,!1,e.toLowerCase(),null,!0,!0)});function pa(e,t,n,r){var o=ye.hasOwnProperty(t)?ye[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(og(t,n,o,r)&&(n=null),r||o===null?ng(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Rt=eg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ko=Symbol.for("react.element"),Rn=Symbol.for("react.portal"),zn=Symbol.for("react.fragment"),fa=Symbol.for("react.strict_mode"),Zs=Symbol.for("react.profiler"),Rd=Symbol.for("react.provider"),zd=Symbol.for("react.context"),ma=Symbol.for("react.forward_ref"),Js=Symbol.for("react.suspense"),el=Symbol.for("react.suspense_list"),ha=Symbol.for("react.memo"),Mt=Symbol.for("react.lazy"),_d=Symbol.for("react.offscreen"),Au=Symbol.iterator;function vr(e){return e===null||typeof e!="object"?null:(e=Au&&e[Au]||e["@@iterator"],typeof e=="function"?e:null)}var re=Object.assign,gs;function Nr(e){if(gs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);gs=t&&t[1]||""}return`
`+gs+e}var ws=!1;function vs(e,t){if(!e||ws)return"";ws=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{ws=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Nr(e):""}function ig(e){switch(e.tag){case 5:return Nr(e.type);case 16:return Nr("Lazy");case 13:return Nr("Suspense");case 19:return Nr("SuspenseList");case 0:case 2:case 15:return e=vs(e.type,!1),e;case 11:return e=vs(e.type.render,!1),e;case 1:return e=vs(e.type,!0),e;default:return""}}function tl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case zn:return"Fragment";case Rn:return"Portal";case Zs:return"Profiler";case fa:return"StrictMode";case Js:return"Suspense";case el:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case zd:return(e.displayName||"Context")+".Consumer";case Rd:return(e._context.displayName||"Context")+".Provider";case ma:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ha:return t=e.displayName||null,t!==null?t:tl(e.type)||"Memo";case Mt:t=e._payload,e=e._init;try{return tl(e(t))}catch{}}return null}function sg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return tl(t);case 8:return t===fa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function en(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Md(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function lg(e){var t=Md(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function So(e){e._valueTracker||(e._valueTracker=lg(e))}function $d(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Md(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function si(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function nl(e,t){var n=t.checked;return re({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Lu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=en(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Dd(e,t){t=t.checked,t!=null&&pa(e,"checked",t,!1)}function rl(e,t){Dd(e,t);var n=en(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ol(e,t.type,n):t.hasOwnProperty("defaultValue")&&ol(e,t.type,en(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ju(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ol(e,t,n){(t!=="number"||si(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ir=Array.isArray;function Kn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+en(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function il(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return re({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ou(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Ir(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:en(n)}}function Bd(e,t){var n=en(t.value),r=en(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ru(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function sl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Eo,Ud=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Eo=Eo||document.createElement("div"),Eo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Eo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var jr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ag=["Webkit","ms","Moz","O"];Object.keys(jr).forEach(function(e){ag.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),jr[t]=jr[e]})});function Vd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||jr.hasOwnProperty(e)&&jr[e]?(""+t).trim():t+"px"}function Hd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Vd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var ug=re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ll(e,t){if(t){if(ug[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function al(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ul=null;function ga(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var cl=null,qn=null,Gn=null;function zu(e){if(e=po(e)){if(typeof cl!="function")throw Error(P(280));var t=e.stateNode;t&&(t=qi(t),cl(e.stateNode,e.type,t))}}function Wd(e){qn?Gn?Gn.push(e):Gn=[e]:qn=e}function Kd(){if(qn){var e=qn,t=Gn;if(Gn=qn=null,zu(e),t)for(e=0;e<t.length;e++)zu(t[e])}}function qd(e,t){return e(t)}function Gd(){}var ys=!1;function Qd(e,t,n){if(ys)return e(t,n);ys=!0;try{return qd(e,t,n)}finally{ys=!1,(qn!==null||Gn!==null)&&(Gd(),Kd())}}function Hr(e,t){var n=e.stateNode;if(n===null)return null;var r=qi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var dl=!1;if(At)try{var yr={};Object.defineProperty(yr,"passive",{get:function(){dl=!0}}),window.addEventListener("test",yr,yr),window.removeEventListener("test",yr,yr)}catch{dl=!1}function cg(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(h){this.onError(h)}}var Or=!1,li=null,ai=!1,pl=null,dg={onError:function(e){Or=!0,li=e}};function pg(e,t,n,r,o,i,s,l,a){Or=!1,li=null,cg.apply(dg,arguments)}function fg(e,t,n,r,o,i,s,l,a){if(pg.apply(this,arguments),Or){if(Or){var u=li;Or=!1,li=null}else throw Error(P(198));ai||(ai=!0,pl=u)}}function In(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _u(e){if(In(e)!==e)throw Error(P(188))}function mg(e){var t=e.alternate;if(!t){if(t=In(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return _u(o),e;if(i===r)return _u(o),t;i=i.sibling}throw Error(P(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function Yd(e){return e=mg(e),e!==null?Zd(e):null}function Zd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Zd(e);if(t!==null)return t;e=e.sibling}return null}var Jd=Ve.unstable_scheduleCallback,Mu=Ve.unstable_cancelCallback,hg=Ve.unstable_shouldYield,gg=Ve.unstable_requestPaint,se=Ve.unstable_now,wg=Ve.unstable_getCurrentPriorityLevel,wa=Ve.unstable_ImmediatePriority,ep=Ve.unstable_UserBlockingPriority,ui=Ve.unstable_NormalPriority,vg=Ve.unstable_LowPriority,tp=Ve.unstable_IdlePriority,Vi=null,vt=null;function yg(e){if(vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(Vi,e,void 0,(e.current.flags&128)===128)}catch{}}var ut=Math.clz32?Math.clz32:Cg,xg=Math.log,bg=Math.LN2;function Cg(e){return e>>>=0,e===0?32:31-(xg(e)/bg|0)|0}var To=64,Po=4194304;function Ar(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ci(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=Ar(l):(i&=s,i!==0&&(r=Ar(i)))}else s=n&~o,s!==0?r=Ar(s):i!==0&&(r=Ar(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ut(t),o=1<<n,r|=e[n],t&=~o;return r}function kg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Sg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-ut(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=kg(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function fl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function np(){var e=To;return To<<=1,!(To&4194240)&&(To=64),e}function xs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function uo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ut(t),e[t]=n}function Eg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-ut(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function va(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ut(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var W=0;function rp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var op,ya,ip,sp,lp,ml=!1,No=[],Kt=null,qt=null,Gt=null,Wr=new Map,Kr=new Map,Dt=[],Tg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function $u(e,t){switch(e){case"focusin":case"focusout":Kt=null;break;case"dragenter":case"dragleave":qt=null;break;case"mouseover":case"mouseout":Gt=null;break;case"pointerover":case"pointerout":Wr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Kr.delete(t.pointerId)}}function xr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=po(t),t!==null&&ya(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Pg(e,t,n,r,o){switch(t){case"focusin":return Kt=xr(Kt,e,t,n,r,o),!0;case"dragenter":return qt=xr(qt,e,t,n,r,o),!0;case"mouseover":return Gt=xr(Gt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Wr.set(i,xr(Wr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Kr.set(i,xr(Kr.get(i)||null,e,t,n,r,o)),!0}return!1}function ap(e){var t=cn(e.target);if(t!==null){var n=In(t);if(n!==null){if(t=n.tag,t===13){if(t=Xd(n),t!==null){e.blockedOn=t,lp(e.priority,function(){ip(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function qo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=hl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ul=r,n.target.dispatchEvent(r),ul=null}else return t=po(n),t!==null&&ya(t),e.blockedOn=n,!1;t.shift()}return!0}function Du(e,t,n){qo(e)&&n.delete(t)}function Ng(){ml=!1,Kt!==null&&qo(Kt)&&(Kt=null),qt!==null&&qo(qt)&&(qt=null),Gt!==null&&qo(Gt)&&(Gt=null),Wr.forEach(Du),Kr.forEach(Du)}function br(e,t){e.blockedOn===t&&(e.blockedOn=null,ml||(ml=!0,Ve.unstable_scheduleCallback(Ve.unstable_NormalPriority,Ng)))}function qr(e){function t(o){return br(o,e)}if(0<No.length){br(No[0],e);for(var n=1;n<No.length;n++){var r=No[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Kt!==null&&br(Kt,e),qt!==null&&br(qt,e),Gt!==null&&br(Gt,e),Wr.forEach(t),Kr.forEach(t),n=0;n<Dt.length;n++)r=Dt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Dt.length&&(n=Dt[0],n.blockedOn===null);)ap(n),n.blockedOn===null&&Dt.shift()}var Qn=Rt.ReactCurrentBatchConfig,di=!0;function Ig(e,t,n,r){var o=W,i=Qn.transition;Qn.transition=null;try{W=1,xa(e,t,n,r)}finally{W=o,Qn.transition=i}}function Ag(e,t,n,r){var o=W,i=Qn.transition;Qn.transition=null;try{W=4,xa(e,t,n,r)}finally{W=o,Qn.transition=i}}function xa(e,t,n,r){if(di){var o=hl(e,t,n,r);if(o===null)As(e,t,r,pi,n),$u(e,r);else if(Pg(o,e,t,n,r))r.stopPropagation();else if($u(e,r),t&4&&-1<Tg.indexOf(e)){for(;o!==null;){var i=po(o);if(i!==null&&op(i),i=hl(e,t,n,r),i===null&&As(e,t,r,pi,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else As(e,t,r,null,n)}}var pi=null;function hl(e,t,n,r){if(pi=null,e=ga(r),e=cn(e),e!==null)if(t=In(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return pi=e,null}function up(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(wg()){case wa:return 1;case ep:return 4;case ui:case vg:return 16;case tp:return 536870912;default:return 16}default:return 16}}var Ft=null,ba=null,Go=null;function cp(){if(Go)return Go;var e,t=ba,n=t.length,r,o="value"in Ft?Ft.value:Ft.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Go=o.slice(e,1<r?1-r:void 0)}function Qo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Io(){return!0}function Bu(){return!1}function Ke(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Io:Bu,this.isPropagationStopped=Bu,this}return re(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Io)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Io)},persist:function(){},isPersistent:Io}),t}var cr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ca=Ke(cr),co=re({},cr,{view:0,detail:0}),Lg=Ke(co),bs,Cs,Cr,Hi=re({},co,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ka,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cr&&(Cr&&e.type==="mousemove"?(bs=e.screenX-Cr.screenX,Cs=e.screenY-Cr.screenY):Cs=bs=0,Cr=e),bs)},movementY:function(e){return"movementY"in e?e.movementY:Cs}}),Fu=Ke(Hi),jg=re({},Hi,{dataTransfer:0}),Og=Ke(jg),Rg=re({},co,{relatedTarget:0}),ks=Ke(Rg),zg=re({},cr,{animationName:0,elapsedTime:0,pseudoElement:0}),_g=Ke(zg),Mg=re({},cr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$g=Ke(Mg),Dg=re({},cr,{data:0}),Uu=Ke(Dg),Bg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ug={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ug[e])?!!t[e]:!1}function ka(){return Vg}var Hg=re({},co,{key:function(e){if(e.key){var t=Bg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Qo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ka,charCode:function(e){return e.type==="keypress"?Qo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wg=Ke(Hg),Kg=re({},Hi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vu=Ke(Kg),qg=re({},co,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ka}),Gg=Ke(qg),Qg=re({},cr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Xg=Ke(Qg),Yg=re({},Hi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Zg=Ke(Yg),Jg=[9,13,27,32],Sa=At&&"CompositionEvent"in window,Rr=null;At&&"documentMode"in document&&(Rr=document.documentMode);var ew=At&&"TextEvent"in window&&!Rr,dp=At&&(!Sa||Rr&&8<Rr&&11>=Rr),Hu=String.fromCharCode(32),Wu=!1;function pp(e,t){switch(e){case"keyup":return Jg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var _n=!1;function tw(e,t){switch(e){case"compositionend":return fp(t);case"keypress":return t.which!==32?null:(Wu=!0,Hu);case"textInput":return e=t.data,e===Hu&&Wu?null:e;default:return null}}function nw(e,t){if(_n)return e==="compositionend"||!Sa&&pp(e,t)?(e=cp(),Go=ba=Ft=null,_n=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dp&&t.locale!=="ko"?null:t.data;default:return null}}var rw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ku(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!rw[e.type]:t==="textarea"}function mp(e,t,n,r){Wd(r),t=fi(t,"onChange"),0<t.length&&(n=new Ca("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zr=null,Gr=null;function ow(e){Ep(e,0)}function Wi(e){var t=Dn(e);if($d(t))return e}function iw(e,t){if(e==="change")return t}var hp=!1;if(At){var Ss;if(At){var Es="oninput"in document;if(!Es){var qu=document.createElement("div");qu.setAttribute("oninput","return;"),Es=typeof qu.oninput=="function"}Ss=Es}else Ss=!1;hp=Ss&&(!document.documentMode||9<document.documentMode)}function Gu(){zr&&(zr.detachEvent("onpropertychange",gp),Gr=zr=null)}function gp(e){if(e.propertyName==="value"&&Wi(Gr)){var t=[];mp(t,Gr,e,ga(e)),Qd(ow,t)}}function sw(e,t,n){e==="focusin"?(Gu(),zr=t,Gr=n,zr.attachEvent("onpropertychange",gp)):e==="focusout"&&Gu()}function lw(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Wi(Gr)}function aw(e,t){if(e==="click")return Wi(t)}function uw(e,t){if(e==="input"||e==="change")return Wi(t)}function cw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dt=typeof Object.is=="function"?Object.is:cw;function Qr(e,t){if(dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ys.call(t,o)||!dt(e[o],t[o]))return!1}return!0}function Qu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xu(e,t){var n=Qu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Qu(n)}}function wp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?wp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function vp(){for(var e=window,t=si();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=si(e.document)}return t}function Ea(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function dw(e){var t=vp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&wp(n.ownerDocument.documentElement,n)){if(r!==null&&Ea(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Xu(n,i);var s=Xu(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var pw=At&&"documentMode"in document&&11>=document.documentMode,Mn=null,gl=null,_r=null,wl=!1;function Yu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wl||Mn==null||Mn!==si(r)||(r=Mn,"selectionStart"in r&&Ea(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),_r&&Qr(_r,r)||(_r=r,r=fi(gl,"onSelect"),0<r.length&&(t=new Ca("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Mn)))}function Ao(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $n={animationend:Ao("Animation","AnimationEnd"),animationiteration:Ao("Animation","AnimationIteration"),animationstart:Ao("Animation","AnimationStart"),transitionend:Ao("Transition","TransitionEnd")},Ts={},yp={};At&&(yp=document.createElement("div").style,"AnimationEvent"in window||(delete $n.animationend.animation,delete $n.animationiteration.animation,delete $n.animationstart.animation),"TransitionEvent"in window||delete $n.transitionend.transition);function Ki(e){if(Ts[e])return Ts[e];if(!$n[e])return e;var t=$n[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in yp)return Ts[e]=t[n];return e}var xp=Ki("animationend"),bp=Ki("animationiteration"),Cp=Ki("animationstart"),kp=Ki("transitionend"),Sp=new Map,Zu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function nn(e,t){Sp.set(e,t),Nn(t,[e])}for(var Ps=0;Ps<Zu.length;Ps++){var Ns=Zu[Ps],fw=Ns.toLowerCase(),mw=Ns[0].toUpperCase()+Ns.slice(1);nn(fw,"on"+mw)}nn(xp,"onAnimationEnd");nn(bp,"onAnimationIteration");nn(Cp,"onAnimationStart");nn("dblclick","onDoubleClick");nn("focusin","onFocus");nn("focusout","onBlur");nn(kp,"onTransitionEnd");er("onMouseEnter",["mouseout","mouseover"]);er("onMouseLeave",["mouseout","mouseover"]);er("onPointerEnter",["pointerout","pointerover"]);er("onPointerLeave",["pointerout","pointerover"]);Nn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Nn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Nn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Nn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Nn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Nn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),hw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Lr));function Ju(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,fg(r,t,void 0,e),e.currentTarget=null}function Ep(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;Ju(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;Ju(o,l,u),i=a}}}if(ai)throw e=pl,ai=!1,pl=null,e}function Y(e,t){var n=t[Cl];n===void 0&&(n=t[Cl]=new Set);var r=e+"__bubble";n.has(r)||(Tp(t,e,2,!1),n.add(r))}function Is(e,t,n){var r=0;t&&(r|=4),Tp(n,e,r,t)}var Lo="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[Lo]){e[Lo]=!0,Od.forEach(function(n){n!=="selectionchange"&&(hw.has(n)||Is(n,!1,e),Is(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Lo]||(t[Lo]=!0,Is("selectionchange",!1,t))}}function Tp(e,t,n,r){switch(up(t)){case 1:var o=Ig;break;case 4:o=Ag;break;default:o=xa}n=o.bind(null,t,n,e),o=void 0,!dl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function As(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=cn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Qd(function(){var u=i,h=ga(n),p=[];e:{var v=Sp.get(e);if(v!==void 0){var g=Ca,w=e;switch(e){case"keypress":if(Qo(n)===0)break e;case"keydown":case"keyup":g=Wg;break;case"focusin":w="focus",g=ks;break;case"focusout":w="blur",g=ks;break;case"beforeblur":case"afterblur":g=ks;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Og;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Gg;break;case xp:case bp:case Cp:g=_g;break;case kp:g=Xg;break;case"scroll":g=Lg;break;case"wheel":g=Zg;break;case"copy":case"cut":case"paste":g=$g;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Vu}var y=(t&4)!==0,x=!y&&e==="scroll",c=y?v!==null?v+"Capture":null:v;y=[];for(var d=u,m;d!==null;){m=d;var C=m.stateNode;if(m.tag===5&&C!==null&&(m=C,c!==null&&(C=Hr(d,c),C!=null&&y.push(Yr(d,C,m)))),x)break;d=d.return}0<y.length&&(v=new g(v,w,null,n,h),p.push({event:v,listeners:y}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",v&&n!==ul&&(w=n.relatedTarget||n.fromElement)&&(cn(w)||w[Lt]))break e;if((g||v)&&(v=h.window===h?h:(v=h.ownerDocument)?v.defaultView||v.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=u,w=w?cn(w):null,w!==null&&(x=In(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=u),g!==w)){if(y=Fu,C="onMouseLeave",c="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(y=Vu,C="onPointerLeave",c="onPointerEnter",d="pointer"),x=g==null?v:Dn(g),m=w==null?v:Dn(w),v=new y(C,d+"leave",g,n,h),v.target=x,v.relatedTarget=m,C=null,cn(h)===u&&(y=new y(c,d+"enter",w,n,h),y.target=m,y.relatedTarget=x,C=y),x=C,g&&w)t:{for(y=g,c=w,d=0,m=y;m;m=On(m))d++;for(m=0,C=c;C;C=On(C))m++;for(;0<d-m;)y=On(y),d--;for(;0<m-d;)c=On(c),m--;for(;d--;){if(y===c||c!==null&&y===c.alternate)break t;y=On(y),c=On(c)}y=null}else y=null;g!==null&&ec(p,v,g,y,!1),w!==null&&x!==null&&ec(p,x,w,y,!0)}}e:{if(v=u?Dn(u):window,g=v.nodeName&&v.nodeName.toLowerCase(),g==="select"||g==="input"&&v.type==="file")var S=iw;else if(Ku(v))if(hp)S=uw;else{S=lw;var T=sw}else(g=v.nodeName)&&g.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(S=aw);if(S&&(S=S(e,u))){mp(p,S,n,h);break e}T&&T(e,v,u),e==="focusout"&&(T=v._wrapperState)&&T.controlled&&v.type==="number"&&ol(v,"number",v.value)}switch(T=u?Dn(u):window,e){case"focusin":(Ku(T)||T.contentEditable==="true")&&(Mn=T,gl=u,_r=null);break;case"focusout":_r=gl=Mn=null;break;case"mousedown":wl=!0;break;case"contextmenu":case"mouseup":case"dragend":wl=!1,Yu(p,n,h);break;case"selectionchange":if(pw)break;case"keydown":case"keyup":Yu(p,n,h)}var E;if(Sa)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else _n?pp(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(dp&&n.locale!=="ko"&&(_n||N!=="onCompositionStart"?N==="onCompositionEnd"&&_n&&(E=cp()):(Ft=h,ba="value"in Ft?Ft.value:Ft.textContent,_n=!0)),T=fi(u,N),0<T.length&&(N=new Uu(N,e,null,n,h),p.push({event:N,listeners:T}),E?N.data=E:(E=fp(n),E!==null&&(N.data=E)))),(E=ew?tw(e,n):nw(e,n))&&(u=fi(u,"onBeforeInput"),0<u.length&&(h=new Uu("onBeforeInput","beforeinput",null,n,h),p.push({event:h,listeners:u}),h.data=E))}Ep(p,t)})}function Yr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function fi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Hr(e,n),i!=null&&r.unshift(Yr(e,i,o)),i=Hr(e,t),i!=null&&r.push(Yr(e,i,o))),e=e.return}return r}function On(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ec(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Hr(n,i),a!=null&&s.unshift(Yr(n,a,l))):o||(a=Hr(n,i),a!=null&&s.push(Yr(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var gw=/\r\n?/g,ww=/\u0000|\uFFFD/g;function tc(e){return(typeof e=="string"?e:""+e).replace(gw,`
`).replace(ww,"")}function jo(e,t,n){if(t=tc(t),tc(e)!==t&&n)throw Error(P(425))}function mi(){}var vl=null,yl=null;function xl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var bl=typeof setTimeout=="function"?setTimeout:void 0,vw=typeof clearTimeout=="function"?clearTimeout:void 0,nc=typeof Promise=="function"?Promise:void 0,yw=typeof queueMicrotask=="function"?queueMicrotask:typeof nc<"u"?function(e){return nc.resolve(null).then(e).catch(xw)}:bl;function xw(e){setTimeout(function(){throw e})}function Ls(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),qr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);qr(t)}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function rc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var dr=Math.random().toString(36).slice(2),wt="__reactFiber$"+dr,Zr="__reactProps$"+dr,Lt="__reactContainer$"+dr,Cl="__reactEvents$"+dr,bw="__reactListeners$"+dr,Cw="__reactHandles$"+dr;function cn(e){var t=e[wt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Lt]||n[wt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=rc(e);e!==null;){if(n=e[wt])return n;e=rc(e)}return t}e=n,n=e.parentNode}return null}function po(e){return e=e[wt]||e[Lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Dn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function qi(e){return e[Zr]||null}var kl=[],Bn=-1;function rn(e){return{current:e}}function Z(e){0>Bn||(e.current=kl[Bn],kl[Bn]=null,Bn--)}function G(e,t){Bn++,kl[Bn]=e.current,e.current=t}var tn={},ke=rn(tn),ze=rn(!1),yn=tn;function tr(e,t){var n=e.type.contextTypes;if(!n)return tn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function _e(e){return e=e.childContextTypes,e!=null}function hi(){Z(ze),Z(ke)}function oc(e,t,n){if(ke.current!==tn)throw Error(P(168));G(ke,t),G(ze,n)}function Pp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(P(108,sg(e)||"Unknown",o));return re({},n,r)}function gi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tn,yn=ke.current,G(ke,e),G(ze,ze.current),!0}function ic(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Pp(e,t,yn),r.__reactInternalMemoizedMergedChildContext=e,Z(ze),Z(ke),G(ke,e)):Z(ze),G(ze,n)}var St=null,Gi=!1,js=!1;function Np(e){St===null?St=[e]:St.push(e)}function kw(e){Gi=!0,Np(e)}function on(){if(!js&&St!==null){js=!0;var e=0,t=W;try{var n=St;for(W=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,Gi=!1}catch(o){throw St!==null&&(St=St.slice(e+1)),Jd(wa,on),o}finally{W=t,js=!1}}return null}var Fn=[],Un=0,wi=null,vi=0,Xe=[],Ye=0,xn=null,Et=1,Tt="";function an(e,t){Fn[Un++]=vi,Fn[Un++]=wi,wi=e,vi=t}function Ip(e,t,n){Xe[Ye++]=Et,Xe[Ye++]=Tt,Xe[Ye++]=xn,xn=e;var r=Et;e=Tt;var o=32-ut(r)-1;r&=~(1<<o),n+=1;var i=32-ut(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Et=1<<32-ut(t)+o|n<<o|r,Tt=i+e}else Et=1<<i|n<<o|r,Tt=e}function Ta(e){e.return!==null&&(an(e,1),Ip(e,1,0))}function Pa(e){for(;e===wi;)wi=Fn[--Un],Fn[Un]=null,vi=Fn[--Un],Fn[Un]=null;for(;e===xn;)xn=Xe[--Ye],Xe[Ye]=null,Tt=Xe[--Ye],Xe[Ye]=null,Et=Xe[--Ye],Xe[Ye]=null}var Ue=null,Fe=null,J=!1,at=null;function Ap(e,t){var n=Ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function sc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ue=e,Fe=Qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ue=e,Fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:Et,overflow:Tt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ue=e,Fe=null,!0):!1;default:return!1}}function Sl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function El(e){if(J){var t=Fe;if(t){var n=t;if(!sc(e,t)){if(Sl(e))throw Error(P(418));t=Qt(n.nextSibling);var r=Ue;t&&sc(e,t)?Ap(r,n):(e.flags=e.flags&-4097|2,J=!1,Ue=e)}}else{if(Sl(e))throw Error(P(418));e.flags=e.flags&-4097|2,J=!1,Ue=e}}}function lc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ue=e}function Oo(e){if(e!==Ue)return!1;if(!J)return lc(e),J=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!xl(e.type,e.memoizedProps)),t&&(t=Fe)){if(Sl(e))throw Lp(),Error(P(418));for(;t;)Ap(e,t),t=Qt(t.nextSibling)}if(lc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Fe=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Fe=null}}else Fe=Ue?Qt(e.stateNode.nextSibling):null;return!0}function Lp(){for(var e=Fe;e;)e=Qt(e.nextSibling)}function nr(){Fe=Ue=null,J=!1}function Na(e){at===null?at=[e]:at.push(e)}var Sw=Rt.ReactCurrentBatchConfig;function kr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function Ro(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ac(e){var t=e._init;return t(e._payload)}function jp(e){function t(c,d){if(e){var m=c.deletions;m===null?(c.deletions=[d],c.flags|=16):m.push(d)}}function n(c,d){if(!e)return null;for(;d!==null;)t(c,d),d=d.sibling;return null}function r(c,d){for(c=new Map;d!==null;)d.key!==null?c.set(d.key,d):c.set(d.index,d),d=d.sibling;return c}function o(c,d){return c=Jt(c,d),c.index=0,c.sibling=null,c}function i(c,d,m){return c.index=m,e?(m=c.alternate,m!==null?(m=m.index,m<d?(c.flags|=2,d):m):(c.flags|=2,d)):(c.flags|=1048576,d)}function s(c){return e&&c.alternate===null&&(c.flags|=2),c}function l(c,d,m,C){return d===null||d.tag!==6?(d=Ds(m,c.mode,C),d.return=c,d):(d=o(d,m),d.return=c,d)}function a(c,d,m,C){var S=m.type;return S===zn?h(c,d,m.props.children,C,m.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&ac(S)===d.type)?(C=o(d,m.props),C.ref=kr(c,d,m),C.return=c,C):(C=ni(m.type,m.key,m.props,null,c.mode,C),C.ref=kr(c,d,m),C.return=c,C)}function u(c,d,m,C){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=Bs(m,c.mode,C),d.return=c,d):(d=o(d,m.children||[]),d.return=c,d)}function h(c,d,m,C,S){return d===null||d.tag!==7?(d=gn(m,c.mode,C,S),d.return=c,d):(d=o(d,m),d.return=c,d)}function p(c,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Ds(""+d,c.mode,m),d.return=c,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case ko:return m=ni(d.type,d.key,d.props,null,c.mode,m),m.ref=kr(c,null,d),m.return=c,m;case Rn:return d=Bs(d,c.mode,m),d.return=c,d;case Mt:var C=d._init;return p(c,C(d._payload),m)}if(Ir(d)||vr(d))return d=gn(d,c.mode,m,null),d.return=c,d;Ro(c,d)}return null}function v(c,d,m,C){var S=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return S!==null?null:l(c,d,""+m,C);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ko:return m.key===S?a(c,d,m,C):null;case Rn:return m.key===S?u(c,d,m,C):null;case Mt:return S=m._init,v(c,d,S(m._payload),C)}if(Ir(m)||vr(m))return S!==null?null:h(c,d,m,C,null);Ro(c,m)}return null}function g(c,d,m,C,S){if(typeof C=="string"&&C!==""||typeof C=="number")return c=c.get(m)||null,l(d,c,""+C,S);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case ko:return c=c.get(C.key===null?m:C.key)||null,a(d,c,C,S);case Rn:return c=c.get(C.key===null?m:C.key)||null,u(d,c,C,S);case Mt:var T=C._init;return g(c,d,m,T(C._payload),S)}if(Ir(C)||vr(C))return c=c.get(m)||null,h(d,c,C,S,null);Ro(d,C)}return null}function w(c,d,m,C){for(var S=null,T=null,E=d,N=d=0,M=null;E!==null&&N<m.length;N++){E.index>N?(M=E,E=null):M=E.sibling;var O=v(c,E,m[N],C);if(O===null){E===null&&(E=M);break}e&&E&&O.alternate===null&&t(c,E),d=i(O,d,N),T===null?S=O:T.sibling=O,T=O,E=M}if(N===m.length)return n(c,E),J&&an(c,N),S;if(E===null){for(;N<m.length;N++)E=p(c,m[N],C),E!==null&&(d=i(E,d,N),T===null?S=E:T.sibling=E,T=E);return J&&an(c,N),S}for(E=r(c,E);N<m.length;N++)M=g(E,c,N,m[N],C),M!==null&&(e&&M.alternate!==null&&E.delete(M.key===null?N:M.key),d=i(M,d,N),T===null?S=M:T.sibling=M,T=M);return e&&E.forEach(function(K){return t(c,K)}),J&&an(c,N),S}function y(c,d,m,C){var S=vr(m);if(typeof S!="function")throw Error(P(150));if(m=S.call(m),m==null)throw Error(P(151));for(var T=S=null,E=d,N=d=0,M=null,O=m.next();E!==null&&!O.done;N++,O=m.next()){E.index>N?(M=E,E=null):M=E.sibling;var K=v(c,E,O.value,C);if(K===null){E===null&&(E=M);break}e&&E&&K.alternate===null&&t(c,E),d=i(K,d,N),T===null?S=K:T.sibling=K,T=K,E=M}if(O.done)return n(c,E),J&&an(c,N),S;if(E===null){for(;!O.done;N++,O=m.next())O=p(c,O.value,C),O!==null&&(d=i(O,d,N),T===null?S=O:T.sibling=O,T=O);return J&&an(c,N),S}for(E=r(c,E);!O.done;N++,O=m.next())O=g(E,c,N,O.value,C),O!==null&&(e&&O.alternate!==null&&E.delete(O.key===null?N:O.key),d=i(O,d,N),T===null?S=O:T.sibling=O,T=O);return e&&E.forEach(function($){return t(c,$)}),J&&an(c,N),S}function x(c,d,m,C){if(typeof m=="object"&&m!==null&&m.type===zn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case ko:e:{for(var S=m.key,T=d;T!==null;){if(T.key===S){if(S=m.type,S===zn){if(T.tag===7){n(c,T.sibling),d=o(T,m.props.children),d.return=c,c=d;break e}}else if(T.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&ac(S)===T.type){n(c,T.sibling),d=o(T,m.props),d.ref=kr(c,T,m),d.return=c,c=d;break e}n(c,T);break}else t(c,T);T=T.sibling}m.type===zn?(d=gn(m.props.children,c.mode,C,m.key),d.return=c,c=d):(C=ni(m.type,m.key,m.props,null,c.mode,C),C.ref=kr(c,d,m),C.return=c,c=C)}return s(c);case Rn:e:{for(T=m.key;d!==null;){if(d.key===T)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(c,d.sibling),d=o(d,m.children||[]),d.return=c,c=d;break e}else{n(c,d);break}else t(c,d);d=d.sibling}d=Bs(m,c.mode,C),d.return=c,c=d}return s(c);case Mt:return T=m._init,x(c,d,T(m._payload),C)}if(Ir(m))return w(c,d,m,C);if(vr(m))return y(c,d,m,C);Ro(c,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(c,d.sibling),d=o(d,m),d.return=c,c=d):(n(c,d),d=Ds(m,c.mode,C),d.return=c,c=d),s(c)):n(c,d)}return x}var rr=jp(!0),Op=jp(!1),yi=rn(null),xi=null,Vn=null,Ia=null;function Aa(){Ia=Vn=xi=null}function La(e){var t=yi.current;Z(yi),e._currentValue=t}function Tl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Xn(e,t){xi=e,Ia=Vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Re=!0),e.firstContext=null)}function et(e){var t=e._currentValue;if(Ia!==e)if(e={context:e,memoizedValue:t,next:null},Vn===null){if(xi===null)throw Error(P(308));Vn=e,xi.dependencies={lanes:0,firstContext:e}}else Vn=Vn.next=e;return t}var dn=null;function ja(e){dn===null?dn=[e]:dn.push(e)}function Rp(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,ja(t)):(n.next=o.next,o.next=n),t.interleaved=n,jt(e,r)}function jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var $t=!1;function Oa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,jt(e,n)}return o=r.interleaved,o===null?(t.next=t,ja(r)):(t.next=o.next,o.next=t),r.interleaved=t,jt(e,n)}function Xo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,va(e,n)}}function uc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function bi(e,t,n,r){var o=e.updateQueue;$t=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var h=e.alternate;h!==null&&(h=h.updateQueue,l=h.lastBaseUpdate,l!==s&&(l===null?h.firstBaseUpdate=u:l.next=u,h.lastBaseUpdate=a))}if(i!==null){var p=o.baseState;s=0,h=u=a=null,l=i;do{var v=l.lane,g=l.eventTime;if((r&v)===v){h!==null&&(h=h.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,y=l;switch(v=t,g=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){p=w.call(g,p,v);break e}p=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,v=typeof w=="function"?w.call(g,p,v):w,v==null)break e;p=re({},p,v);break e;case 2:$t=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,v=o.effects,v===null?o.effects=[l]:v.push(l))}else g={eventTime:g,lane:v,tag:l.tag,payload:l.payload,callback:l.callback,next:null},h===null?(u=h=g,a=p):h=h.next=g,s|=v;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;v=l,l=v.next,v.next=null,o.lastBaseUpdate=v,o.shared.pending=null}}while(1);if(h===null&&(a=p),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=h,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Cn|=s,e.lanes=s,e.memoizedState=p}}function cc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(P(191,o));o.call(r)}}}var fo={},yt=rn(fo),Jr=rn(fo),eo=rn(fo);function pn(e){if(e===fo)throw Error(P(174));return e}function Ra(e,t){switch(G(eo,t),G(Jr,e),G(yt,fo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:sl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=sl(t,e)}Z(yt),G(yt,t)}function or(){Z(yt),Z(Jr),Z(eo)}function _p(e){pn(eo.current);var t=pn(yt.current),n=sl(t,e.type);t!==n&&(G(Jr,e),G(yt,n))}function za(e){Jr.current===e&&(Z(yt),Z(Jr))}var te=rn(0);function Ci(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Os=[];function _a(){for(var e=0;e<Os.length;e++)Os[e]._workInProgressVersionPrimary=null;Os.length=0}var Yo=Rt.ReactCurrentDispatcher,Rs=Rt.ReactCurrentBatchConfig,bn=0,ne=null,ue=null,pe=null,ki=!1,Mr=!1,to=0,Ew=0;function xe(){throw Error(P(321))}function Ma(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!dt(e[n],t[n]))return!1;return!0}function $a(e,t,n,r,o,i){if(bn=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yo.current=e===null||e.memoizedState===null?Iw:Aw,e=n(r,o),Mr){i=0;do{if(Mr=!1,to=0,25<=i)throw Error(P(301));i+=1,pe=ue=null,t.updateQueue=null,Yo.current=Lw,e=n(r,o)}while(Mr)}if(Yo.current=Si,t=ue!==null&&ue.next!==null,bn=0,pe=ue=ne=null,ki=!1,t)throw Error(P(300));return e}function Da(){var e=to!==0;return to=0,e}function ht(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return pe===null?ne.memoizedState=pe=e:pe=pe.next=e,pe}function tt(){if(ue===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=ue.next;var t=pe===null?ne.memoizedState:pe.next;if(t!==null)pe=t,ue=e;else{if(e===null)throw Error(P(310));ue=e,e={memoizedState:ue.memoizedState,baseState:ue.baseState,baseQueue:ue.baseQueue,queue:ue.queue,next:null},pe===null?ne.memoizedState=pe=e:pe=pe.next=e}return pe}function no(e,t){return typeof t=="function"?t(e):t}function zs(e){var t=tt(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ue,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var h=u.lane;if((bn&h)===h)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:h,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=p,s=r):a=a.next=p,ne.lanes|=h,Cn|=h}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,dt(r,t.memoizedState)||(Re=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ne.lanes|=i,Cn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function _s(e){var t=tt(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);dt(i,t.memoizedState)||(Re=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Mp(){}function $p(e,t){var n=ne,r=tt(),o=t(),i=!dt(r.memoizedState,o);if(i&&(r.memoizedState=o,Re=!0),r=r.queue,Ba(Fp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||pe!==null&&pe.memoizedState.tag&1){if(n.flags|=2048,ro(9,Bp.bind(null,n,r,o,t),void 0,null),fe===null)throw Error(P(349));bn&30||Dp(n,t,o)}return o}function Dp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bp(e,t,n,r){t.value=n,t.getSnapshot=r,Up(t)&&Vp(e)}function Fp(e,t,n){return n(function(){Up(t)&&Vp(e)})}function Up(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!dt(e,n)}catch{return!0}}function Vp(e){var t=jt(e,1);t!==null&&ct(t,e,1,-1)}function dc(e){var t=ht();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:no,lastRenderedState:e},t.queue=e,e=e.dispatch=Nw.bind(null,ne,e),[t.memoizedState,e]}function ro(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Hp(){return tt().memoizedState}function Zo(e,t,n,r){var o=ht();ne.flags|=e,o.memoizedState=ro(1|t,n,void 0,r===void 0?null:r)}function Qi(e,t,n,r){var o=tt();r=r===void 0?null:r;var i=void 0;if(ue!==null){var s=ue.memoizedState;if(i=s.destroy,r!==null&&Ma(r,s.deps)){o.memoizedState=ro(t,n,i,r);return}}ne.flags|=e,o.memoizedState=ro(1|t,n,i,r)}function pc(e,t){return Zo(8390656,8,e,t)}function Ba(e,t){return Qi(2048,8,e,t)}function Wp(e,t){return Qi(4,2,e,t)}function Kp(e,t){return Qi(4,4,e,t)}function qp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Gp(e,t,n){return n=n!=null?n.concat([e]):null,Qi(4,4,qp.bind(null,t,e),n)}function Fa(){}function Qp(e,t){var n=tt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ma(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xp(e,t){var n=tt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ma(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Yp(e,t,n){return bn&21?(dt(n,t)||(n=np(),ne.lanes|=n,Cn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Re=!0),e.memoizedState=n)}function Tw(e,t){var n=W;W=n!==0&&4>n?n:4,e(!0);var r=Rs.transition;Rs.transition={};try{e(!1),t()}finally{W=n,Rs.transition=r}}function Zp(){return tt().memoizedState}function Pw(e,t,n){var r=Zt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jp(e))ef(t,n);else if(n=Rp(e,t,n,r),n!==null){var o=Pe();ct(n,e,r,o),tf(n,t,r)}}function Nw(e,t,n){var r=Zt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jp(e))ef(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,dt(l,s)){var a=t.interleaved;a===null?(o.next=o,ja(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Rp(e,t,o,r),n!==null&&(o=Pe(),ct(n,e,r,o),tf(n,t,r))}}function Jp(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function ef(e,t){Mr=ki=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,va(e,n)}}var Si={readContext:et,useCallback:xe,useContext:xe,useEffect:xe,useImperativeHandle:xe,useInsertionEffect:xe,useLayoutEffect:xe,useMemo:xe,useReducer:xe,useRef:xe,useState:xe,useDebugValue:xe,useDeferredValue:xe,useTransition:xe,useMutableSource:xe,useSyncExternalStore:xe,useId:xe,unstable_isNewReconciler:!1},Iw={readContext:et,useCallback:function(e,t){return ht().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:pc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Zo(4194308,4,qp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Zo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Zo(4,2,e,t)},useMemo:function(e,t){var n=ht();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ht();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Pw.bind(null,ne,e),[r.memoizedState,e]},useRef:function(e){var t=ht();return e={current:e},t.memoizedState=e},useState:dc,useDebugValue:Fa,useDeferredValue:function(e){return ht().memoizedState=e},useTransition:function(){var e=dc(!1),t=e[0];return e=Tw.bind(null,e[1]),ht().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ne,o=ht();if(J){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),fe===null)throw Error(P(349));bn&30||Dp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,pc(Fp.bind(null,r,i,e),[e]),r.flags|=2048,ro(9,Bp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=ht(),t=fe.identifierPrefix;if(J){var n=Tt,r=Et;n=(r&~(1<<32-ut(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=to++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ew++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Aw={readContext:et,useCallback:Qp,useContext:et,useEffect:Ba,useImperativeHandle:Gp,useInsertionEffect:Wp,useLayoutEffect:Kp,useMemo:Xp,useReducer:zs,useRef:Hp,useState:function(){return zs(no)},useDebugValue:Fa,useDeferredValue:function(e){var t=tt();return Yp(t,ue.memoizedState,e)},useTransition:function(){var e=zs(no)[0],t=tt().memoizedState;return[e,t]},useMutableSource:Mp,useSyncExternalStore:$p,useId:Zp,unstable_isNewReconciler:!1},Lw={readContext:et,useCallback:Qp,useContext:et,useEffect:Ba,useImperativeHandle:Gp,useInsertionEffect:Wp,useLayoutEffect:Kp,useMemo:Xp,useReducer:_s,useRef:Hp,useState:function(){return _s(no)},useDebugValue:Fa,useDeferredValue:function(e){var t=tt();return ue===null?t.memoizedState=e:Yp(t,ue.memoizedState,e)},useTransition:function(){var e=_s(no)[0],t=tt().memoizedState;return[e,t]},useMutableSource:Mp,useSyncExternalStore:$p,useId:Zp,unstable_isNewReconciler:!1};function st(e,t){if(e&&e.defaultProps){t=re({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Pl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:re({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Xi={isMounted:function(e){return(e=e._reactInternals)?In(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Pe(),o=Zt(e),i=Pt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Xt(e,i,o),t!==null&&(ct(t,e,o,r),Xo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Pe(),o=Zt(e),i=Pt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Xt(e,i,o),t!==null&&(ct(t,e,o,r),Xo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Pe(),r=Zt(e),o=Pt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Xt(e,o,r),t!==null&&(ct(t,e,r,n),Xo(t,e,r))}};function fc(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Qr(n,r)||!Qr(o,i):!0}function nf(e,t,n){var r=!1,o=tn,i=t.contextType;return typeof i=="object"&&i!==null?i=et(i):(o=_e(t)?yn:ke.current,r=t.contextTypes,i=(r=r!=null)?tr(e,o):tn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Xi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function mc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Xi.enqueueReplaceState(t,t.state,null)}function Nl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Oa(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=et(i):(i=_e(t)?yn:ke.current,o.context=tr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Pl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Xi.enqueueReplaceState(o,o.state,null),bi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function ir(e,t){try{var n="",r=t;do n+=ig(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ms(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Il(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var jw=typeof WeakMap=="function"?WeakMap:Map;function rf(e,t,n){n=Pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ti||(Ti=!0,Dl=r),Il(e,t)},n}function of(e,t,n){n=Pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Il(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Il(e,t),typeof r!="function"&&(Yt===null?Yt=new Set([this]):Yt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function hc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new jw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Kw.bind(null,e,t,n),t.then(e,e))}function gc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Pt(-1,1),t.tag=2,Xt(n,t,1))),n.lanes|=1),e)}var Ow=Rt.ReactCurrentOwner,Re=!1;function Te(e,t,n,r){t.child=e===null?Op(t,null,n,r):rr(t,e.child,n,r)}function vc(e,t,n,r,o){n=n.render;var i=t.ref;return Xn(t,o),r=$a(e,t,n,r,i,o),n=Da(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ot(e,t,o)):(J&&n&&Ta(t),t.flags|=1,Te(e,t,r,o),t.child)}function yc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Qa(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,sf(e,t,i,r,o)):(e=ni(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Qr,n(s,r)&&e.ref===t.ref)return Ot(e,t,o)}return t.flags|=1,e=Jt(i,r),e.ref=t.ref,e.return=t,t.child=e}function sf(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Qr(i,r)&&e.ref===t.ref)if(Re=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,Ot(e,t,o)}return Al(e,t,n,r,o)}function lf(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(Wn,De),De|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,G(Wn,De),De|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,G(Wn,De),De|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,G(Wn,De),De|=r;return Te(e,t,o,n),t.child}function af(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Al(e,t,n,r,o){var i=_e(n)?yn:ke.current;return i=tr(t,i),Xn(t,o),n=$a(e,t,n,r,i,o),r=Da(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ot(e,t,o)):(J&&r&&Ta(t),t.flags|=1,Te(e,t,n,o),t.child)}function xc(e,t,n,r,o){if(_e(n)){var i=!0;gi(t)}else i=!1;if(Xn(t,o),t.stateNode===null)Jo(e,t),nf(t,n,r),Nl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=et(u):(u=_e(n)?yn:ke.current,u=tr(t,u));var h=n.getDerivedStateFromProps,p=typeof h=="function"||typeof s.getSnapshotBeforeUpdate=="function";p||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&mc(t,s,r,u),$t=!1;var v=t.memoizedState;s.state=v,bi(t,r,s,o),a=t.memoizedState,l!==r||v!==a||ze.current||$t?(typeof h=="function"&&(Pl(t,n,h,r),a=t.memoizedState),(l=$t||fc(t,n,l,r,v,a,u))?(p||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,zp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:st(t.type,l),s.props=u,p=t.pendingProps,v=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=et(a):(a=_e(n)?yn:ke.current,a=tr(t,a));var g=n.getDerivedStateFromProps;(h=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==p||v!==a)&&mc(t,s,r,a),$t=!1,v=t.memoizedState,s.state=v,bi(t,r,s,o);var w=t.memoizedState;l!==p||v!==w||ze.current||$t?(typeof g=="function"&&(Pl(t,n,g,r),w=t.memoizedState),(u=$t||fc(t,n,u,r,v,w,a)||!1)?(h||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Ll(e,t,n,r,i,o)}function Ll(e,t,n,r,o,i){af(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&ic(t,n,!1),Ot(e,t,i);r=t.stateNode,Ow.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=rr(t,e.child,null,i),t.child=rr(t,null,l,i)):Te(e,t,l,i),t.memoizedState=r.state,o&&ic(t,n,!0),t.child}function uf(e){var t=e.stateNode;t.pendingContext?oc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&oc(e,t.context,!1),Ra(e,t.containerInfo)}function bc(e,t,n,r,o){return nr(),Na(o),t.flags|=256,Te(e,t,n,r),t.child}var jl={dehydrated:null,treeContext:null,retryLane:0};function Ol(e){return{baseLanes:e,cachePool:null,transitions:null}}function cf(e,t,n){var r=t.pendingProps,o=te.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),G(te,o&1),e===null)return El(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Ji(s,r,0,null),e=gn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ol(n),t.memoizedState=jl,e):Ua(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return Rw(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Jt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Jt(l,i):(i=gn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Ol(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=jl,r}return i=e.child,e=i.sibling,r=Jt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ua(e,t){return t=Ji({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zo(e,t,n,r){return r!==null&&Na(r),rr(t,e.child,null,n),e=Ua(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Rw(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ms(Error(P(422))),zo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ji({mode:"visible",children:r.children},o,0,null),i=gn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&rr(t,e.child,null,s),t.child.memoizedState=Ol(s),t.memoizedState=jl,i);if(!(t.mode&1))return zo(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(P(419)),r=Ms(i,r,void 0),zo(e,t,s,r)}if(l=(s&e.childLanes)!==0,Re||l){if(r=fe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,jt(e,o),ct(r,e,o,-1))}return Ga(),r=Ms(Error(P(421))),zo(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=qw.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Fe=Qt(o.nextSibling),Ue=t,J=!0,at=null,e!==null&&(Xe[Ye++]=Et,Xe[Ye++]=Tt,Xe[Ye++]=xn,Et=e.id,Tt=e.overflow,xn=t),t=Ua(t,r.children),t.flags|=4096,t)}function Cc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Tl(e.return,t,n)}function $s(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function df(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Te(e,t,r.children,n),r=te.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cc(e,n,t);else if(e.tag===19)Cc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(G(te,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ci(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),$s(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ci(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}$s(t,!0,n,null,i);break;case"together":$s(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Jo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ot(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Cn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=Jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function zw(e,t,n){switch(t.tag){case 3:uf(t),nr();break;case 5:_p(t);break;case 1:_e(t.type)&&gi(t);break;case 4:Ra(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;G(yi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(G(te,te.current&1),t.flags|=128,null):n&t.child.childLanes?cf(e,t,n):(G(te,te.current&1),e=Ot(e,t,n),e!==null?e.sibling:null);G(te,te.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return df(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),G(te,te.current),r)break;return null;case 22:case 23:return t.lanes=0,lf(e,t,n)}return Ot(e,t,n)}var pf,Rl,ff,mf;pf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Rl=function(){};ff=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,pn(yt.current);var i=null;switch(n){case"input":o=nl(e,o),r=nl(e,r),i=[];break;case"select":o=re({},o,{value:void 0}),r=re({},r,{value:void 0}),i=[];break;case"textarea":o=il(e,o),r=il(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=mi)}ll(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ur.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ur.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&Y("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};mf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Sr(e,t){if(!J)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function _w(e,t,n){var r=t.pendingProps;switch(Pa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return _e(t.type)&&hi(),be(t),null;case 3:return r=t.stateNode,or(),Z(ze),Z(ke),_a(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Oo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,at!==null&&(Ul(at),at=null))),Rl(e,t),be(t),null;case 5:za(t);var o=pn(eo.current);if(n=t.type,e!==null&&t.stateNode!=null)ff(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return be(t),null}if(e=pn(yt.current),Oo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[wt]=t,r[Zr]=i,e=(t.mode&1)!==0,n){case"dialog":Y("cancel",r),Y("close",r);break;case"iframe":case"object":case"embed":Y("load",r);break;case"video":case"audio":for(o=0;o<Lr.length;o++)Y(Lr[o],r);break;case"source":Y("error",r);break;case"img":case"image":case"link":Y("error",r),Y("load",r);break;case"details":Y("toggle",r);break;case"input":Lu(r,i),Y("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Y("invalid",r);break;case"textarea":Ou(r,i),Y("invalid",r)}ll(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&jo(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&jo(r.textContent,l,e),o=["children",""+l]):Ur.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&Y("scroll",r)}switch(n){case"input":So(r),ju(r,i,!0);break;case"textarea":So(r),Ru(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=mi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[wt]=t,e[Zr]=r,pf(e,t,!1,!1),t.stateNode=e;e:{switch(s=al(n,r),n){case"dialog":Y("cancel",e),Y("close",e),o=r;break;case"iframe":case"object":case"embed":Y("load",e),o=r;break;case"video":case"audio":for(o=0;o<Lr.length;o++)Y(Lr[o],e);o=r;break;case"source":Y("error",e),o=r;break;case"img":case"image":case"link":Y("error",e),Y("load",e),o=r;break;case"details":Y("toggle",e),o=r;break;case"input":Lu(e,r),o=nl(e,r),Y("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=re({},r,{value:void 0}),Y("invalid",e);break;case"textarea":Ou(e,r),o=il(e,r),Y("invalid",e);break;default:o=r}ll(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Hd(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Ud(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Vr(e,a):typeof a=="number"&&Vr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ur.hasOwnProperty(i)?a!=null&&i==="onScroll"&&Y("scroll",e):a!=null&&pa(e,i,a,s))}switch(n){case"input":So(e),ju(e,r,!1);break;case"textarea":So(e),Ru(e);break;case"option":r.value!=null&&e.setAttribute("value",""+en(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Kn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Kn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=mi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)mf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=pn(eo.current),pn(yt.current),Oo(t)){if(r=t.stateNode,n=t.memoizedProps,r[wt]=t,(i=r.nodeValue!==n)&&(e=Ue,e!==null))switch(e.tag){case 3:jo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&jo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[wt]=t,t.stateNode=r}return be(t),null;case 13:if(Z(te),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(J&&Fe!==null&&t.mode&1&&!(t.flags&128))Lp(),nr(),t.flags|=98560,i=!1;else if(i=Oo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(P(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(P(317));i[wt]=t}else nr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),i=!1}else at!==null&&(Ul(at),at=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||te.current&1?ce===0&&(ce=3):Ga())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return or(),Rl(e,t),e===null&&Xr(t.stateNode.containerInfo),be(t),null;case 10:return La(t.type._context),be(t),null;case 17:return _e(t.type)&&hi(),be(t),null;case 19:if(Z(te),i=t.memoizedState,i===null)return be(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Sr(i,!1);else{if(ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ci(e),s!==null){for(t.flags|=128,Sr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return G(te,te.current&1|2),t.child}e=e.sibling}i.tail!==null&&se()>sr&&(t.flags|=128,r=!0,Sr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Ci(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Sr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!J)return be(t),null}else 2*se()-i.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,r=!0,Sr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=se(),t.sibling=null,n=te.current,G(te,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return qa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?De&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function Mw(e,t){switch(Pa(t),t.tag){case 1:return _e(t.type)&&hi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return or(),Z(ze),Z(ke),_a(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return za(t),null;case 13:if(Z(te),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));nr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Z(te),null;case 4:return or(),null;case 10:return La(t.type._context),null;case 22:case 23:return qa(),null;case 24:return null;default:return null}}var _o=!1,Ce=!1,$w=typeof WeakSet=="function"?WeakSet:Set,A=null;function Hn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(e,t,r)}else n.current=null}function zl(e,t,n){try{n()}catch(r){oe(e,t,r)}}var kc=!1;function Dw(e,t){if(vl=di,e=vp(),Ea(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,h=0,p=e,v=null;t:for(;;){for(var g;p!==n||o!==0&&p.nodeType!==3||(l=s+o),p!==i||r!==0&&p.nodeType!==3||(a=s+r),p.nodeType===3&&(s+=p.nodeValue.length),(g=p.firstChild)!==null;)v=p,p=g;for(;;){if(p===e)break t;if(v===n&&++u===o&&(l=s),v===i&&++h===r&&(a=s),(g=p.nextSibling)!==null)break;p=v,v=p.parentNode}p=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(yl={focusedElem:e,selectionRange:n},di=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,x=w.memoizedState,c=t.stateNode,d=c.getSnapshotBeforeUpdate(t.elementType===t.type?y:st(t.type,y),x);c.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(C){oe(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return w=kc,kc=!1,w}function $r(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&zl(t,n,i)}o=o.next}while(o!==r)}}function Yi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function _l(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function hf(e){var t=e.alternate;t!==null&&(e.alternate=null,hf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[wt],delete t[Zr],delete t[Cl],delete t[bw],delete t[Cw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function gf(e){return e.tag===5||e.tag===3||e.tag===4}function Sc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||gf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=mi));else if(r!==4&&(e=e.child,e!==null))for(Ml(e,t,n),e=e.sibling;e!==null;)Ml(e,t,n),e=e.sibling}function $l(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for($l(e,t,n),e=e.sibling;e!==null;)$l(e,t,n),e=e.sibling}var we=null,lt=!1;function zt(e,t,n){for(n=n.child;n!==null;)wf(e,t,n),n=n.sibling}function wf(e,t,n){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(Vi,n)}catch{}switch(n.tag){case 5:Ce||Hn(n,t);case 6:var r=we,o=lt;we=null,zt(e,t,n),we=r,lt=o,we!==null&&(lt?(e=we,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):we.removeChild(n.stateNode));break;case 18:we!==null&&(lt?(e=we,n=n.stateNode,e.nodeType===8?Ls(e.parentNode,n):e.nodeType===1&&Ls(e,n),qr(e)):Ls(we,n.stateNode));break;case 4:r=we,o=lt,we=n.stateNode.containerInfo,lt=!0,zt(e,t,n),we=r,lt=o;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&zl(n,t,s),o=o.next}while(o!==r)}zt(e,t,n);break;case 1:if(!Ce&&(Hn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){oe(n,t,l)}zt(e,t,n);break;case 21:zt(e,t,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,zt(e,t,n),Ce=r):zt(e,t,n);break;default:zt(e,t,n)}}function Ec(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new $w),t.forEach(function(r){var o=Gw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ot(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:we=l.stateNode,lt=!1;break e;case 3:we=l.stateNode.containerInfo,lt=!0;break e;case 4:we=l.stateNode.containerInfo,lt=!0;break e}l=l.return}if(we===null)throw Error(P(160));wf(i,s,o),we=null,lt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){oe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)vf(t,e),t=t.sibling}function vf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ot(t,e),mt(e),r&4){try{$r(3,e,e.return),Yi(3,e)}catch(y){oe(e,e.return,y)}try{$r(5,e,e.return)}catch(y){oe(e,e.return,y)}}break;case 1:ot(t,e),mt(e),r&512&&n!==null&&Hn(n,n.return);break;case 5:if(ot(t,e),mt(e),r&512&&n!==null&&Hn(n,n.return),e.flags&32){var o=e.stateNode;try{Vr(o,"")}catch(y){oe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Dd(o,i),al(l,s);var u=al(l,i);for(s=0;s<a.length;s+=2){var h=a[s],p=a[s+1];h==="style"?Hd(o,p):h==="dangerouslySetInnerHTML"?Ud(o,p):h==="children"?Vr(o,p):pa(o,h,p,u)}switch(l){case"input":rl(o,i);break;case"textarea":Bd(o,i);break;case"select":var v=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Kn(o,!!i.multiple,g,!1):v!==!!i.multiple&&(i.defaultValue!=null?Kn(o,!!i.multiple,i.defaultValue,!0):Kn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Zr]=i}catch(y){oe(e,e.return,y)}}break;case 6:if(ot(t,e),mt(e),r&4){if(e.stateNode===null)throw Error(P(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){oe(e,e.return,y)}}break;case 3:if(ot(t,e),mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{qr(t.containerInfo)}catch(y){oe(e,e.return,y)}break;case 4:ot(t,e),mt(e);break;case 13:ot(t,e),mt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Wa=se())),r&4&&Ec(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?(Ce=(u=Ce)||h,ot(t,e),Ce=u):ot(t,e),mt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!h&&e.mode&1)for(A=e,h=e.child;h!==null;){for(p=A=h;A!==null;){switch(v=A,g=v.child,v.tag){case 0:case 11:case 14:case 15:$r(4,v,v.return);break;case 1:Hn(v,v.return);var w=v.stateNode;if(typeof w.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){oe(r,n,y)}}break;case 5:Hn(v,v.return);break;case 22:if(v.memoizedState!==null){Pc(p);continue}}g!==null?(g.return=v,A=g):Pc(p)}h=h.sibling}e:for(h=null,p=e;;){if(p.tag===5){if(h===null){h=p;try{o=p.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=p.stateNode,a=p.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Vd("display",s))}catch(y){oe(e,e.return,y)}}}else if(p.tag===6){if(h===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(y){oe(e,e.return,y)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;h===p&&(h=null),p=p.return}h===p&&(h=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:ot(t,e),mt(e),r&4&&Ec(e);break;case 21:break;default:ot(t,e),mt(e)}}function mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(gf(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Vr(o,""),r.flags&=-33);var i=Sc(e);$l(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Sc(e);Ml(e,l,s);break;default:throw Error(P(161))}}catch(a){oe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Bw(e,t,n){A=e,yf(e)}function yf(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var o=A,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||_o;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Ce;l=_o;var u=Ce;if(_o=s,(Ce=a)&&!u)for(A=o;A!==null;)s=A,a=s.child,s.tag===22&&s.memoizedState!==null?Nc(o):a!==null?(a.return=s,A=a):Nc(o);for(;i!==null;)A=i,yf(i),i=i.sibling;A=o,_o=l,Ce=u}Tc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,A=i):Tc(e)}}function Tc(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ce||Yi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:st(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&cc(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}cc(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var h=u.memoizedState;if(h!==null){var p=h.dehydrated;p!==null&&qr(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Ce||t.flags&512&&_l(t)}catch(v){oe(t,t.return,v)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function Pc(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function Nc(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Yi(4,t)}catch(a){oe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){oe(t,o,a)}}var i=t.return;try{_l(t)}catch(a){oe(t,i,a)}break;case 5:var s=t.return;try{_l(t)}catch(a){oe(t,s,a)}}}catch(a){oe(t,t.return,a)}if(t===e){A=null;break}var l=t.sibling;if(l!==null){l.return=t.return,A=l;break}A=t.return}}var Fw=Math.ceil,Ei=Rt.ReactCurrentDispatcher,Va=Rt.ReactCurrentOwner,Je=Rt.ReactCurrentBatchConfig,F=0,fe=null,ae=null,ve=0,De=0,Wn=rn(0),ce=0,oo=null,Cn=0,Zi=0,Ha=0,Dr=null,Oe=null,Wa=0,sr=1/0,Ct=null,Ti=!1,Dl=null,Yt=null,Mo=!1,Ut=null,Pi=0,Br=0,Bl=null,ei=-1,ti=0;function Pe(){return F&6?se():ei!==-1?ei:ei=se()}function Zt(e){return e.mode&1?F&2&&ve!==0?ve&-ve:Sw.transition!==null?(ti===0&&(ti=np()),ti):(e=W,e!==0||(e=window.event,e=e===void 0?16:up(e.type)),e):1}function ct(e,t,n,r){if(50<Br)throw Br=0,Bl=null,Error(P(185));uo(e,n,r),(!(F&2)||e!==fe)&&(e===fe&&(!(F&2)&&(Zi|=n),ce===4&&Bt(e,ve)),Me(e,r),n===1&&F===0&&!(t.mode&1)&&(sr=se()+500,Gi&&on()))}function Me(e,t){var n=e.callbackNode;Sg(e,t);var r=ci(e,e===fe?ve:0);if(r===0)n!==null&&Mu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Mu(n),t===1)e.tag===0?kw(Ic.bind(null,e)):Np(Ic.bind(null,e)),yw(function(){!(F&6)&&on()}),n=null;else{switch(rp(r)){case 1:n=wa;break;case 4:n=ep;break;case 16:n=ui;break;case 536870912:n=tp;break;default:n=ui}n=Pf(n,xf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function xf(e,t){if(ei=-1,ti=0,F&6)throw Error(P(327));var n=e.callbackNode;if(Yn()&&e.callbackNode!==n)return null;var r=ci(e,e===fe?ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ni(e,r);else{t=r;var o=F;F|=2;var i=Cf();(fe!==e||ve!==t)&&(Ct=null,sr=se()+500,hn(e,t));do try{Hw();break}catch(l){bf(e,l)}while(1);Aa(),Ei.current=i,F=o,ae!==null?t=0:(fe=null,ve=0,t=ce)}if(t!==0){if(t===2&&(o=fl(e),o!==0&&(r=o,t=Fl(e,o))),t===1)throw n=oo,hn(e,0),Bt(e,r),Me(e,se()),n;if(t===6)Bt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Uw(o)&&(t=Ni(e,r),t===2&&(i=fl(e),i!==0&&(r=i,t=Fl(e,i))),t===1))throw n=oo,hn(e,0),Bt(e,r),Me(e,se()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:un(e,Oe,Ct);break;case 3:if(Bt(e,r),(r&130023424)===r&&(t=Wa+500-se(),10<t)){if(ci(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Pe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=bl(un.bind(null,e,Oe,Ct),t);break}un(e,Oe,Ct);break;case 4:if(Bt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-ut(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Fw(r/1960))-r,10<r){e.timeoutHandle=bl(un.bind(null,e,Oe,Ct),r);break}un(e,Oe,Ct);break;case 5:un(e,Oe,Ct);break;default:throw Error(P(329))}}}return Me(e,se()),e.callbackNode===n?xf.bind(null,e):null}function Fl(e,t){var n=Dr;return e.current.memoizedState.isDehydrated&&(hn(e,t).flags|=256),e=Ni(e,t),e!==2&&(t=Oe,Oe=n,t!==null&&Ul(t)),e}function Ul(e){Oe===null?Oe=e:Oe.push.apply(Oe,e)}function Uw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!dt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bt(e,t){for(t&=~Ha,t&=~Zi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ut(t),r=1<<n;e[n]=-1,t&=~r}}function Ic(e){if(F&6)throw Error(P(327));Yn();var t=ci(e,0);if(!(t&1))return Me(e,se()),null;var n=Ni(e,t);if(e.tag!==0&&n===2){var r=fl(e);r!==0&&(t=r,n=Fl(e,r))}if(n===1)throw n=oo,hn(e,0),Bt(e,t),Me(e,se()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,un(e,Oe,Ct),Me(e,se()),null}function Ka(e,t){var n=F;F|=1;try{return e(t)}finally{F=n,F===0&&(sr=se()+500,Gi&&on())}}function kn(e){Ut!==null&&Ut.tag===0&&!(F&6)&&Yn();var t=F;F|=1;var n=Je.transition,r=W;try{if(Je.transition=null,W=1,e)return e()}finally{W=r,Je.transition=n,F=t,!(F&6)&&on()}}function qa(){De=Wn.current,Z(Wn)}function hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,vw(n)),ae!==null)for(n=ae.return;n!==null;){var r=n;switch(Pa(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&hi();break;case 3:or(),Z(ze),Z(ke),_a();break;case 5:za(r);break;case 4:or();break;case 13:Z(te);break;case 19:Z(te);break;case 10:La(r.type._context);break;case 22:case 23:qa()}n=n.return}if(fe=e,ae=e=Jt(e.current,null),ve=De=t,ce=0,oo=null,Ha=Zi=Cn=0,Oe=Dr=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}dn=null}return e}function bf(e,t){do{var n=ae;try{if(Aa(),Yo.current=Si,ki){for(var r=ne.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ki=!1}if(bn=0,pe=ue=ne=null,Mr=!1,to=0,Va.current=null,n===null||n.return===null){ce=1,oo=t,ae=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=ve,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,h=l,p=h.tag;if(!(h.mode&1)&&(p===0||p===11||p===15)){var v=h.alternate;v?(h.updateQueue=v.updateQueue,h.memoizedState=v.memoizedState,h.lanes=v.lanes):(h.updateQueue=null,h.memoizedState=null)}var g=gc(s);if(g!==null){g.flags&=-257,wc(g,s,l,i,t),g.mode&1&&hc(i,u,t),t=g,a=u;var w=t.updateQueue;if(w===null){var y=new Set;y.add(a),t.updateQueue=y}else w.add(a);break e}else{if(!(t&1)){hc(i,u,t),Ga();break e}a=Error(P(426))}}else if(J&&l.mode&1){var x=gc(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),wc(x,s,l,i,t),Na(ir(a,l));break e}}i=a=ir(a,l),ce!==4&&(ce=2),Dr===null?Dr=[i]:Dr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var c=rf(i,a,t);uc(i,c);break e;case 1:l=a;var d=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Yt===null||!Yt.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var C=of(i,l,t);uc(i,C);break e}}i=i.return}while(i!==null)}Sf(n)}catch(S){t=S,ae===n&&n!==null&&(ae=n=n.return);continue}break}while(1)}function Cf(){var e=Ei.current;return Ei.current=Si,e===null?Si:e}function Ga(){(ce===0||ce===3||ce===2)&&(ce=4),fe===null||!(Cn&268435455)&&!(Zi&268435455)||Bt(fe,ve)}function Ni(e,t){var n=F;F|=2;var r=Cf();(fe!==e||ve!==t)&&(Ct=null,hn(e,t));do try{Vw();break}catch(o){bf(e,o)}while(1);if(Aa(),F=n,Ei.current=r,ae!==null)throw Error(P(261));return fe=null,ve=0,ce}function Vw(){for(;ae!==null;)kf(ae)}function Hw(){for(;ae!==null&&!hg();)kf(ae)}function kf(e){var t=Tf(e.alternate,e,De);e.memoizedProps=e.pendingProps,t===null?Sf(e):ae=t,Va.current=null}function Sf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Mw(n,t),n!==null){n.flags&=32767,ae=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ce=6,ae=null;return}}else if(n=_w(n,t,De),n!==null){ae=n;return}if(t=t.sibling,t!==null){ae=t;return}ae=t=e}while(t!==null);ce===0&&(ce=5)}function un(e,t,n){var r=W,o=Je.transition;try{Je.transition=null,W=1,Ww(e,t,n,r)}finally{Je.transition=o,W=r}return null}function Ww(e,t,n,r){do Yn();while(Ut!==null);if(F&6)throw Error(P(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Eg(e,i),e===fe&&(ae=fe=null,ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mo||(Mo=!0,Pf(ui,function(){return Yn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Je.transition,Je.transition=null;var s=W;W=1;var l=F;F|=4,Va.current=null,Dw(e,n),vf(n,e),dw(yl),di=!!vl,yl=vl=null,e.current=n,Bw(n),gg(),F=l,W=s,Je.transition=i}else e.current=n;if(Mo&&(Mo=!1,Ut=e,Pi=o),i=e.pendingLanes,i===0&&(Yt=null),yg(n.stateNode),Me(e,se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ti)throw Ti=!1,e=Dl,Dl=null,e;return Pi&1&&e.tag!==0&&Yn(),i=e.pendingLanes,i&1?e===Bl?Br++:(Br=0,Bl=e):Br=0,on(),null}function Yn(){if(Ut!==null){var e=rp(Pi),t=Je.transition,n=W;try{if(Je.transition=null,W=16>e?16:e,Ut===null)var r=!1;else{if(e=Ut,Ut=null,Pi=0,F&6)throw Error(P(331));var o=F;for(F|=4,A=e.current;A!==null;){var i=A,s=i.child;if(A.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(A=u;A!==null;){var h=A;switch(h.tag){case 0:case 11:case 15:$r(8,h,i)}var p=h.child;if(p!==null)p.return=h,A=p;else for(;A!==null;){h=A;var v=h.sibling,g=h.return;if(hf(h),h===u){A=null;break}if(v!==null){v.return=g,A=v;break}A=g}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}A=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,A=s;else e:for(;A!==null;){if(i=A,i.flags&2048)switch(i.tag){case 0:case 11:case 15:$r(9,i,i.return)}var c=i.sibling;if(c!==null){c.return=i.return,A=c;break e}A=i.return}}var d=e.current;for(A=d;A!==null;){s=A;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,A=m;else e:for(s=d;A!==null;){if(l=A,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Yi(9,l)}}catch(S){oe(l,l.return,S)}if(l===s){A=null;break e}var C=l.sibling;if(C!==null){C.return=l.return,A=C;break e}A=l.return}}if(F=o,on(),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(Vi,e)}catch{}r=!0}return r}finally{W=n,Je.transition=t}}return!1}function Ac(e,t,n){t=ir(n,t),t=rf(e,t,1),e=Xt(e,t,1),t=Pe(),e!==null&&(uo(e,1,t),Me(e,t))}function oe(e,t,n){if(e.tag===3)Ac(e,e,n);else for(;t!==null;){if(t.tag===3){Ac(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Yt===null||!Yt.has(r))){e=ir(n,e),e=of(t,e,1),t=Xt(t,e,1),e=Pe(),t!==null&&(uo(t,1,e),Me(t,e));break}}t=t.return}}function Kw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Pe(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(ve&n)===n&&(ce===4||ce===3&&(ve&130023424)===ve&&500>se()-Wa?hn(e,0):Ha|=n),Me(e,t)}function Ef(e,t){t===0&&(e.mode&1?(t=Po,Po<<=1,!(Po&130023424)&&(Po=4194304)):t=1);var n=Pe();e=jt(e,t),e!==null&&(uo(e,t,n),Me(e,n))}function qw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ef(e,n)}function Gw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Ef(e,n)}var Tf;Tf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ze.current)Re=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Re=!1,zw(e,t,n);Re=!!(e.flags&131072)}else Re=!1,J&&t.flags&1048576&&Ip(t,vi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Jo(e,t),e=t.pendingProps;var o=tr(t,ke.current);Xn(t,n),o=$a(null,t,r,e,o,n);var i=Da();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_e(r)?(i=!0,gi(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Oa(t),o.updater=Xi,t.stateNode=o,o._reactInternals=t,Nl(t,r,e,n),t=Ll(null,t,r,!0,i,n)):(t.tag=0,J&&i&&Ta(t),Te(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Jo(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Xw(r),e=st(r,e),o){case 0:t=Al(null,t,r,e,n);break e;case 1:t=xc(null,t,r,e,n);break e;case 11:t=vc(null,t,r,e,n);break e;case 14:t=yc(null,t,r,st(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),Al(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),xc(e,t,r,o,n);case 3:e:{if(uf(t),e===null)throw Error(P(387));r=t.pendingProps,i=t.memoizedState,o=i.element,zp(e,t),bi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=ir(Error(P(423)),t),t=bc(e,t,r,n,o);break e}else if(r!==o){o=ir(Error(P(424)),t),t=bc(e,t,r,n,o);break e}else for(Fe=Qt(t.stateNode.containerInfo.firstChild),Ue=t,J=!0,at=null,n=Op(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(nr(),r===o){t=Ot(e,t,n);break e}Te(e,t,r,n)}t=t.child}return t;case 5:return _p(t),e===null&&El(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,xl(r,o)?s=null:i!==null&&xl(r,i)&&(t.flags|=32),af(e,t),Te(e,t,s,n),t.child;case 6:return e===null&&El(t),null;case 13:return cf(e,t,n);case 4:return Ra(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=rr(t,null,r,n):Te(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),vc(e,t,r,o,n);case 7:return Te(e,t,t.pendingProps,n),t.child;case 8:return Te(e,t,t.pendingProps.children,n),t.child;case 12:return Te(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,G(yi,r._currentValue),r._currentValue=s,i!==null)if(dt(i.value,s)){if(i.children===o.children&&!ze.current){t=Ot(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Pt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var h=u.pending;h===null?a.next=a:(a.next=h.next,h.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Tl(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(P(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Tl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Te(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Xn(t,n),o=et(o),r=r(o),t.flags|=1,Te(e,t,r,n),t.child;case 14:return r=t.type,o=st(r,t.pendingProps),o=st(r.type,o),yc(e,t,r,o,n);case 15:return sf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),Jo(e,t),t.tag=1,_e(r)?(e=!0,gi(t)):e=!1,Xn(t,n),nf(t,r,o),Nl(t,r,o,n),Ll(null,t,r,!0,e,n);case 19:return df(e,t,n);case 22:return lf(e,t,n)}throw Error(P(156,t.tag))};function Pf(e,t){return Jd(e,t)}function Qw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ze(e,t,n,r){return new Qw(e,t,n,r)}function Qa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Xw(e){if(typeof e=="function")return Qa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ma)return 11;if(e===ha)return 14}return 2}function Jt(e,t){var n=e.alternate;return n===null?(n=Ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ni(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Qa(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case zn:return gn(n.children,o,i,t);case fa:s=8,o|=8;break;case Zs:return e=Ze(12,n,t,o|2),e.elementType=Zs,e.lanes=i,e;case Js:return e=Ze(13,n,t,o),e.elementType=Js,e.lanes=i,e;case el:return e=Ze(19,n,t,o),e.elementType=el,e.lanes=i,e;case _d:return Ji(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Rd:s=10;break e;case zd:s=9;break e;case ma:s=11;break e;case ha:s=14;break e;case Mt:s=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=Ze(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function gn(e,t,n,r){return e=Ze(7,e,r,t),e.lanes=n,e}function Ji(e,t,n,r){return e=Ze(22,e,r,t),e.elementType=_d,e.lanes=n,e.stateNode={isHidden:!1},e}function Ds(e,t,n){return e=Ze(6,e,null,t),e.lanes=n,e}function Bs(e,t,n){return t=Ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Yw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=xs(0),this.expirationTimes=xs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Xa(e,t,n,r,o,i,s,l,a){return e=new Yw(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ze(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oa(i),e}function Zw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Rn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Nf(e){if(!e)return tn;e=e._reactInternals;e:{if(In(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_e(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(_e(n))return Pp(e,n,t)}return t}function If(e,t,n,r,o,i,s,l,a){return e=Xa(n,r,!0,e,o,i,s,l,a),e.context=Nf(null),n=e.current,r=Pe(),o=Zt(n),i=Pt(r,o),i.callback=t??null,Xt(n,i,o),e.current.lanes=o,uo(e,o,r),Me(e,r),e}function es(e,t,n,r){var o=t.current,i=Pe(),s=Zt(o);return n=Nf(n),t.context===null?t.context=n:t.pendingContext=n,t=Pt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Xt(o,t,s),e!==null&&(ct(e,o,s,i),Xo(e,o,s)),s}function Ii(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ya(e,t){Lc(e,t),(e=e.alternate)&&Lc(e,t)}function Jw(){return null}var Af=typeof reportError=="function"?reportError:function(e){console.error(e)};function Za(e){this._internalRoot=e}ts.prototype.render=Za.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));es(e,t,null,null)};ts.prototype.unmount=Za.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kn(function(){es(null,e,null,null)}),t[Lt]=null}};function ts(e){this._internalRoot=e}ts.prototype.unstable_scheduleHydration=function(e){if(e){var t=sp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dt.length&&t!==0&&t<Dt[n].priority;n++);Dt.splice(n,0,e),n===0&&ap(e)}};function Ja(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ns(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function jc(){}function ev(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ii(s);i.call(u)}}var s=If(t,r,e,0,null,!1,!1,"",jc);return e._reactRootContainer=s,e[Lt]=s.current,Xr(e.nodeType===8?e.parentNode:e),kn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=Ii(a);l.call(u)}}var a=Xa(e,0,!1,null,null,!1,!1,"",jc);return e._reactRootContainer=a,e[Lt]=a.current,Xr(e.nodeType===8?e.parentNode:e),kn(function(){es(t,a,n,r)}),a}function rs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=Ii(s);l.call(a)}}es(t,s,e,o)}else s=ev(n,t,e,o,r);return Ii(s)}op=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Ar(t.pendingLanes);n!==0&&(va(t,n|1),Me(t,se()),!(F&6)&&(sr=se()+500,on()))}break;case 13:kn(function(){var r=jt(e,1);if(r!==null){var o=Pe();ct(r,e,1,o)}}),Ya(e,1)}};ya=function(e){if(e.tag===13){var t=jt(e,134217728);if(t!==null){var n=Pe();ct(t,e,134217728,n)}Ya(e,134217728)}};ip=function(e){if(e.tag===13){var t=Zt(e),n=jt(e,t);if(n!==null){var r=Pe();ct(n,e,t,r)}Ya(e,t)}};sp=function(){return W};lp=function(e,t){var n=W;try{return W=e,t()}finally{W=n}};cl=function(e,t,n){switch(t){case"input":if(rl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=qi(r);if(!o)throw Error(P(90));$d(r),rl(r,o)}}}break;case"textarea":Bd(e,n);break;case"select":t=n.value,t!=null&&Kn(e,!!n.multiple,t,!1)}};qd=Ka;Gd=kn;var tv={usingClientEntryPoint:!1,Events:[po,Dn,qi,Wd,Kd,Ka]},Er={findFiberByHostInstance:cn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nv={bundleType:Er.bundleType,version:Er.version,rendererPackageName:Er.rendererPackageName,rendererConfig:Er.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Rt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yd(e),e===null?null:e.stateNode},findFiberByHostInstance:Er.findFiberByHostInstance||Jw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $o=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$o.isDisabled&&$o.supportsFiber)try{Vi=$o.inject(nv),vt=$o}catch{}}We.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tv;We.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ja(t))throw Error(P(200));return Zw(e,t,null,n)};We.createRoot=function(e,t){if(!Ja(e))throw Error(P(299));var n=!1,r="",o=Af;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Xa(e,1,!1,null,null,n,!1,r,o),e[Lt]=t.current,Xr(e.nodeType===8?e.parentNode:e),new Za(t)};We.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Yd(t),e=e===null?null:e.stateNode,e};We.flushSync=function(e){return kn(e)};We.hydrate=function(e,t,n){if(!ns(t))throw Error(P(200));return rs(null,e,t,!0,n)};We.hydrateRoot=function(e,t,n){if(!Ja(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Af;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=If(t,null,e,1,n??null,o,!1,i,s),e[Lt]=t.current,Xr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ts(t)};We.render=function(e,t,n){if(!ns(t))throw Error(P(200));return rs(null,e,t,!1,n)};We.unmountComponentAtNode=function(e){if(!ns(e))throw Error(P(40));return e._reactRootContainer?(kn(function(){rs(null,null,e,!1,function(){e._reactRootContainer=null,e[Lt]=null})}),!0):!1};We.unstable_batchedUpdates=Ka;We.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ns(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return rs(e,t,n,!1,r)};We.version="18.3.1-next-f1338f8080-20240426";function Lf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Lf)}catch(e){console.error(e)}}Lf(),Ad.exports=We;var os=Ad.exports;const rv=wd(os);var Oc=os;Xs.createRoot=Oc.createRoot,Xs.hydrateRoot=Oc.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function io(){return io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},io.apply(this,arguments)}var Vt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Vt||(Vt={}));const Rc="popstate";function ov(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:l}=r.location;return Vl("",{pathname:i,search:s,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Of(o)}return sv(t,n,null,e)}function de(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function jf(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function iv(){return Math.random().toString(36).substr(2,8)}function zc(e,t){return{usr:e.state,key:e.key,idx:t}}function Vl(e,t,n,r){return n===void 0&&(n=null),io({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?pr(t):t,{state:n,key:t&&t.key||r||iv()})}function Of(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function pr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function sv(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,l=Vt.Pop,a=null,u=h();u==null&&(u=0,s.replaceState(io({},s.state,{idx:u}),""));function h(){return(s.state||{idx:null}).idx}function p(){l=Vt.Pop;let x=h(),c=x==null?null:x-u;u=x,a&&a({action:l,location:y.location,delta:c})}function v(x,c){l=Vt.Push;let d=Vl(y.location,x,c);n&&n(d,x),u=h()+1;let m=zc(d,u),C=y.createHref(d);try{s.pushState(m,"",C)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;o.location.assign(C)}i&&a&&a({action:l,location:y.location,delta:1})}function g(x,c){l=Vt.Replace;let d=Vl(y.location,x,c);n&&n(d,x),u=h();let m=zc(d,u),C=y.createHref(d);s.replaceState(m,"",C),i&&a&&a({action:l,location:y.location,delta:0})}function w(x){let c=o.location.origin!=="null"?o.location.origin:o.location.href,d=typeof x=="string"?x:Of(x);return d=d.replace(/ $/,"%20"),de(c,"No window.location.(origin|href) available to create URL for href: "+d),new URL(d,c)}let y={get action(){return l},get location(){return e(o,s)},listen(x){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(Rc,p),a=x,()=>{o.removeEventListener(Rc,p),a=null}},createHref(x){return t(o,x)},createURL:w,encodeLocation(x){let c=w(x);return{pathname:c.pathname,search:c.search,hash:c.hash}},push:v,replace:g,go(x){return s.go(x)}};return y}var _c;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(_c||(_c={}));function lv(e,t,n){return n===void 0&&(n="/"),av(e,t,n,!1)}function av(e,t,n,r){let o=typeof t=="string"?pr(t):t,i=_f(o.pathname||"/",n);if(i==null)return null;let s=Rf(e);uv(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=xv(i);l=vv(s[a],u,r)}return l}function Rf(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};a.relativePath.startsWith("/")&&(de(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=wn([r,a.relativePath]),h=n.concat(a);i.children&&i.children.length>0&&(de(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Rf(i.children,t,h,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:gv(u,i.index),routesMeta:h})};return e.forEach((i,s)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,s);else for(let a of zf(i.path))o(i,s,a)}),t}function zf(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=zf(r.join("/")),l=[];return l.push(...s.map(a=>a===""?i:[i,a].join("/"))),o&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function uv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:wv(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const cv=/^:[\w-]+$/,dv=3,pv=2,fv=1,mv=10,hv=-2,Mc=e=>e==="*";function gv(e,t){let n=e.split("/"),r=n.length;return n.some(Mc)&&(r+=hv),t&&(r+=pv),n.filter(o=>!Mc(o)).reduce((o,i)=>o+(cv.test(i)?dv:i===""?fv:mv),r)}function wv(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function vv(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,o={},i="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,h=i==="/"?t:t.slice(i.length)||"/",p=$c({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},h),v=a.route;if(!p&&u&&n&&!r[r.length-1].route.index&&(p=$c({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},h)),!p)return null;Object.assign(o,p.params),s.push({params:o,pathname:wn([i,p.pathname]),pathnameBase:Tv(wn([i,p.pathnameBase])),route:v}),p.pathnameBase!=="/"&&(i=wn([i,p.pathnameBase]))}return s}function $c(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=yv(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,h,p)=>{let{paramName:v,isOptional:g}=h;if(v==="*"){let y=l[p]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const w=l[p];return g&&!w?u[v]=void 0:u[v]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function yv(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),jf(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function xv(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return jf(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function _f(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function bv(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?pr(e):e;return{pathname:n?n.startsWith("/")?n:Cv(n,t):t,search:Pv(r),hash:Nv(o)}}function Cv(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Fs(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function kv(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Sv(e,t){let n=kv(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Ev(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=pr(e):(o=io({},e),de(!o.pathname||!o.pathname.includes("?"),Fs("?","pathname","search",o)),de(!o.pathname||!o.pathname.includes("#"),Fs("#","pathname","hash",o)),de(!o.search||!o.search.includes("#"),Fs("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,l;if(s==null)l=n;else{let p=t.length-1;if(!r&&s.startsWith("..")){let v=s.split("/");for(;v[0]==="..";)v.shift(),p-=1;o.pathname=v.join("/")}l=p>=0?t[p]:"/"}let a=bv(o,l),u=s&&s!=="/"&&s.endsWith("/"),h=(i||s===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||h)&&(a.pathname+="/"),a}const wn=e=>e.join("/").replace(/\/\/+/g,"/"),Tv=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Pv=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Nv=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Iv(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Mf=["post","put","patch","delete"];new Set(Mf);const Av=["get",...Mf];new Set(Av);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function so(){return so=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},so.apply(this,arguments)}const eu=b.createContext(null),Lv=b.createContext(null),is=b.createContext(null),ss=b.createContext(null),fr=b.createContext({outlet:null,matches:[],isDataRoute:!1}),$f=b.createContext(null);function ls(){return b.useContext(ss)!=null}function tu(){return ls()||de(!1),b.useContext(ss).location}function Df(e){b.useContext(is).static||b.useLayoutEffect(e)}function Bf(){let{isDataRoute:e}=b.useContext(fr);return e?Hv():jv()}function jv(){ls()||de(!1);let e=b.useContext(eu),{basename:t,future:n,navigator:r}=b.useContext(is),{matches:o}=b.useContext(fr),{pathname:i}=tu(),s=JSON.stringify(Sv(o,n.v7_relativeSplatPath)),l=b.useRef(!1);return Df(()=>{l.current=!0}),b.useCallback(function(u,h){if(h===void 0&&(h={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let p=Ev(u,JSON.parse(s),i,h.relative==="path");e==null&&t!=="/"&&(p.pathname=p.pathname==="/"?t:wn([t,p.pathname])),(h.replace?r.replace:r.push)(p,h.state,h)},[t,r,s,i,e])}function Ov(e,t){return Rv(e,t)}function Rv(e,t,n,r){ls()||de(!1);let{navigator:o}=b.useContext(is),{matches:i}=b.useContext(fr),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=tu(),h;if(t){var p;let x=typeof t=="string"?pr(t):t;a==="/"||(p=x.pathname)!=null&&p.startsWith(a)||de(!1),h=x}else h=u;let v=h.pathname||"/",g=v;if(a!=="/"){let x=a.replace(/^\//,"").split("/");g="/"+v.replace(/^\//,"").split("/").slice(x.length).join("/")}let w=lv(e,{pathname:g}),y=Dv(w&&w.map(x=>Object.assign({},x,{params:Object.assign({},l,x.params),pathname:wn([a,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?a:wn([a,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),i,n,r);return t&&y?b.createElement(ss.Provider,{value:{location:so({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:Vt.Pop}},y):y}function zv(){let e=Vv(),t=Iv(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return b.createElement(b.Fragment,null,b.createElement("h2",null,"Unexpected Application Error!"),b.createElement("h3",{style:{fontStyle:"italic"}},t),n?b.createElement("pre",{style:o},n):null,i)}const _v=b.createElement(zv,null);class Mv extends b.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?b.createElement(fr.Provider,{value:this.props.routeContext},b.createElement($f.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function $v(e){let{routeContext:t,match:n,children:r}=e,o=b.useContext(eu);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),b.createElement(fr.Provider,{value:t},r)}function Dv(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let h=s.findIndex(p=>p.route.id&&(l==null?void 0:l[p.route.id])!==void 0);h>=0||de(!1),s=s.slice(0,Math.min(s.length,h+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<s.length;h++){let p=s[h];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(u=h),p.route.id){let{loaderData:v,errors:g}=n,w=p.route.loader&&v[p.route.id]===void 0&&(!g||g[p.route.id]===void 0);if(p.route.lazy||w){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((h,p,v)=>{let g,w=!1,y=null,x=null;n&&(g=l&&p.route.id?l[p.route.id]:void 0,y=p.route.errorElement||_v,a&&(u<0&&v===0?(Wv("route-fallback",!1),w=!0,x=null):u===v&&(w=!0,x=p.route.hydrateFallbackElement||null)));let c=t.concat(s.slice(0,v+1)),d=()=>{let m;return g?m=y:w?m=x:p.route.Component?m=b.createElement(p.route.Component,null):p.route.element?m=p.route.element:m=h,b.createElement($v,{match:p,routeContext:{outlet:h,matches:c,isDataRoute:n!=null},children:m})};return n&&(p.route.ErrorBoundary||p.route.errorElement||v===0)?b.createElement(Mv,{location:n.location,revalidation:n.revalidation,component:y,error:g,children:d(),routeContext:{outlet:null,matches:c,isDataRoute:!0}}):d()},null)}var Ff=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ff||{}),Ai=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ai||{});function Bv(e){let t=b.useContext(eu);return t||de(!1),t}function Fv(e){let t=b.useContext(Lv);return t||de(!1),t}function Uv(e){let t=b.useContext(fr);return t||de(!1),t}function Uf(e){let t=Uv(),n=t.matches[t.matches.length-1];return n.route.id||de(!1),n.route.id}function Vv(){var e;let t=b.useContext($f),n=Fv(Ai.UseRouteError),r=Uf(Ai.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Hv(){let{router:e}=Bv(Ff.UseNavigateStable),t=Uf(Ai.UseNavigateStable),n=b.useRef(!1);return Df(()=>{n.current=!0}),b.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,so({fromRouteId:t},i)))},[e,t])}const Dc={};function Wv(e,t,n){!t&&!Dc[e]&&(Dc[e]=!0)}function Kv(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Hl(e){de(!1)}function qv(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Vt.Pop,navigator:i,static:s=!1,future:l}=e;ls()&&de(!1);let a=t.replace(/^\/*/,"/"),u=b.useMemo(()=>({basename:a,navigator:i,static:s,future:so({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=pr(r));let{pathname:h="/",search:p="",hash:v="",state:g=null,key:w="default"}=r,y=b.useMemo(()=>{let x=_f(h,a);return x==null?null:{location:{pathname:x,search:p,hash:v,state:g,key:w},navigationType:o}},[a,h,p,v,g,w,o]);return y==null?null:b.createElement(is.Provider,{value:u},b.createElement(ss.Provider,{children:n,value:y}))}function Gv(e){let{children:t,location:n}=e;return Ov(Wl(t),n)}new Promise(()=>{});function Wl(e,t){t===void 0&&(t=[]);let n=[];return b.Children.forEach(e,(r,o)=>{if(!b.isValidElement(r))return;let i=[...t,o];if(r.type===b.Fragment){n.push.apply(n,Wl(r.props.children,i));return}r.type!==Hl&&de(!1),!r.props.index||!r.props.children||de(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Wl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Qv="6";try{window.__reactRouterVersion=Qv}catch{}const Xv="startTransition",Bc=Nd[Xv];function Yv(e){let{basename:t,children:n,future:r,window:o}=e,i=b.useRef();i.current==null&&(i.current=ov({window:o,v5Compat:!0}));let s=i.current,[l,a]=b.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},h=b.useCallback(p=>{u&&Bc?Bc(()=>a(p)):a(p)},[a,u]);return b.useLayoutEffect(()=>s.listen(h),[s,h]),b.useEffect(()=>Kv(r),[r]),b.createElement(qv,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}var Fc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Fc||(Fc={}));var Uc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Uc||(Uc={}));const Zv=1,Jv=1e6;let Us=0;function e0(){return Us=(Us+1)%Number.MAX_SAFE_INTEGER,Us.toString()}const Vs=new Map,Vc=e=>{if(Vs.has(e))return;const t=setTimeout(()=>{Vs.delete(e),Fr({type:"REMOVE_TOAST",toastId:e})},Jv);Vs.set(e,t)},t0=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Zv)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Vc(n):e.toasts.forEach(r=>{Vc(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},ri=[];let oi={toasts:[]};function Fr(e){oi=t0(oi,e),ri.forEach(t=>{t(oi)})}function n0({...e}){const t=e0(),n=o=>Fr({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Fr({type:"DISMISS_TOAST",toastId:t});return Fr({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Vf(){const[e,t]=b.useState(oi);return b.useEffect(()=>(ri.push(t),()=>{const n=ri.indexOf(t);n>-1&&ri.splice(n,1)}),[e]),{...e,toast:n0,dismiss:n=>Fr({type:"DISMISS_TOAST",toastId:n})}}function Be(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Hc(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Hf(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Hc(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Hc(e[o],null)}}}}function Sn(...e){return b.useCallback(Hf(...e),e)}function Wf(e,t=[]){let n=[];function r(i,s){const l=b.createContext(s),a=n.length;n=[...n,s];const u=p=>{var c;const{scope:v,children:g,...w}=p,y=((c=v==null?void 0:v[e])==null?void 0:c[a])||l,x=b.useMemo(()=>w,Object.values(w));return f.jsx(y.Provider,{value:x,children:g})};u.displayName=i+"Provider";function h(p,v){var y;const g=((y=v==null?void 0:v[e])==null?void 0:y[a])||l,w=b.useContext(g);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${p}\` must be used within \`${i}\``)}return[u,h]}const o=()=>{const i=n.map(s=>b.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return b.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,r0(o,...t)]}function r0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const p=a(i)[`__scope${u}`];return{...l,...p}},{});return b.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Li(e){const t=o0(e),n=b.forwardRef((r,o)=>{const{children:i,...s}=r,l=b.Children.toArray(i),a=l.find(s0);if(a){const u=a.props.children,h=l.map(p=>p===a?b.Children.count(u)>1?b.Children.only(null):b.isValidElement(u)?u.props.children:null:p);return f.jsx(t,{...s,ref:o,children:b.isValidElement(u)?b.cloneElement(u,void 0,h):null})}return f.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var Kf=Li("Slot");function o0(e){const t=b.forwardRef((n,r)=>{const{children:o,...i}=n;if(b.isValidElement(o)){const s=a0(o),l=l0(i,o.props);return o.type!==b.Fragment&&(l.ref=r?Hf(r,s):s),b.cloneElement(o,l)}return b.Children.count(o)>1?b.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var i0=Symbol("radix.slottable");function s0(e){return b.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===i0}function l0(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{const a=i(...l);return o(...l),a}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function a0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function u0(e){const t=e+"CollectionProvider",[n,r]=Wf(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=y=>{const{scope:x,children:c}=y,d=bt.useRef(null),m=bt.useRef(new Map).current;return f.jsx(o,{scope:x,itemMap:m,collectionRef:d,children:c})};s.displayName=t;const l=e+"CollectionSlot",a=Li(l),u=bt.forwardRef((y,x)=>{const{scope:c,children:d}=y,m=i(l,c),C=Sn(x,m.collectionRef);return f.jsx(a,{ref:C,children:d})});u.displayName=l;const h=e+"CollectionItemSlot",p="data-radix-collection-item",v=Li(h),g=bt.forwardRef((y,x)=>{const{scope:c,children:d,...m}=y,C=bt.useRef(null),S=Sn(x,C),T=i(h,c);return bt.useEffect(()=>(T.itemMap.set(C,{ref:C,...m}),()=>void T.itemMap.delete(C))),f.jsx(v,{[p]:"",ref:S,children:d})});g.displayName=h;function w(y){const x=i(e+"CollectionConsumer",y);return bt.useCallback(()=>{const d=x.collectionRef.current;if(!d)return[];const m=Array.from(d.querySelectorAll(`[${p}]`));return Array.from(x.itemMap.values()).sort((T,E)=>m.indexOf(T.ref.current)-m.indexOf(E.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:s,Slot:u,ItemSlot:g},w,r]}var c0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pt=c0.reduce((e,t)=>{const n=Li(`Primitive.${t}`),r=b.forwardRef((o,i)=>{const{asChild:s,...l}=o,a=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(a,{...l,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function qf(e,t){e&&os.flushSync(()=>e.dispatchEvent(t))}function En(e){const t=b.useRef(e);return b.useEffect(()=>{t.current=e}),b.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function d0(e,t=globalThis==null?void 0:globalThis.document){const n=En(e);b.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var p0="DismissableLayer",Kl="dismissableLayer.update",f0="dismissableLayer.pointerDownOutside",m0="dismissableLayer.focusOutside",Wc,Gf=b.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Qf=b.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=b.useContext(Gf),[h,p]=b.useState(null),v=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=b.useState({}),w=Sn(t,E=>p(E)),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),c=y.indexOf(x),d=h?y.indexOf(h):-1,m=u.layersWithOutsidePointerEventsDisabled.size>0,C=d>=c,S=g0(E=>{const N=E.target,M=[...u.branches].some(O=>O.contains(N));!C||M||(o==null||o(E),s==null||s(E),E.defaultPrevented||l==null||l())},v),T=w0(E=>{const N=E.target;[...u.branches].some(O=>O.contains(N))||(i==null||i(E),s==null||s(E),E.defaultPrevented||l==null||l())},v);return d0(E=>{d===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&l&&(E.preventDefault(),l()))},v),b.useEffect(()=>{if(h)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Wc=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(h)),u.layers.add(h),Kc(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=Wc)}},[h,v,n,u]),b.useEffect(()=>()=>{h&&(u.layers.delete(h),u.layersWithOutsidePointerEventsDisabled.delete(h),Kc())},[h,u]),b.useEffect(()=>{const E=()=>g({});return document.addEventListener(Kl,E),()=>document.removeEventListener(Kl,E)},[]),f.jsx(pt.div,{...a,ref:w,style:{pointerEvents:m?C?"auto":"none":void 0,...e.style},onFocusCapture:Be(e.onFocusCapture,T.onFocusCapture),onBlurCapture:Be(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:Be(e.onPointerDownCapture,S.onPointerDownCapture)})});Qf.displayName=p0;var h0="DismissableLayerBranch",Xf=b.forwardRef((e,t)=>{const n=b.useContext(Gf),r=b.useRef(null),o=Sn(t,r);return b.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),f.jsx(pt.div,{...e,ref:o})});Xf.displayName=h0;function g0(e,t=globalThis==null?void 0:globalThis.document){const n=En(e),r=b.useRef(!1),o=b.useRef(()=>{});return b.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){Yf(f0,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function w0(e,t=globalThis==null?void 0:globalThis.document){const n=En(e),r=b.useRef(!1);return b.useEffect(()=>{const o=i=>{i.target&&!r.current&&Yf(m0,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Kc(){const e=new CustomEvent(Kl);document.dispatchEvent(e)}function Yf(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?qf(o,i):o.dispatchEvent(i)}var v0=Qf,y0=Xf,lo=globalThis!=null&&globalThis.document?b.useLayoutEffect:()=>{},x0="Portal",Zf=b.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=b.useState(!1);lo(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?rv.createPortal(f.jsx(pt.div,{...r,ref:t}),s):null});Zf.displayName=x0;function b0(e,t){return b.useReducer((n,r)=>t[n][r]??n,e)}var Jf=e=>{const{present:t,children:n}=e,r=C0(t),o=typeof n=="function"?n({present:r.isPresent}):b.Children.only(n),i=Sn(r.ref,k0(o));return typeof n=="function"||r.isPresent?b.cloneElement(o,{ref:i}):null};Jf.displayName="Presence";function C0(e){const[t,n]=b.useState(),r=b.useRef(null),o=b.useRef(e),i=b.useRef("none"),s=e?"mounted":"unmounted",[l,a]=b0(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return b.useEffect(()=>{const u=Do(r.current);i.current=l==="mounted"?u:"none"},[l]),lo(()=>{const u=r.current,h=o.current;if(h!==e){const v=i.current,g=Do(u);e?a("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(h&&v!==g?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),lo(()=>{if(t){let u;const h=t.ownerDocument.defaultView??window,p=g=>{const y=Do(r.current).includes(g.animationName);if(g.target===t&&y&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=h.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},v=g=>{g.target===t&&(i.current=Do(r.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{h.clearTimeout(u),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:b.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function Do(e){return(e==null?void 0:e.animationName)||"none"}function k0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var S0=Nd[" useInsertionEffect ".trim().toString()]||lo;function E0({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=T0({defaultProp:t,onChange:n}),l=e!==void 0,a=l?e:o;{const h=b.useRef(e!==void 0);b.useEffect(()=>{const p=h.current;p!==l&&console.warn(`${r} is changing from ${p?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),h.current=l},[l,r])}const u=b.useCallback(h=>{var p;if(l){const v=P0(h)?h(e):h;v!==e&&((p=s.current)==null||p.call(s,v))}else i(h)},[l,e,i,s]);return[a,u]}function T0({defaultProp:e,onChange:t}){const[n,r]=b.useState(e),o=b.useRef(n),i=b.useRef(t);return S0(()=>{i.current=t},[t]),b.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function P0(e){return typeof e=="function"}var N0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),I0="VisuallyHidden",nu=b.forwardRef((e,t)=>f.jsx(pt.span,{...e,ref:t,style:{...N0,...e.style}}));nu.displayName=I0;var ru="ToastProvider",[ou,A0,L0]=u0("Toast"),[em,H2]=Wf("Toast",[L0]),[j0,as]=em(ru),tm=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[l,a]=b.useState(null),[u,h]=b.useState(0),p=b.useRef(!1),v=b.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${ru}\`. Expected non-empty \`string\`.`),f.jsx(ou.Provider,{scope:t,children:f.jsx(j0,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:b.useCallback(()=>h(g=>g+1),[]),onToastRemove:b.useCallback(()=>h(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:s})})};tm.displayName=ru;var nm="ToastViewport",O0=["F8"],ql="toast.viewportPause",Gl="toast.viewportResume",rm=b.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=O0,label:o="Notifications ({hotkey})",...i}=e,s=as(nm,n),l=A0(n),a=b.useRef(null),u=b.useRef(null),h=b.useRef(null),p=b.useRef(null),v=Sn(t,p,s.onViewportChange),g=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=s.toastCount>0;b.useEffect(()=>{const x=c=>{var m;r.length!==0&&r.every(C=>c[C]||c.code===C)&&((m=p.current)==null||m.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),b.useEffect(()=>{const x=a.current,c=p.current;if(w&&x&&c){const d=()=>{if(!s.isClosePausedRef.current){const T=new CustomEvent(ql);c.dispatchEvent(T),s.isClosePausedRef.current=!0}},m=()=>{if(s.isClosePausedRef.current){const T=new CustomEvent(Gl);c.dispatchEvent(T),s.isClosePausedRef.current=!1}},C=T=>{!x.contains(T.relatedTarget)&&m()},S=()=>{x.contains(document.activeElement)||m()};return x.addEventListener("focusin",d),x.addEventListener("focusout",C),x.addEventListener("pointermove",d),x.addEventListener("pointerleave",S),window.addEventListener("blur",d),window.addEventListener("focus",m),()=>{x.removeEventListener("focusin",d),x.removeEventListener("focusout",C),x.removeEventListener("pointermove",d),x.removeEventListener("pointerleave",S),window.removeEventListener("blur",d),window.removeEventListener("focus",m)}}},[w,s.isClosePausedRef]);const y=b.useCallback(({tabbingDirection:x})=>{const d=l().map(m=>{const C=m.ref.current,S=[C,...K0(C)];return x==="forwards"?S:S.reverse()});return(x==="forwards"?d.reverse():d).flat()},[l]);return b.useEffect(()=>{const x=p.current;if(x){const c=d=>{var S,T,E;const m=d.altKey||d.ctrlKey||d.metaKey;if(d.key==="Tab"&&!m){const N=document.activeElement,M=d.shiftKey;if(d.target===x&&M){(S=u.current)==null||S.focus();return}const $=y({tabbingDirection:M?"backwards":"forwards"}),Ee=$.findIndex(z=>z===N);Hs($.slice(Ee+1))?d.preventDefault():M?(T=u.current)==null||T.focus():(E=h.current)==null||E.focus()}};return x.addEventListener("keydown",c),()=>x.removeEventListener("keydown",c)}},[l,y]),f.jsxs(y0,{ref:a,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&f.jsx(Ql,{ref:u,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});Hs(x)}}),f.jsx(ou.Slot,{scope:n,children:f.jsx(pt.ol,{tabIndex:-1,...i,ref:v})}),w&&f.jsx(Ql,{ref:h,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});Hs(x)}})]})});rm.displayName=nm;var om="ToastFocusProxy",Ql=b.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=as(om,n);return f.jsx(nu,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const l=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});Ql.displayName=om;var mo="Toast",R0="toast.swipeStart",z0="toast.swipeMove",_0="toast.swipeCancel",M0="toast.swipeEnd",im=b.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[l,a]=E0({prop:r,defaultProp:o??!0,onChange:i,caller:mo});return f.jsx(Jf,{present:n||l,children:f.jsx(B0,{open:l,...s,ref:t,onClose:()=>a(!1),onPause:En(e.onPause),onResume:En(e.onResume),onSwipeStart:Be(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Be(e.onSwipeMove,u=>{const{x:h,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${h}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:Be(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Be(e.onSwipeEnd,u=>{const{x:h,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${h}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),a(!1)})})})});im.displayName=mo;var[$0,D0]=em(mo,{onClose(){}}),B0=b.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:h,onSwipeMove:p,onSwipeCancel:v,onSwipeEnd:g,...w}=e,y=as(mo,n),[x,c]=b.useState(null),d=Sn(t,z=>c(z)),m=b.useRef(null),C=b.useRef(null),S=o||y.duration,T=b.useRef(0),E=b.useRef(S),N=b.useRef(0),{onToastAdd:M,onToastRemove:O}=y,K=En(()=>{var he;(x==null?void 0:x.contains(document.activeElement))&&((he=y.viewport)==null||he.focus()),s()}),$=b.useCallback(z=>{!z||z===1/0||(window.clearTimeout(N.current),T.current=new Date().getTime(),N.current=window.setTimeout(K,z))},[K]);b.useEffect(()=>{const z=y.viewport;if(z){const he=()=>{$(E.current),u==null||u()},ge=()=>{const Qe=new Date().getTime()-T.current;E.current=E.current-Qe,window.clearTimeout(N.current),a==null||a()};return z.addEventListener(ql,ge),z.addEventListener(Gl,he),()=>{z.removeEventListener(ql,ge),z.removeEventListener(Gl,he)}}},[y.viewport,S,a,u,$]),b.useEffect(()=>{i&&!y.isClosePausedRef.current&&$(S)},[i,S,y.isClosePausedRef,$]),b.useEffect(()=>(M(),()=>O()),[M,O]);const Ee=b.useMemo(()=>x?pm(x):null,[x]);return y.viewport?f.jsxs(f.Fragment,{children:[Ee&&f.jsx(F0,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:Ee}),f.jsx($0,{scope:n,onClose:K,children:os.createPortal(f.jsx(ou.ItemSlot,{scope:n,children:f.jsx(v0,{asChild:!0,onEscapeKeyDown:Be(l,()=>{y.isFocusedToastEscapeKeyDownRef.current||K(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:f.jsx(pt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...w,ref:d,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Be(e.onKeyDown,z=>{z.key==="Escape"&&(l==null||l(z.nativeEvent),z.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,K()))}),onPointerDown:Be(e.onPointerDown,z=>{z.button===0&&(m.current={x:z.clientX,y:z.clientY})}),onPointerMove:Be(e.onPointerMove,z=>{if(!m.current)return;const he=z.clientX-m.current.x,ge=z.clientY-m.current.y,Qe=!!C.current,I=["left","right"].includes(y.swipeDirection),L=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,_=I?L(0,he):0,H=I?0:L(0,ge),q=z.pointerType==="touch"?10:2,ee={x:_,y:H},ft={originalEvent:z,delta:ee};Qe?(C.current=ee,Bo(z0,p,ft,{discrete:!1})):qc(ee,y.swipeDirection,q)?(C.current=ee,Bo(R0,h,ft,{discrete:!1}),z.target.setPointerCapture(z.pointerId)):(Math.abs(he)>q||Math.abs(ge)>q)&&(m.current=null)}),onPointerUp:Be(e.onPointerUp,z=>{const he=C.current,ge=z.target;if(ge.hasPointerCapture(z.pointerId)&&ge.releasePointerCapture(z.pointerId),C.current=null,m.current=null,he){const Qe=z.currentTarget,I={originalEvent:z,delta:he};qc(he,y.swipeDirection,y.swipeThreshold)?Bo(M0,g,I,{discrete:!0}):Bo(_0,v,I,{discrete:!0}),Qe.addEventListener("click",L=>L.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),F0=e=>{const{__scopeToast:t,children:n,...r}=e,o=as(mo,t),[i,s]=b.useState(!1),[l,a]=b.useState(!1);return H0(()=>s(!0)),b.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:f.jsx(Zf,{asChild:!0,children:f.jsx(nu,{...r,children:i&&f.jsxs(f.Fragment,{children:[o.label," ",n]})})})},U0="ToastTitle",sm=b.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return f.jsx(pt.div,{...r,ref:t})});sm.displayName=U0;var V0="ToastDescription",lm=b.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return f.jsx(pt.div,{...r,ref:t})});lm.displayName=V0;var am="ToastAction",um=b.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?f.jsx(dm,{altText:n,asChild:!0,children:f.jsx(iu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${am}\`. Expected non-empty \`string\`.`),null)});um.displayName=am;var cm="ToastClose",iu=b.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=D0(cm,n);return f.jsx(dm,{asChild:!0,children:f.jsx(pt.button,{type:"button",...r,ref:t,onClick:Be(e.onClick,o.onClose)})})});iu.displayName=cm;var dm=b.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return f.jsx(pt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function pm(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),W0(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...pm(r))}}),t}function Bo(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?qf(o,i):o.dispatchEvent(i)}var qc=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function H0(e=()=>{}){const t=En(e);lo(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function W0(e){return e.nodeType===e.ELEMENT_NODE}function K0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Hs(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var q0=tm,fm=rm,mm=im,hm=sm,gm=lm,wm=um,vm=iu;function ym(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=ym(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function xm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=ym(e))&&(r&&(r+=" "),r+=t);return r}const Gc=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Qc=xm,su=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Qc(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const h=n==null?void 0:n[u],p=i==null?void 0:i[u];if(h===null)return null;const v=Gc(h)||Gc(p);return o[u][v]}),l=n&&Object.entries(n).reduce((u,h)=>{let[p,v]=h;return v===void 0||(u[p]=v),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,h)=>{let{class:p,className:v,...g}=h;return Object.entries(g).every(w=>{let[y,x]=w;return Array.isArray(x)?x.includes({...i,...l}[y]):{...i,...l}[y]===x})?[...u,p,v]:u},[]);return Qc(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)};var G0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Q0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),X0=(e,t)=>{const n=b.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,children:l,...a},u)=>b.createElement("svg",{ref:u,...G0,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:`lucide lucide-${Q0(e)}`,...a},[...t.map(([h,p])=>b.createElement(h,p)),...(Array.isArray(l)?l:[l])||[]]));return n.displayName=`${e}`,n};var rt=X0;const Y0=rt("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Xc=rt("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Z0=rt("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),J0=rt("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ey=rt("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),Yc=rt("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),Zc=rt("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),ty=rt("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),ii=rt("Tv",[["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",ry:"2",key:"10ag99"}],["polyline",{points:"17 2 12 7 7 2",key:"11pgbg"}]]),Jc=rt("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),bm=rt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),ed=rt("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function ny(){for(var e=0,t,n,r="";e<arguments.length;)(t=arguments[e++])&&(n=Cm(t))&&(r&&(r+=" "),r+=n);return r}function Cm(e){if(typeof e=="string")return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=Cm(e[r]))&&(n&&(n+=" "),n+=t);return n}var lu="-";function ry(e){var t=iy(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,o=r===void 0?{}:r;function i(l){var a=l.split(lu);return a[0]===""&&a.length!==1&&a.shift(),km(a,t)||oy(l)}function s(l,a){var u=n[l]||[];return a&&o[l]?[].concat(u,o[l]):u}return{getClassGroupId:i,getConflictingClassGroupIds:s}}function km(e,t){var s;if(e.length===0)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),o=r?km(e.slice(1),r):void 0;if(o)return o;if(t.validators.length!==0){var i=e.join(lu);return(s=t.validators.find(function(l){var a=l.validator;return a(i)}))==null?void 0:s.classGroupId}}var td=/^\[(.+)\]$/;function oy(e){if(td.test(e)){var t=td.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function iy(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},o=ly(Object.entries(e.classGroups),n);return o.forEach(function(i){var s=i[0],l=i[1];Xl(l,r,s,t)}),r}function Xl(e,t,n,r){e.forEach(function(o){if(typeof o=="string"){var i=o===""?t:nd(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(sy(o)){Xl(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(function(s){var l=s[0],a=s[1];Xl(a,nd(t,l),n,r)})})}function nd(e,t){var n=e;return t.split(lu).forEach(function(r){n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function sy(e){return e.isThemeGetter}function ly(e,t){return t?e.map(function(n){var r=n[0],o=n[1],i=o.map(function(s){return typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(function(l){var a=l[0],u=l[1];return[t+a,u]})):s});return[r,i]}):e}function ay(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get:function(s){var l=n.get(s);if(l!==void 0)return l;if((l=r.get(s))!==void 0)return o(s,l),l},set:function(s,l){n.has(s)?n.set(s,l):o(s,l)}}}var Sm="!";function uy(e){var t=e.separator||":",n=t.length===1,r=t[0],o=t.length;return function(s){for(var l=[],a=0,u=0,h,p=0;p<s.length;p++){var v=s[p];if(a===0){if(v===r&&(n||s.slice(p,p+o)===t)){l.push(s.slice(u,p)),u=p+o;continue}if(v==="/"){h=p;continue}}v==="["?a++:v==="]"&&a--}var g=l.length===0?s:s.substring(u),w=g.startsWith(Sm),y=w?g.substring(1):g,x=h&&h>u?h-u:void 0;return{modifiers:l,hasImportantModifier:w,baseClassName:y,maybePostfixModifierPosition:x}}}function cy(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(r){var o=r[0]==="[";o?(t.push.apply(t,n.sort().concat([r])),n=[]):n.push(r)}),t.push.apply(t,n.sort()),t}function dy(e){return{cache:ay(e.cacheSize),splitModifiers:uy(e),...ry(e)}}var py=/\s+/;function fy(e,t){var n=t.splitModifiers,r=t.getClassGroupId,o=t.getConflictingClassGroupIds,i=new Set;return e.trim().split(py).map(function(s){var l=n(s),a=l.modifiers,u=l.hasImportantModifier,h=l.baseClassName,p=l.maybePostfixModifierPosition,v=r(p?h.substring(0,p):h),g=!!p;if(!v){if(!p)return{isTailwindClass:!1,originalClassName:s};if(v=r(h),!v)return{isTailwindClass:!1,originalClassName:s};g=!1}var w=cy(a).join(":"),y=u?w+Sm:w;return{isTailwindClass:!0,modifierId:y,classGroupId:v,originalClassName:s,hasPostfixModifier:g}}).reverse().filter(function(s){if(!s.isTailwindClass)return!0;var l=s.modifierId,a=s.classGroupId,u=s.hasPostfixModifier,h=l+a;return i.has(h)?!1:(i.add(h),o(a,u).forEach(function(p){return i.add(l+p)}),!0)}).reverse().map(function(s){return s.originalClassName}).join(" ")}function my(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,o,i,s=l;function l(u){var h=t[0],p=t.slice(1),v=p.reduce(function(g,w){return w(g)},h());return r=dy(v),o=r.cache.get,i=r.cache.set,s=a,a(u)}function a(u){var h=o(u);if(h)return h;var p=fy(u,r);return i(u,p),p}return function(){return s(ny.apply(null,arguments))}}function X(e){var t=function(r){return r[e]||[]};return t.isThemeGetter=!0,t}var Em=/^\[(?:([a-z-]+):)?(.+)\]$/i,hy=/^\d+\/\d+$/,gy=new Set(["px","full","screen"]),wy=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,vy=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,yy=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function it(e){return fn(e)||gy.has(e)||hy.test(e)||Yl(e)}function Yl(e){return An(e,"length",Ey)}function xy(e){return An(e,"size",Tm)}function by(e){return An(e,"position",Tm)}function Cy(e){return An(e,"url",Ty)}function Fo(e){return An(e,"number",fn)}function fn(e){return!Number.isNaN(Number(e))}function ky(e){return e.endsWith("%")&&fn(e.slice(0,-1))}function Tr(e){return rd(e)||An(e,"number",rd)}function B(e){return Em.test(e)}function Pr(){return!0}function _t(e){return wy.test(e)}function Sy(e){return An(e,"",Py)}function An(e,t,n){var r=Em.exec(e);return r?r[1]?r[1]===t:n(r[2]):!1}function Ey(e){return vy.test(e)}function Tm(){return!1}function Ty(e){return e.startsWith("url(")}function rd(e){return Number.isInteger(Number(e))}function Py(e){return yy.test(e)}function Ny(){var e=X("colors"),t=X("spacing"),n=X("blur"),r=X("brightness"),o=X("borderColor"),i=X("borderRadius"),s=X("borderSpacing"),l=X("borderWidth"),a=X("contrast"),u=X("grayscale"),h=X("hueRotate"),p=X("invert"),v=X("gap"),g=X("gradientColorStops"),w=X("gradientColorStopPositions"),y=X("inset"),x=X("margin"),c=X("opacity"),d=X("padding"),m=X("saturate"),C=X("scale"),S=X("sepia"),T=X("skew"),E=X("space"),N=X("translate"),M=function(){return["auto","contain","none"]},O=function(){return["auto","hidden","clip","visible","scroll"]},K=function(){return["auto",B,t]},$=function(){return[B,t]},Ee=function(){return["",it]},z=function(){return["auto",fn,B]},he=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},ge=function(){return["solid","dashed","dotted","double","none"]},Qe=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},I=function(){return["start","end","center","between","around","evenly","stretch"]},L=function(){return["","0",B]},_=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},H=function(){return[fn,Fo]},q=function(){return[fn,B]};return{cacheSize:500,theme:{colors:[Pr],spacing:[it],blur:["none","",_t,B],brightness:H(),borderColor:[e],borderRadius:["none","","full",_t,B],borderSpacing:$(),borderWidth:Ee(),contrast:H(),grayscale:L(),hueRotate:q(),invert:L(),gap:$(),gradientColorStops:[e],gradientColorStopPositions:[ky,Yl],inset:K(),margin:K(),opacity:H(),padding:$(),saturate:H(),scale:H(),sepia:L(),skew:q(),space:$(),translate:$()},classGroups:{aspect:[{aspect:["auto","square","video",B]}],container:["container"],columns:[{columns:[_t]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(he(),[B])}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:M()}],"overscroll-x":[{"overscroll-x":M()}],"overscroll-y":[{"overscroll-y":M()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Tr]}],basis:[{basis:K()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",B]}],grow:[{grow:L()}],shrink:[{shrink:L()}],order:[{order:["first","last","none",Tr]}],"grid-cols":[{"grid-cols":[Pr]}],"col-start-end":[{col:["auto",{span:["full",Tr]},B]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[Pr]}],"row-start-end":[{row:["auto",{span:[Tr]},B]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",B]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",B]}],gap:[{gap:[v]}],"gap-x":[{"gap-x":[v]}],"gap-y":[{"gap-y":[v]}],"justify-content":[{justify:["normal"].concat(I())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(I(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(I(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[d]}],px:[{px:[d]}],py:[{py:[d]}],ps:[{ps:[d]}],pe:[{pe:[d]}],pt:[{pt:[d]}],pr:[{pr:[d]}],pb:[{pb:[d]}],pl:[{pl:[d]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",B,t]}],"min-w":[{"min-w":["min","max","fit",B,it]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[_t]},_t,B]}],h:[{h:[B,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",B,it]}],"max-h":[{"max-h":[B,t,"min","max","fit"]}],"font-size":[{text:["base",_t,Yl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Fo]}],"font-family":[{font:[Pr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",B]}],"line-clamp":[{"line-clamp":["none",fn,Fo]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",B,it]}],"list-image":[{"list-image":["none",B]}],"list-style-type":[{list:["none","disc","decimal",B]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[c]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[c]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(ge(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",it]}],"underline-offset":[{"underline-offset":["auto",B,it]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[c]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(he(),[by])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",xy]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Cy]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[c]}],"border-style":[{border:[].concat(ge(),["hidden"])}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[c]}],"divide-style":[{divide:ge()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(ge())}],"outline-offset":[{"outline-offset":[B,it]}],"outline-w":[{outline:[it]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Ee()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[c]}],"ring-offset-w":[{"ring-offset":[it]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",_t,Sy]}],"shadow-color":[{shadow:[Pr]}],opacity:[{opacity:[c]}],"mix-blend":[{"mix-blend":Qe()}],"bg-blend":[{"bg-blend":Qe()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",_t,B]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[p]}],saturate:[{saturate:[m]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[c]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",B]}],duration:[{duration:q()}],ease:[{ease:["linear","in","out","in-out",B]}],delay:[{delay:q()}],animate:[{animate:["none","spin","ping","pulse","bounce",B]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[Tr,B]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",B]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[it,Fo]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var Iy=my(Ny);function Le(...e){return Iy(xm(e))}const Ay=q0,Pm=b.forwardRef(({className:e,...t},n)=>f.jsx(fm,{ref:n,className:Le("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Pm.displayName=fm.displayName;const Ly=su("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Nm=b.forwardRef(({className:e,variant:t,...n},r)=>f.jsx(mm,{ref:r,className:Le(Ly({variant:t}),e),...n}));Nm.displayName=mm.displayName;const jy=b.forwardRef(({className:e,...t},n)=>f.jsx(wm,{ref:n,className:Le("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));jy.displayName=wm.displayName;const Im=b.forwardRef(({className:e,...t},n)=>f.jsx(vm,{ref:n,className:Le("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:f.jsx(bm,{className:"h-4 w-4"})}));Im.displayName=vm.displayName;const Am=b.forwardRef(({className:e,...t},n)=>f.jsx(hm,{ref:n,className:Le("text-sm font-semibold [&+div]:text-xs",e),...t}));Am.displayName=hm.displayName;const Lm=b.forwardRef(({className:e,...t},n)=>f.jsx(gm,{ref:n,className:Le("text-sm opacity-90",e),...t}));Lm.displayName=gm.displayName;function Oy(){const{toasts:e}=Vf();return f.jsxs(Ay,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return f.jsxs(Nm,{...i,children:[f.jsxs("div",{className:"grid gap-1",children:[n&&f.jsx(Am,{children:n}),r&&f.jsx(Lm,{children:r})]}),o,f.jsx(Im,{})]},t)}),f.jsx(Pm,{})]})}const Ry=su("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function kt({className:e,variant:t,size:n,asChild:r=!1,...o}){const i=r?Kf:"button";return f.jsx(i,{"data-slot":"button",className:Le(Ry({variant:t,size:n,className:e})),...o})}function Zl({className:e,...t}){return f.jsx("div",{"data-slot":"card",className:Le("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function jm({className:e,...t}){return f.jsx("div",{"data-slot":"card-header",className:Le("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function Om({className:e,...t}){return f.jsx("div",{"data-slot":"card-title",className:Le("leading-none font-semibold",e),...t})}function Rm({className:e,...t}){return f.jsx("div",{"data-slot":"card-description",className:Le("text-muted-foreground text-sm",e),...t})}function Jl({className:e,...t}){return f.jsx("div",{"data-slot":"card-content",className:Le("px-6",e),...t})}const zy=su("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Uo({className:e,variant:t,asChild:n=!1,...r}){const o=n?Kf:"span";return f.jsx(o,{"data-slot":"badge",className:Le(zy({variant:t}),e),...r})}function od({className:e,type:t,...n}){return f.jsx("input",{type:t,"data-slot":"input",className:Le("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}var _y="Label",zm=b.forwardRef((e,t)=>f.jsx(pt.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));zm.displayName=_y;var My=zm;function Ws({className:e,...t}){return f.jsx(My,{"data-slot":"label",className:Le("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}const $y=typeof globalThis=="object"&&"window"in globalThis;class au{constructor(t,n){this.target=t,this.options=n??{};const r=new Hy(this.target,this.options);this.payment=new ea.ServiceClient(r),this.test=new ta.ServiceClient(r),this.whatsapp=new na.ServiceClient(r)}with(t){return new au(this.target,{...this.options,...t})}}var ea;(e=>{class t{constructor(r){this.baseClient=r,this.checkPayment=this.checkPayment.bind(this),this.createPix=this.createPix.bind(this),this.createTestPix=this.createTestPix.bind(this)}async checkPayment(r){const o=await this.baseClient.callTypedAPI(`/payment/${encodeURIComponent(r.paymentId)}/status`,{method:"GET",body:void 0});return JSON.parse(await o.text(),Nt)}async createPix(r){const o=await this.baseClient.callTypedAPI("/payment/pix",{method:"POST",body:JSON.stringify(r)});return JSON.parse(await o.text(),Nt)}async createTestPix(r){const o=await this.baseClient.callTypedAPI("/payment/test-pix",{method:"POST",body:JSON.stringify(r)});return JSON.parse(await o.text(),Nt)}}e.ServiceClient=t})(ea||(ea={}));var ta;(e=>{class t{constructor(r){this.baseClient=r,this.requestTest=this.requestTest.bind(this)}async requestTest(r){const o=await this.baseClient.callTypedAPI("/test/request",{method:"POST",body:JSON.stringify(r)});return JSON.parse(await o.text(),Nt)}}e.ServiceClient=t})(ta||(ta={}));var na;(e=>{class t{constructor(r){this.baseClient=r,this.sendReceipt=this.sendReceipt.bind(this),this.sendAdminNotification=this.sendAdminNotification.bind(this)}async sendReceipt(r){const o=await this.baseClient.callTypedAPI("/whatsapp/send-receipt",{method:"POST",body:JSON.stringify(r)});return JSON.parse(await o.text(),Nt)}async sendAdminNotification(r){const o=await this.baseClient.callTypedAPI("/whatsapp/send-admin-notification",{method:"POST",body:JSON.stringify(r)});return JSON.parse(await o.text(),Nt)}}e.ServiceClient=t})(na||(na={}));function Nt(e,t){if(typeof t=="string"&&t.length>=10&&t.charCodeAt(0)>=48&&t.charCodeAt(0)<=57){const n=new Date(t);if(!isNaN(n.getTime()))return n}return t}function Vo(e){const t=[];for(const n in e){const r=Array.isArray(e[n])?e[n]:[e[n]];for(const o of r)t.push(`${n}=${encodeURIComponent(o)}`)}return t.join("&")}function Dy(e){return"encore.dev.headers."+btoa(JSON.stringify(e)).replaceAll("=","").replaceAll("+","-").replaceAll("/","_")}class uu{constructor(t,n){this.hasUpdateHandlers=[];let r=["encore-ws"];n&&r.push(Dy(n)),this.ws=new WebSocket(t,r),this.on("error",()=>{this.resolveHasUpdateHandlers()}),this.on("close",()=>{this.resolveHasUpdateHandlers()})}resolveHasUpdateHandlers(){const t=this.hasUpdateHandlers;this.hasUpdateHandlers=[];for(const n of t)n()}async hasUpdate(){await new Promise(t=>{this.hasUpdateHandlers.push(()=>t(null))})}on(t,n){this.ws.addEventListener(t,n)}off(t,n){this.ws.removeEventListener(t,n)}close(){this.ws.close()}}class By{constructor(t,n){this.buffer=[],this.socket=new uu(t,n),this.socket.on("message",r=>{this.buffer.push(JSON.parse(r.data,Nt)),this.socket.resolveHasUpdateHandlers()})}close(){this.socket.close()}async send(t){return this.socket.ws.readyState===WebSocket.CONNECTING&&await new Promise(n=>{this.socket.ws.addEventListener("open",n,{once:!0})}),this.socket.ws.send(JSON.stringify(t))}async next(){for await(const t of this)return t}async*[Symbol.asyncIterator](){for(;;)if(this.buffer.length>0)yield this.buffer.shift();else{if(this.socket.ws.readyState===WebSocket.CLOSED)return;await this.socket.hasUpdate()}}}class Fy{constructor(t,n){this.buffer=[],this.socket=new uu(t,n),this.socket.on("message",r=>{this.buffer.push(JSON.parse(r.data,Nt)),this.socket.resolveHasUpdateHandlers()})}close(){this.socket.close()}async next(){for await(const t of this)return t}async*[Symbol.asyncIterator](){for(;;)if(this.buffer.length>0)yield this.buffer.shift();else{if(this.socket.ws.readyState===WebSocket.CLOSED)return;await this.socket.hasUpdate()}}}class Uy{constructor(t,n){let r;this.responseValue=new Promise(o=>r=o),this.socket=new uu(t,n),this.socket.on("message",o=>{r(JSON.parse(o.data,Nt))})}async response(){return this.responseValue}close(){this.socket.close()}async send(t){return this.socket.ws.readyState===WebSocket.CONNECTING&&await new Promise(n=>{this.socket.ws.addEventListener("open",n,{once:!0})}),this.socket.ws.send(JSON.stringify(t))}}const Vy=fetch.bind(void 0);class Hy{constructor(t,n){this.baseURL=t,this.headers={},$y||(this.headers["User-Agent"]="-Generated-TS-Client (Encore/1.48.8)"),this.requestInit=n.requestInit??{},n.fetcher!==void 0?this.fetcher=n.fetcher:this.fetcher=Vy}async getAuthData(){}async createStreamInOut(t,n){let{query:r,headers:o}=n??{};const i=await this.getAuthData();i&&(i.query&&(r={...r,...i.query}),i.headers&&(o={...o,...i.headers}));const s=r?"?"+Vo(r):"";return new By(this.baseURL+t+s,o)}async createStreamIn(t,n){let{query:r,headers:o}=n??{};const i=await this.getAuthData();i&&(i.query&&(r={...r,...i.query}),i.headers&&(o={...o,...i.headers}));const s=r?"?"+Vo(r):"";return new Fy(this.baseURL+t+s,o)}async createStreamOut(t,n){let{query:r,headers:o}=n??{};const i=await this.getAuthData();i&&(i.query&&(r={...r,...i.query}),i.headers&&(o={...o,...i.headers}));const s=r?"?"+Vo(r):"";return new Uy(this.baseURL+t+s,o)}async callTypedAPI(t,n){return this.callAPI(t,{...n,headers:{"Content-Type":"application/json",...n==null?void 0:n.headers}})}async callAPI(t,n){let{query:r,headers:o,...i}=n??{};const s={...this.requestInit,...i};s.headers={...this.headers,...s.headers,...o};const l=await this.getAuthData();l&&(l.query&&(r={...r,...l.query}),l.headers&&(s.headers={...s.headers,...l.headers}));const a=r?"?"+Vo(r):"",u=await this.fetcher(this.baseURL+t+a,s);if(!u.ok){let h={code:"unknown",message:`request failed: status ${u.status}`};try{const p=await u.text();try{const v=JSON.parse(p);Wy(v)?h=v:h.message+=": "+JSON.stringify(v)}catch{h.message+=": "+p}}catch(p){h.message+=": "+String(p)}throw new ji(u.status,h)}return u}}function Wy(e){return e!=null&&Ky(e.code)&&typeof e.message=="string"&&(e.details===void 0||e.details===null||typeof e.details=="object")}function Ky(e){return e!==void 0&&Object.values(_m).includes(e)}class ji extends Error{constructor(t,n){super(n.message),Object.defineProperty(this,"name",{value:"APIError",enumerable:!1,configurable:!0}),Object.setPrototypeOf==null?this.__proto__=ji.prototype:Object.setPrototypeOf(this,ji.prototype),Error.captureStackTrace!==void 0&&Error.captureStackTrace(this,this.constructor),this.status=t,this.code=n.code,this.details=n.details}}var _m=(e=>(e.OK="ok",e.Canceled="canceled",e.Unknown="unknown",e.InvalidArgument="invalid_argument",e.DeadlineExceeded="deadline_exceeded",e.NotFound="not_found",e.AlreadyExists="already_exists",e.PermissionDenied="permission_denied",e.ResourceExhausted="resource_exhausted",e.FailedPrecondition="failed_precondition",e.Aborted="aborted",e.OutOfRange="out_of_range",e.Unimplemented="unimplemented",e.Internal="internal",e.Unavailable="unavailable",e.DataLoss="data_loss",e.Unauthenticated="unauthenticated",e))(_m||{});const mn=new au("https://smartv.shop/api",{requestInit:{credentials:"include"}});/*!
* sweetalert2 v11.22.2
* Released under the MIT License.
*/function Mm(e,t,n){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function qy(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function id(e,t){return e.get(Mm(e,t))}function Gy(e,t,n){qy(e,t),t.set(e,n)}function Qy(e,t,n){return e.set(Mm(e,t),n),n}const Xy=100,j={},Yy=()=>{j.previousActiveElement instanceof HTMLElement?(j.previousActiveElement.focus(),j.previousActiveElement=null):document.body&&document.body.focus()},Zy=e=>new Promise(t=>{if(!e)return t();const n=window.scrollX,r=window.scrollY;j.restoreFocusTimeout=setTimeout(()=>{Yy(),t()},Xy),window.scrollTo(n,r)}),$m="swal2-",Jy=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],k=Jy.reduce((e,t)=>(e[t]=$m+t,e),{}),ex=["success","warning","info","question","error"],Oi=ex.reduce((e,t)=>(e[t]=$m+t,e),{}),Dm="SweetAlert2:",cu=e=>e.charAt(0).toUpperCase()+e.slice(1),Ne=e=>{console.warn(`${Dm} ${typeof e=="object"?e.join(" "):e}`)},Ln=e=>{console.error(`${Dm} ${e}`)},sd=[],tx=e=>{sd.includes(e)||(sd.push(e),Ne(e))},Bm=(e,t=null)=>{tx(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},us=e=>typeof e=="function"?e():e,du=e=>e&&typeof e.toPromise=="function",ho=e=>du(e)?e.toPromise():Promise.resolve(e),pu=e=>e&&Promise.resolve(e)===e,je=()=>document.body.querySelector(`.${k.container}`),go=e=>{const t=je();return t?t.querySelector(e):null},qe=e=>go(`.${e}`),U=()=>qe(k.popup),mr=()=>qe(k.icon),nx=()=>qe(k["icon-content"]),Fm=()=>qe(k.title),fu=()=>qe(k["html-container"]),Um=()=>qe(k.image),mu=()=>qe(k["progress-steps"]),cs=()=>qe(k["validation-message"]),xt=()=>go(`.${k.actions} .${k.confirm}`),hr=()=>go(`.${k.actions} .${k.cancel}`),jn=()=>go(`.${k.actions} .${k.deny}`),rx=()=>qe(k["input-label"]),gr=()=>go(`.${k.loader}`),wo=()=>qe(k.actions),Vm=()=>qe(k.footer),ds=()=>qe(k["timer-progress-bar"]),hu=()=>qe(k.close),ox=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,gu=()=>{const e=U();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),n=Array.from(t).sort((i,s)=>{const l=parseInt(i.getAttribute("tabindex")||"0"),a=parseInt(s.getAttribute("tabindex")||"0");return l>a?1:l<a?-1:0}),r=e.querySelectorAll(ox),o=Array.from(r).filter(i=>i.getAttribute("tabindex")!=="-1");return[...new Set(n.concat(o))].filter(i=>$e(i))},wu=()=>It(document.body,k.shown)&&!It(document.body,k["toast-shown"])&&!It(document.body,k["no-backdrop"]),ps=()=>{const e=U();return e?It(e,k.toast):!1},ix=()=>{const e=U();return e?e.hasAttribute("data-loading"):!1},Ge=(e,t)=>{if(e.textContent="",t){const r=new DOMParser().parseFromString(t,"text/html"),o=r.querySelector("head");o&&Array.from(o.childNodes).forEach(s=>{e.appendChild(s)});const i=r.querySelector("body");i&&Array.from(i.childNodes).forEach(s=>{s instanceof HTMLVideoElement||s instanceof HTMLAudioElement?e.appendChild(s.cloneNode(!0)):e.appendChild(s)})}},It=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let r=0;r<n.length;r++)if(!e.classList.contains(n[r]))return!1;return!0},sx=(e,t)=>{Array.from(e.classList).forEach(n=>{!Object.values(k).includes(n)&&!Object.values(Oi).includes(n)&&!Object.values(t.showClass||{}).includes(n)&&e.classList.remove(n)})},He=(e,t,n)=>{if(sx(e,t),!t.customClass)return;const r=t.customClass[n];if(r){if(typeof r!="string"&&!r.forEach){Ne(`Invalid type of customClass.${n}! Expected string or iterable object, got "${typeof r}"`);return}V(e,r)}},fs=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${k.popup} > .${k[t]}`);case"checkbox":return e.querySelector(`.${k.popup} > .${k.checkbox} input`);case"radio":return e.querySelector(`.${k.popup} > .${k.radio} input:checked`)||e.querySelector(`.${k.popup} > .${k.radio} input:first-child`);case"range":return e.querySelector(`.${k.popup} > .${k.range} input`);default:return e.querySelector(`.${k.popup} > .${k.input}`)}},Hm=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},Wm=(e,t,n)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(r=>{Array.isArray(e)?e.forEach(o=>{n?o.classList.add(r):o.classList.remove(r)}):n?e.classList.add(r):e.classList.remove(r)}))},V=(e,t)=>{Wm(e,t,!0)},nt=(e,t)=>{Wm(e,t,!1)},Ht=(e,t)=>{const n=Array.from(e.children);for(let r=0;r<n.length;r++){const o=n[r];if(o instanceof HTMLElement&&It(o,t))return o}},vn=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||parseInt(n)===0?e.style.setProperty(t,typeof n=="number"?`${n}px`:n):e.style.removeProperty(t)},me=(e,t="flex")=>{e&&(e.style.display=t)},Se=e=>{e&&(e.style.display="none")},vu=(e,t="block")=>{e&&new MutationObserver(()=>{vo(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},ld=(e,t,n,r)=>{const o=e.querySelector(t);o&&o.style.setProperty(n,r)},vo=(e,t,n="flex")=>{t?me(e,n):Se(e)},$e=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),lx=()=>!$e(xt())&&!$e(jn())&&!$e(hr()),ra=e=>e.scrollHeight>e.clientHeight,ax=(e,t)=>{let n=e;for(;n&&n!==t;){if(ra(n))return!0;n=n.parentElement}return!1},Km=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),r=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||r>0},yu=(e,t=!1)=>{const n=ds();n&&$e(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout(()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"},10))},ux=()=>{const e=ds();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=parseInt(window.getComputedStyle(e).width),r=t/n*100;e.style.width=`${r}%`},cx=()=>typeof window>"u"||typeof document>"u",dx=`
 <div aria-labelledby="${k.title}" aria-describedby="${k["html-container"]}" class="${k.popup}" tabindex="-1">
   <button type="button" class="${k.close}"></button>
   <ul class="${k["progress-steps"]}"></ul>
   <div class="${k.icon}"></div>
   <img class="${k.image}" />
   <h2 class="${k.title}" id="${k.title}"></h2>
   <div class="${k["html-container"]}" id="${k["html-container"]}"></div>
   <input class="${k.input}" id="${k.input}" />
   <input type="file" class="${k.file}" />
   <div class="${k.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${k.select}" id="${k.select}"></select>
   <div class="${k.radio}"></div>
   <label class="${k.checkbox}">
     <input type="checkbox" id="${k.checkbox}" />
     <span class="${k.label}"></span>
   </label>
   <textarea class="${k.textarea}" id="${k.textarea}"></textarea>
   <div class="${k["validation-message"]}" id="${k["validation-message"]}"></div>
   <div class="${k.actions}">
     <div class="${k.loader}"></div>
     <button type="button" class="${k.confirm}"></button>
     <button type="button" class="${k.deny}"></button>
     <button type="button" class="${k.cancel}"></button>
   </div>
   <div class="${k.footer}"></div>
   <div class="${k["timer-progress-bar-container"]}">
     <div class="${k["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),px=()=>{const e=je();return e?(e.remove(),nt([document.documentElement,document.body],[k["no-backdrop"],k["toast-shown"],k["has-column"]]),!0):!1},ln=()=>{j.currentInstance.resetValidationMessage()},fx=()=>{const e=U(),t=Ht(e,k.input),n=Ht(e,k.file),r=e.querySelector(`.${k.range} input`),o=e.querySelector(`.${k.range} output`),i=Ht(e,k.select),s=e.querySelector(`.${k.checkbox} input`),l=Ht(e,k.textarea);t.oninput=ln,n.onchange=ln,i.onchange=ln,s.onchange=ln,l.oninput=ln,r.oninput=()=>{ln(),o.value=r.value},r.onchange=()=>{ln(),o.value=r.value}},mx=e=>typeof e=="string"?document.querySelector(e):e,hx=e=>{const t=U();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},gx=e=>{window.getComputedStyle(e).direction==="rtl"&&V(je(),k.rtl)},wx=e=>{const t=px();if(cx()){Ln("SweetAlert2 requires document to initialize");return}const n=document.createElement("div");n.className=k.container,t&&V(n,k["no-transition"]),Ge(n,dx),n.dataset.swal2Theme=e.theme;const r=mx(e.target);r.appendChild(n),e.topLayer&&(n.setAttribute("popover",""),n.showPopover()),hx(e),gx(r),fx()},xu=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?vx(e,t):e&&Ge(t,e)},vx=(e,t)=>{e.jquery?yx(t,e):Ge(t,e.toString())},yx=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},xx=(e,t)=>{const n=wo(),r=gr();!n||!r||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?Se(n):me(n),He(n,t,"actions"),bx(n,r,t),Ge(r,t.loaderHtml||""),He(r,t,"loader"))};function bx(e,t,n){const r=xt(),o=jn(),i=hr();!r||!o||!i||(qs(r,"confirm",n),qs(o,"deny",n),qs(i,"cancel",n),Cx(r,o,i,n),n.reverseButtons&&(n.toast?(e.insertBefore(i,r),e.insertBefore(o,r)):(e.insertBefore(i,t),e.insertBefore(o,t),e.insertBefore(r,t))))}function Cx(e,t,n,r){if(!r.buttonsStyling){nt([e,t,n],k.styled);return}V([e,t,n],k.styled),r.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",r.confirmButtonColor),r.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",r.denyButtonColor),r.cancelButtonColor&&n.style.setProperty("--swal2-cancel-button-background-color",r.cancelButtonColor),Ks(e),Ks(t),Ks(n)}function Ks(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const n=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${n}`))}function qs(e,t,n){const r=cu(t);vo(e,n[`show${r}Button`],"inline-block"),Ge(e,n[`${t}ButtonText`]||""),e.setAttribute("aria-label",n[`${t}ButtonAriaLabel`]||""),e.className=k[t],He(e,n,`${t}Button`)}const kx=(e,t)=>{const n=hu();n&&(Ge(n,t.closeButtonHtml||""),He(n,t,"closeButton"),vo(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel||""))},Sx=(e,t)=>{const n=je();n&&(Ex(n,t.backdrop),Tx(n,t.position),Px(n,t.grow),He(n,t,"container"))};function Ex(e,t){typeof t=="string"?e.style.background=t:t||V([document.documentElement,document.body],k["no-backdrop"])}function Tx(e,t){t&&(t in k?V(e,k[t]):(Ne('The "position" parameter is not valid, defaulting to "center"'),V(e,k.center)))}function Px(e,t){t&&V(e,k[`grow-${t}`])}var Q={innerParams:new WeakMap,domCache:new WeakMap};const Nx=["input","file","range","select","radio","checkbox","textarea"],Ix=(e,t)=>{const n=U();if(!n)return;const r=Q.innerParams.get(e),o=!r||t.input!==r.input;Nx.forEach(i=>{const s=Ht(n,k[i]);s&&(jx(i,t.inputAttributes),s.className=k[i],o&&Se(s))}),t.input&&(o&&Ax(t),Ox(t))},Ax=e=>{if(!e.input)return;if(!ie[e.input]){Ln(`Unexpected type of input! Expected ${Object.keys(ie).join(" | ")}, got "${e.input}"`);return}const t=qm(e.input);if(!t)return;const n=ie[e.input](t,e);me(t),e.inputAutoFocus&&setTimeout(()=>{Hm(n)})},Lx=e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["id","type","value","style"].includes(n)||e.removeAttribute(n)}},jx=(e,t)=>{const n=U();if(!n)return;const r=fs(n,e);if(r){Lx(r);for(const o in t)r.setAttribute(o,t[o])}},Ox=e=>{if(!e.input)return;const t=qm(e.input);t&&He(t,e,"input")},bu=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},yo=(e,t,n)=>{if(n.inputLabel){const r=document.createElement("label"),o=k["input-label"];r.setAttribute("for",e.id),r.className=o,typeof n.customClass=="object"&&V(r,n.customClass.inputLabel),r.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",r)}},qm=e=>{const t=U();if(t)return Ht(t,k[e]||k.input)},Ri=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:pu(t)||Ne(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},ie={};ie.text=ie.email=ie.password=ie.number=ie.tel=ie.url=ie.search=ie.date=ie["datetime-local"]=ie.time=ie.week=ie.month=(e,t)=>(Ri(e,t.inputValue),yo(e,e,t),bu(e,t),e.type=t.input,e);ie.file=(e,t)=>(yo(e,e,t),bu(e,t),e);ie.range=(e,t)=>{const n=e.querySelector("input"),r=e.querySelector("output");return Ri(n,t.inputValue),n.type=t.input,Ri(r,t.inputValue),yo(n,e,t),e};ie.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");Ge(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return yo(e,e,t),e};ie.radio=e=>(e.textContent="",e);ie.checkbox=(e,t)=>{const n=fs(U(),"checkbox");n.value="1",n.checked=!!t.inputValue;const r=e.querySelector("span");return Ge(r,t.inputPlaceholder||t.inputLabel),n};ie.textarea=(e,t)=>{Ri(e,t.inputValue),bu(e,t),yo(e,e,t);const n=r=>parseInt(window.getComputedStyle(r).marginLeft)+parseInt(window.getComputedStyle(r).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const r=parseInt(window.getComputedStyle(U()).width),o=()=>{if(!document.body.contains(e))return;const i=e.offsetWidth+n(e);i>r?U().style.width=`${i}px`:vn(U(),"width",t.width)};new MutationObserver(o).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const Rx=(e,t)=>{const n=fu();n&&(vu(n),He(n,t,"htmlContainer"),t.html?(xu(t.html,n),me(n,"block")):t.text?(n.textContent=t.text,me(n,"block")):Se(n),Ix(e,t))},zx=(e,t)=>{const n=Vm();n&&(vu(n),vo(n,t.footer,"block"),t.footer&&xu(t.footer,n),He(n,t,"footer"))},_x=(e,t)=>{const n=Q.innerParams.get(e),r=mr();if(!r)return;if(n&&t.icon===n.icon){ud(r,t),ad(r,t);return}if(!t.icon&&!t.iconHtml){Se(r);return}if(t.icon&&Object.keys(Oi).indexOf(t.icon)===-1){Ln(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),Se(r);return}me(r),ud(r,t),ad(r,t),V(r,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Gm)},ad=(e,t)=>{for(const[n,r]of Object.entries(Oi))t.icon!==n&&nt(e,r);V(e,t.icon&&Oi[t.icon]),Dx(e,t),Gm(),He(e,t,"icon")},Gm=()=>{const e=U();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let r=0;r<n.length;r++)n[r].style.backgroundColor=t},Mx=e=>`
  ${e.animation?'<div class="swal2-success-circular-line-left"></div>':""}
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div>
  ${e.animation?'<div class="swal2-success-fix"></div>':""}
  ${e.animation?'<div class="swal2-success-circular-line-right"></div>':""}
`,$x=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,ud=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let n=e.innerHTML,r="";t.iconHtml?r=cd(t.iconHtml):t.icon==="success"?(r=Mx(t),n=n.replace(/ style=".*?"/g,"")):t.icon==="error"?r=$x:t.icon&&(r=cd({question:"?",warning:"!",info:"i"}[t.icon])),n.trim()!==r.trim()&&Ge(e,r)},Dx=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])ld(e,n,"background-color",t.iconColor);ld(e,".swal2-success-ring","border-color",t.iconColor)}},cd=e=>`<div class="${k["icon-content"]}">${e}</div>`,Bx=(e,t)=>{const n=Um();if(n){if(!t.imageUrl){Se(n);return}me(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt||""),vn(n,"width",t.imageWidth),vn(n,"height",t.imageHeight),n.className=k.image,He(n,t,"image")}};let Cu=!1,Qm=0,Xm=0,Ym=0,Zm=0;const Fx=e=>{e.addEventListener("mousedown",zi),document.body.addEventListener("mousemove",_i),e.addEventListener("mouseup",Mi),e.addEventListener("touchstart",zi),document.body.addEventListener("touchmove",_i),e.addEventListener("touchend",Mi)},Ux=e=>{e.removeEventListener("mousedown",zi),document.body.removeEventListener("mousemove",_i),e.removeEventListener("mouseup",Mi),e.removeEventListener("touchstart",zi),document.body.removeEventListener("touchmove",_i),e.removeEventListener("touchend",Mi)},zi=e=>{const t=U();if(e.target===t||mr().contains(e.target)){Cu=!0;const n=Jm(e);Qm=n.clientX,Xm=n.clientY,Ym=parseInt(t.style.insetInlineStart)||0,Zm=parseInt(t.style.insetBlockStart)||0,V(t,"swal2-dragging")}},_i=e=>{const t=U();if(Cu){let{clientX:n,clientY:r}=Jm(e);t.style.insetInlineStart=`${Ym+(n-Qm)}px`,t.style.insetBlockStart=`${Zm+(r-Xm)}px`}},Mi=()=>{const e=U();Cu=!1,nt(e,"swal2-dragging")},Jm=e=>{let t=0,n=0;return e.type.startsWith("mouse")?(t=e.clientX,n=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,n=e.touches[0].clientY),{clientX:t,clientY:n}},Vx=(e,t)=>{const n=je(),r=U();if(!(!n||!r)){if(t.toast){vn(n,"width",t.width),r.style.width="100%";const o=gr();o&&r.insertBefore(o,mr())}else vn(r,"width",t.width);vn(r,"padding",t.padding),t.color&&(r.style.color=t.color),t.background&&(r.style.background=t.background),Se(cs()),Hx(r,t),t.draggable&&!t.toast?(V(r,k.draggable),Fx(r)):(nt(r,k.draggable),Ux(r))}},Hx=(e,t)=>{const n=t.showClass||{};e.className=`${k.popup} ${$e(e)?n.popup:""}`,t.toast?(V([document.documentElement,document.body],k["toast-shown"]),V(e,k.toast)):V(e,k.modal),He(e,t,"popup"),typeof t.customClass=="string"&&V(e,t.customClass),t.icon&&V(e,k[`icon-${t.icon}`])},Wx=(e,t)=>{const n=mu();if(!n)return;const{progressSteps:r,currentProgressStep:o}=t;if(!r||r.length===0||o===void 0){Se(n);return}me(n),n.textContent="",o>=r.length&&Ne("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),r.forEach((i,s)=>{const l=Kx(i);if(n.appendChild(l),s===o&&V(l,k["active-progress-step"]),s!==r.length-1){const a=qx(t);n.appendChild(a)}})},Kx=e=>{const t=document.createElement("li");return V(t,k["progress-step"]),Ge(t,e),t},qx=e=>{const t=document.createElement("li");return V(t,k["progress-step-line"]),e.progressStepsDistance&&vn(t,"width",e.progressStepsDistance),t},Gx=(e,t)=>{const n=Fm();n&&(vu(n),vo(n,t.title||t.titleText,"block"),t.title&&xu(t.title,n),t.titleText&&(n.innerText=t.titleText),He(n,t,"title"))},eh=(e,t)=>{Vx(e,t),Sx(e,t),Wx(e,t),_x(e,t),Bx(e,t),Gx(e,t),kx(e,t),Rx(e,t),xx(e,t),zx(e,t);const n=U();typeof t.didRender=="function"&&n&&t.didRender(n),j.eventEmitter.emit("didRender",n)},Qx=()=>$e(U()),th=()=>{var e;return(e=xt())===null||e===void 0?void 0:e.click()},Xx=()=>{var e;return(e=jn())===null||e===void 0?void 0:e.click()},Yx=()=>{var e;return(e=hr())===null||e===void 0?void 0:e.click()},wr=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),nh=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Zx=(e,t,n)=>{nh(e),t.toast||(e.keydownHandler=r=>e1(t,r,n),e.keydownTarget=t.keydownListenerCapture?window:U(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},oa=(e,t)=>{var n;const r=gu();if(r.length){e=e+t,e===-2&&(e=r.length-1),e===r.length?e=0:e===-1&&(e=r.length-1),r[e].focus();return}(n=U())===null||n===void 0||n.focus()},rh=["ArrowRight","ArrowDown"],Jx=["ArrowLeft","ArrowUp"],e1=(e,t,n)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?t1(t,e):t.key==="Tab"?n1(t):[...rh,...Jx].includes(t.key)?r1(t.key):t.key==="Escape"&&o1(t,e,n)))},t1=(e,t)=>{if(!us(t.allowEnterKey))return;const n=fs(U(),t.input);if(e.target&&n&&e.target instanceof HTMLElement&&e.target.outerHTML===n.outerHTML){if(["textarea","file"].includes(t.input))return;th(),e.preventDefault()}},n1=e=>{const t=e.target,n=gu();let r=-1;for(let o=0;o<n.length;o++)if(t===n[o]){r=o;break}e.shiftKey?oa(r,-1):oa(r,1),e.stopPropagation(),e.preventDefault()},r1=e=>{const t=wo(),n=xt(),r=jn(),o=hr();if(!t||!n||!r||!o)return;const i=[n,r,o];if(document.activeElement instanceof HTMLElement&&!i.includes(document.activeElement))return;const s=rh.includes(e)?"nextElementSibling":"previousElementSibling";let l=document.activeElement;if(l){for(let a=0;a<t.children.length;a++){if(l=l[s],!l)return;if(l instanceof HTMLButtonElement&&$e(l))break}l instanceof HTMLButtonElement&&l.focus()}},o1=(e,t,n)=>{e.preventDefault(),us(t.allowEscapeKey)&&n(wr.esc)};var lr={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const i1=()=>{const e=je();Array.from(document.body.children).forEach(n=>{n.contains(e)||(n.hasAttribute("aria-hidden")&&n.setAttribute("data-previous-aria-hidden",n.getAttribute("aria-hidden")||""),n.setAttribute("aria-hidden","true"))})},oh=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},ih=typeof window<"u"&&!!window.GestureEvent,s1=()=>{if(ih&&!It(document.body,k.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,V(document.body,k.iosfix),l1()}},l1=()=>{const e=je();if(!e)return;let t;e.ontouchstart=n=>{t=a1(n)},e.ontouchmove=n=>{t&&(n.preventDefault(),n.stopPropagation())}},a1=e=>{const t=e.target,n=je(),r=fu();return!n||!r||u1(e)||c1(e)?!1:t===n||!ra(n)&&t instanceof HTMLElement&&!ax(t,r)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(ra(r)&&r.contains(t))},u1=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",c1=e=>e.touches&&e.touches.length>1,d1=()=>{if(It(document.body,k.iosfix)){const e=parseInt(document.body.style.top,10);nt(document.body,k.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},p1=()=>{const e=document.createElement("div");e.className=k["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let Zn=null;const f1=e=>{Zn===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(Zn=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Zn+p1()}px`)},m1=()=>{Zn!==null&&(document.body.style.paddingRight=`${Zn}px`,Zn=null)};function sh(e,t,n,r){ps()?dd(e,r):(Zy(n).then(()=>dd(e,r)),nh(j)),ih?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),wu()&&(m1(),d1(),oh()),h1()}function h1(){nt([document.documentElement,document.body],[k.shown,k["height-auto"],k["no-backdrop"],k["toast-shown"]])}function Wt(e){e=w1(e);const t=lr.swalPromiseResolve.get(this),n=g1(this);this.isAwaitingPromise?e.isDismissed||(xo(this),t(e)):n&&t(e)}const g1=e=>{const t=U();if(!t)return!1;const n=Q.innerParams.get(e);if(!n||It(t,n.hideClass.popup))return!1;nt(t,n.showClass.popup),V(t,n.hideClass.popup);const r=je();return nt(r,n.showClass.backdrop),V(r,n.hideClass.backdrop),v1(e,t,n),!0};function lh(e){const t=lr.swalPromiseReject.get(this);xo(this),t&&t(e)}const xo=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,Q.innerParams.get(e)||e._destroy())},w1=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),v1=(e,t,n)=>{var r;const o=je(),i=Km(t);typeof n.willClose=="function"&&n.willClose(t),(r=j.eventEmitter)===null||r===void 0||r.emit("willClose",t),i?y1(e,t,o,n.returnFocus,n.didClose):sh(e,o,n.returnFocus,n.didClose)},y1=(e,t,n,r,o)=>{j.swalCloseEventFinishedCallback=sh.bind(null,e,n,r,o);const i=function(s){if(s.target===t){var l;(l=j.swalCloseEventFinishedCallback)===null||l===void 0||l.call(j),delete j.swalCloseEventFinishedCallback,t.removeEventListener("animationend",i),t.removeEventListener("transitionend",i)}};t.addEventListener("animationend",i),t.addEventListener("transitionend",i)},dd=(e,t)=>{setTimeout(()=>{var n;typeof t=="function"&&t.bind(e.params)(),(n=j.eventEmitter)===null||n===void 0||n.emit("didClose"),e._destroy&&e._destroy()})},ar=e=>{let t=U();if(t||new R,t=U(),!t)return;const n=gr();ps()?Se(mr()):x1(t,e),me(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},x1=(e,t)=>{const n=wo(),r=gr();!n||!r||(!t&&$e(xt())&&(t=xt()),me(n),t&&(Se(t),r.setAttribute("data-button-to-replace",t.className),n.insertBefore(r,t)),V([e,n],k.loading))},b1=(e,t)=>{t.input==="select"||t.input==="radio"?T1(e,t):["text","email","number","tel","textarea"].some(n=>n===t.input)&&(du(t.inputValue)||pu(t.inputValue))&&(ar(xt()),P1(e,t))},C1=(e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return k1(n);case"radio":return S1(n);case"file":return E1(n);default:return t.inputAutoTrim?n.value.trim():n.value}},k1=e=>e.checked?1:0,S1=e=>e.checked?e.value:null,E1=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,T1=(e,t)=>{const n=U();if(!n)return;const r=o=>{t.input==="select"?N1(n,$i(o),t):t.input==="radio"&&I1(n,$i(o),t)};du(t.inputOptions)||pu(t.inputOptions)?(ar(xt()),ho(t.inputOptions).then(o=>{e.hideLoading(),r(o)})):typeof t.inputOptions=="object"?r(t.inputOptions):Ln(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},P1=(e,t)=>{const n=e.getInput();n&&(Se(n),ho(t.inputValue).then(r=>{n.value=t.input==="number"?`${parseFloat(r)||0}`:`${r}`,me(n),n.focus(),e.hideLoading()}).catch(r=>{Ln(`Error in inputValue promise: ${r}`),n.value="",me(n),n.focus(),e.hideLoading()}))};function N1(e,t,n){const r=Ht(e,k.select);if(!r)return;const o=(i,s,l)=>{const a=document.createElement("option");a.value=l,Ge(a,s),a.selected=ah(l,n.inputValue),i.appendChild(a)};t.forEach(i=>{const s=i[0],l=i[1];if(Array.isArray(l)){const a=document.createElement("optgroup");a.label=s,a.disabled=!1,r.appendChild(a),l.forEach(u=>o(a,u[1],u[0]))}else o(r,l,s)}),r.focus()}function I1(e,t,n){const r=Ht(e,k.radio);if(!r)return;t.forEach(i=>{const s=i[0],l=i[1],a=document.createElement("input"),u=document.createElement("label");a.type="radio",a.name=k.radio,a.value=s,ah(s,n.inputValue)&&(a.checked=!0);const h=document.createElement("span");Ge(h,l),h.className=k.label,u.appendChild(a),u.appendChild(h),r.appendChild(u)});const o=r.querySelectorAll("input");o.length&&o[0].focus()}const $i=e=>{const t=[];return e instanceof Map?e.forEach((n,r)=>{let o=n;typeof o=="object"&&(o=$i(o)),t.push([r,o])}):Object.keys(e).forEach(n=>{let r=e[n];typeof r=="object"&&(r=$i(r)),t.push([n,r])}),t},ah=(e,t)=>!!t&&t.toString()===e.toString(),A1=e=>{const t=Q.innerParams.get(e);e.disableButtons(),t.input?uh(e,"confirm"):Su(e,!0)},L1=e=>{const t=Q.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?uh(e,"deny"):ku(e,!1)},j1=(e,t)=>{e.disableButtons(),t(wr.cancel)},uh=(e,t)=>{const n=Q.innerParams.get(e);if(!n.input){Ln(`The "input" parameter is needed to be set when using returnInputValueOn${cu(t)}`);return}const r=e.getInput(),o=C1(e,n);n.inputValidator?O1(e,o,t):r&&!r.checkValidity()?(e.enableButtons(),e.showValidationMessage(n.validationMessage||r.validationMessage)):t==="deny"?ku(e,o):Su(e,o)},O1=(e,t,n)=>{const r=Q.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>ho(r.inputValidator(t,r.validationMessage))).then(i=>{e.enableButtons(),e.enableInput(),i?e.showValidationMessage(i):n==="deny"?ku(e,t):Su(e,t)})},ku=(e,t)=>{const n=Q.innerParams.get(e||void 0);n.showLoaderOnDeny&&ar(jn()),n.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>ho(n.preDeny(t,n.validationMessage))).then(o=>{o===!1?(e.hideLoading(),xo(e)):e.close({isDenied:!0,value:typeof o>"u"?t:o})}).catch(o=>ch(e||void 0,o))):e.close({isDenied:!0,value:t})},pd=(e,t)=>{e.close({isConfirmed:!0,value:t})},ch=(e,t)=>{e.rejectPromise(t)},Su=(e,t)=>{const n=Q.innerParams.get(e||void 0);n.showLoaderOnConfirm&&ar(),n.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>ho(n.preConfirm(t,n.validationMessage))).then(o=>{$e(cs())||o===!1?(e.hideLoading(),xo(e)):pd(e,typeof o>"u"?t:o)}).catch(o=>ch(e||void 0,o))):pd(e,t)};function Di(){const e=Q.innerParams.get(this);if(!e)return;const t=Q.domCache.get(this);Se(t.loader),ps()?e.icon&&me(mr()):R1(t),nt([t.popup,t.actions],k.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const R1=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?me(t[0],"inline-block"):lx()&&Se(e.actions)};function dh(){const e=Q.innerParams.get(this),t=Q.domCache.get(this);return t?fs(t.popup,e.input):null}function ph(e,t,n){const r=Q.domCache.get(e);t.forEach(o=>{r[o].disabled=n})}function fh(e,t){const n=U();if(!(!n||!e))if(e.type==="radio"){const r=n.querySelectorAll(`[name="${k.radio}"]`);for(let o=0;o<r.length;o++)r[o].disabled=t}else e.disabled=t}function mh(){ph(this,["confirmButton","denyButton","cancelButton"],!1)}function hh(){ph(this,["confirmButton","denyButton","cancelButton"],!0)}function gh(){fh(this.getInput(),!1)}function wh(){fh(this.getInput(),!0)}function vh(e){const t=Q.domCache.get(this),n=Q.innerParams.get(this);Ge(t.validationMessage,e),t.validationMessage.className=k["validation-message"],n.customClass&&n.customClass.validationMessage&&V(t.validationMessage,n.customClass.validationMessage),me(t.validationMessage);const r=this.getInput();r&&(r.setAttribute("aria-invalid","true"),r.setAttribute("aria-describedby",k["validation-message"]),Hm(r),V(r,k.inputerror))}function yh(){const e=Q.domCache.get(this);e.validationMessage&&Se(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),nt(t,k.inputerror))}const Jn={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},z1=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],_1={allowEnterKey:void 0},M1=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],xh=e=>Object.prototype.hasOwnProperty.call(Jn,e),bh=e=>z1.indexOf(e)!==-1,Ch=e=>_1[e],$1=e=>{xh(e)||Ne(`Unknown parameter "${e}"`)},D1=e=>{M1.includes(e)&&Ne(`The parameter "${e}" is incompatible with toasts`)},B1=e=>{const t=Ch(e);t&&Bm(e,t)},kh=e=>{e.backdrop===!1&&e.allowOutsideClick&&Ne('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&Ne(`Invalid theme "${e.theme}"`);for(const t in e)$1(t),e.toast&&D1(t),B1(t)};function Sh(e){const t=je(),n=U(),r=Q.innerParams.get(this);if(!n||It(n,r.hideClass.popup)){Ne("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const o=F1(e),i=Object.assign({},r,o);kh(i),t.dataset.swal2Theme=i.theme,eh(this,i),Q.innerParams.set(this,i),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const F1=e=>{const t={};return Object.keys(e).forEach(n=>{bh(n)?t[n]=e[n]:Ne(`Invalid parameter to update: ${n}`)}),t};function Eh(){const e=Q.domCache.get(this),t=Q.innerParams.get(this);if(!t){Th(this);return}e.popup&&j.swalCloseEventFinishedCallback&&(j.swalCloseEventFinishedCallback(),delete j.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),j.eventEmitter.emit("didDestroy"),U1(this)}const U1=e=>{Th(e),delete e.params,delete j.keydownHandler,delete j.keydownTarget,delete j.currentInstance},Th=e=>{e.isAwaitingPromise?(Gs(Q,e),e.isAwaitingPromise=!0):(Gs(lr,e),Gs(Q,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},Gs=(e,t)=>{for(const n in e)e[n].delete(t)};var V1=Object.freeze({__proto__:null,_destroy:Eh,close:Wt,closeModal:Wt,closePopup:Wt,closeToast:Wt,disableButtons:hh,disableInput:wh,disableLoading:Di,enableButtons:mh,enableInput:gh,getInput:dh,handleAwaitingPromise:xo,hideLoading:Di,rejectPromise:lh,resetValidationMessage:yh,showValidationMessage:vh,update:Sh});const H1=(e,t,n)=>{e.toast?W1(e,t,n):(q1(t),G1(t),Q1(e,t,n))},W1=(e,t,n)=>{t.popup.onclick=()=>{e&&(K1(e)||e.timer||e.input)||n(wr.close)}},K1=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let Bi=!1;const q1=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(Bi=!0)}}},G1=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(n){e.popup.onmouseup=()=>{},(n.target===e.popup||n.target instanceof HTMLElement&&e.popup.contains(n.target))&&(Bi=!0)}}},Q1=(e,t,n)=>{t.container.onclick=r=>{if(Bi){Bi=!1;return}r.target===t.container&&us(e.allowOutsideClick)&&n(wr.backdrop)}},X1=e=>typeof e=="object"&&e.jquery,fd=e=>e instanceof Element||X1(e),Y1=e=>{const t={};return typeof e[0]=="object"&&!fd(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((n,r)=>{const o=e[r];typeof o=="string"||fd(o)?t[n]=o:o!==void 0&&Ln(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof o}`)}),t};function Z1(...e){return new this(...e)}function J1(e){class t extends this{_main(r,o){return super._main(r,Object.assign({},e,o))}}return t}const e2=()=>j.timeout&&j.timeout.getTimerLeft(),Ph=()=>{if(j.timeout)return ux(),j.timeout.stop()},Nh=()=>{if(j.timeout){const e=j.timeout.start();return yu(e),e}},t2=()=>{const e=j.timeout;return e&&(e.running?Ph():Nh())},n2=e=>{if(j.timeout){const t=j.timeout.increase(e);return yu(t,!0),t}},r2=()=>!!(j.timeout&&j.timeout.isRunning());let md=!1;const ia={};function o2(e="data-swal-template"){ia[e]=this,md||(document.body.addEventListener("click",i2),md=!0)}const i2=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const n in ia){const r=t.getAttribute(n);if(r){ia[n].fire({template:r});return}}};class s2{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,n){const r=this._getHandlersByEventName(t);r.includes(n)||r.push(n)}once(t,n){const r=(...o)=>{this.removeListener(t,r),n.apply(this,o)};this.on(t,r)}emit(t,...n){this._getHandlersByEventName(t).forEach(r=>{try{r.apply(this,n)}catch(o){console.error(o)}})}removeListener(t,n){const r=this._getHandlersByEventName(t),o=r.indexOf(n);o>-1&&r.splice(o,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}j.eventEmitter=new s2;const l2=(e,t)=>{j.eventEmitter.on(e,t)},a2=(e,t)=>{j.eventEmitter.once(e,t)},u2=(e,t)=>{if(!e){j.eventEmitter.reset();return}t?j.eventEmitter.removeListener(e,t):j.eventEmitter.removeAllListeners(e)};var c2=Object.freeze({__proto__:null,argsToParams:Y1,bindClickHandler:o2,clickCancel:Yx,clickConfirm:th,clickDeny:Xx,enableLoading:ar,fire:Z1,getActions:wo,getCancelButton:hr,getCloseButton:hu,getConfirmButton:xt,getContainer:je,getDenyButton:jn,getFocusableElements:gu,getFooter:Vm,getHtmlContainer:fu,getIcon:mr,getIconContent:nx,getImage:Um,getInputLabel:rx,getLoader:gr,getPopup:U,getProgressSteps:mu,getTimerLeft:e2,getTimerProgressBar:ds,getTitle:Fm,getValidationMessage:cs,increaseTimer:n2,isDeprecatedParameter:Ch,isLoading:ix,isTimerRunning:r2,isUpdatableParameter:bh,isValidParameter:xh,isVisible:Qx,mixin:J1,off:u2,on:l2,once:a2,resumeTimer:Nh,showLoading:ar,stopTimer:Ph,toggleTimer:t2});class d2{constructor(t,n){this.callback=t,this.remaining=n,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const n=this.running;return n&&this.stop(),this.remaining+=t,n&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Ih=["swal-title","swal-html","swal-footer"],p2=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return x2(n),Object.assign(f2(n),m2(n),h2(n),g2(n),w2(n),v2(n),y2(n,Ih))},f2=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(r=>{Tn(r,["name","value"]);const o=r.getAttribute("name"),i=r.getAttribute("value");!o||!i||(typeof Jn[o]=="boolean"?t[o]=i!=="false":typeof Jn[o]=="object"?t[o]=JSON.parse(i):t[o]=i)}),t},m2=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(r=>{const o=r.getAttribute("name"),i=r.getAttribute("value");!o||!i||(t[o]=new Function(`return ${i}`)())}),t},h2=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(r=>{Tn(r,["type","color","aria-label"]);const o=r.getAttribute("type");!o||!["confirm","cancel","deny"].includes(o)||(t[`${o}ButtonText`]=r.innerHTML,t[`show${cu(o)}Button`]=!0,r.hasAttribute("color")&&(t[`${o}ButtonColor`]=r.getAttribute("color")),r.hasAttribute("aria-label")&&(t[`${o}ButtonAriaLabel`]=r.getAttribute("aria-label")))}),t},g2=e=>{const t={},n=e.querySelector("swal-image");return n&&(Tn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")||void 0),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")||void 0),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")||void 0),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt")||void 0)),t},w2=e=>{const t={},n=e.querySelector("swal-icon");return n&&(Tn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},v2=e=>{const t={},n=e.querySelector("swal-input");n&&(Tn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const r=Array.from(e.querySelectorAll("swal-input-option"));return r.length&&(t.inputOptions={},r.forEach(o=>{Tn(o,["value"]);const i=o.getAttribute("value");if(!i)return;const s=o.innerHTML;t.inputOptions[i]=s})),t},y2=(e,t)=>{const n={};for(const r in t){const o=t[r],i=e.querySelector(o);i&&(Tn(i,[]),n[o.replace(/^swal-/,"")]=i.innerHTML.trim())}return n},x2=e=>{const t=Ih.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(n=>{const r=n.tagName.toLowerCase();t.includes(r)||Ne(`Unrecognized element <${r}>`)})},Tn=(e,t)=>{Array.from(e.attributes).forEach(n=>{t.indexOf(n.name)===-1&&Ne([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},Ah=10,b2=e=>{const t=je(),n=U();typeof e.willOpen=="function"&&e.willOpen(n),j.eventEmitter.emit("willOpen",n);const o=window.getComputedStyle(document.body).overflowY;S2(t,n,e),setTimeout(()=>{C2(t,n)},Ah),wu()&&(k2(t,e.scrollbarPadding,o),i1()),!ps()&&!j.previousActiveElement&&(j.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(n)),j.eventEmitter.emit("didOpen",n),nt(t,k["no-transition"])},Fi=e=>{const t=U();if(e.target!==t)return;const n=je();t.removeEventListener("animationend",Fi),t.removeEventListener("transitionend",Fi),n.style.overflowY="auto"},C2=(e,t)=>{Km(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",Fi),t.addEventListener("transitionend",Fi)):e.style.overflowY="auto"},k2=(e,t,n)=>{s1(),t&&n!=="hidden"&&f1(n),setTimeout(()=>{e.scrollTop=0})},S2=(e,t,n)=>{V(e,n.showClass.backdrop),n.animation?(t.style.setProperty("opacity","0","important"),me(t,"grid"),setTimeout(()=>{V(t,n.showClass.popup),t.style.removeProperty("opacity")},Ah)):me(t,"grid"),V([document.documentElement,document.body],k.shown),n.heightAuto&&n.backdrop&&!n.toast&&V([document.documentElement,document.body],k["height-auto"])};var hd={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function E2(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=hd.email),e.input==="url"&&(e.inputValidator=hd.url))}function T2(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(Ne('Target parameter is not valid, defaulting to "body"'),e.target="body")}function P2(e){E2(e),e.showLoaderOnConfirm&&!e.preConfirm&&Ne(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),T2(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),wx(e)}let gt;var Ho=new WeakMap;class le{constructor(...t){if(Gy(this,Ho,void 0),typeof window>"u")return;gt=this;const n=Object.freeze(this.constructor.argsToParams(t));this.params=n,this.isAwaitingPromise=!1,Qy(Ho,this,this._main(gt.params))}_main(t,n={}){if(kh(Object.assign({},n,t)),j.currentInstance){const i=lr.swalPromiseResolve.get(j.currentInstance),{isAwaitingPromise:s}=j.currentInstance;j.currentInstance._destroy(),s||i({isDismissed:!0}),wu()&&oh()}j.currentInstance=gt;const r=I2(t,n);P2(r),Object.freeze(r),j.timeout&&(j.timeout.stop(),delete j.timeout),clearTimeout(j.restoreFocusTimeout);const o=A2(gt);return eh(gt,r),Q.innerParams.set(gt,r),N2(gt,o,r)}then(t){return id(Ho,this).then(t)}finally(t){return id(Ho,this).finally(t)}}const N2=(e,t,n)=>new Promise((r,o)=>{const i=s=>{e.close({isDismissed:!0,dismiss:s})};lr.swalPromiseResolve.set(e,r),lr.swalPromiseReject.set(e,o),t.confirmButton.onclick=()=>{A1(e)},t.denyButton.onclick=()=>{L1(e)},t.cancelButton.onclick=()=>{j1(e,i)},t.closeButton.onclick=()=>{i(wr.close)},H1(n,t,i),Zx(j,n,i),b1(e,n),b2(n),L2(j,n,i),j2(t,n),setTimeout(()=>{t.container.scrollTop=0})}),I2=(e,t)=>{const n=p2(e),r=Object.assign({},Jn,t,n,e);return r.showClass=Object.assign({},Jn.showClass,r.showClass),r.hideClass=Object.assign({},Jn.hideClass,r.hideClass),r.animation===!1&&(r.showClass={backdrop:"swal2-noanimation"},r.hideClass={}),r},A2=e=>{const t={popup:U(),container:je(),actions:wo(),confirmButton:xt(),denyButton:jn(),cancelButton:hr(),loader:gr(),closeButton:hu(),validationMessage:cs(),progressSteps:mu()};return Q.domCache.set(e,t),t},L2=(e,t,n)=>{const r=ds();Se(r),t.timer&&(e.timeout=new d2(()=>{n("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(me(r),He(r,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&yu(t.timer)})))},j2=(e,t)=>{if(!t.toast){if(!us(t.allowEnterKey)){Bm("allowEnterKey"),z2();return}O2(e)||R2(e,t)||oa(-1,1)}},O2=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const n of t)if(n instanceof HTMLElement&&$e(n))return n.focus(),!0;return!1},R2=(e,t)=>t.focusDeny&&$e(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&$e(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&$e(e.confirmButton)?(e.confirmButton.focus(),!0):!1,z2=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const n=document.createElement("audio");n.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",n.loop=!0,document.body.appendChild(n),setTimeout(()=>{n.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}le.prototype.disableButtons=hh;le.prototype.enableButtons=mh;le.prototype.getInput=dh;le.prototype.disableInput=wh;le.prototype.enableInput=gh;le.prototype.hideLoading=Di;le.prototype.disableLoading=Di;le.prototype.showValidationMessage=vh;le.prototype.resetValidationMessage=yh;le.prototype.close=Wt;le.prototype.closePopup=Wt;le.prototype.closeModal=Wt;le.prototype.closeToast=Wt;le.prototype.rejectPromise=lh;le.prototype.update=Sh;le.prototype._destroy=Eh;Object.assign(le,c2);Object.keys(V1).forEach(e=>{le[e]=function(...t){return gt&&gt[e]?gt[e](...t):null}});le.DismissReason=wr;le.version="11.22.2";const R=le;R.default=R;typeof document<"u"&&function(e,t){var n=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch{n.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');const Pn={background:"#ffffff",color:"#333333",customClass:{popup:"swal-clean-theme",title:"swal-clean-title",htmlContainer:"swal-clean-content",confirmButton:"swal-clean-button",cancelButton:"swal-clean-cancel-button"},buttonsStyling:!1},_2=(e="⏳ Aguarde",t="Processando...")=>R.fire({...Pn,title:e,html:`
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="font-size: 16px; font-weight: 500;">${t}</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        Aguarde enquanto processamos sua solicitação
      </div>
      <div style="margin-top: 15px;">
        <div class="loading-spinner"></div>
      </div>
    `,allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{R.showLoading()}}),M2=(e="✅ Sucesso!",t="Sua solicitação foi processada com sucesso!")=>R.fire({...Pn,title:e,html:`
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">🎉</span>
          <span style="font-size: 16px; font-weight: 500;">Operação Realizada!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Tudo funcionou perfeitamente</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${t}
      </div>
    `,confirmButtonText:"Continuar",confirmButtonColor:"#10b981"}),gd=(e="❌ Erro!",t="Ocorreu um erro inesperado. Tente novamente.")=>R.fire({...Pn,title:e,html:`
      <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">⚠️</span>
          <span style="font-size: 16px; font-weight: 500;">Ops!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Algo deu errado</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${t}
      </div>
    `,confirmButtonText:"Tentar Novamente",confirmButtonColor:"#ef4444"}),$2=e=>R.fire({...Pn,title:"🎯 Teste Gerado com Sucesso!",html:`
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">✅</span>
          <span style="font-size: 16px; font-weight: 500;">Teste Liberado!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Seus dados de acesso estão prontos</div>
      </div>

      <div style="
        background: #f8fafc;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        text-align: left;
        max-height: 400px;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        border: 1px solid #e5e7eb;
      " class="test-result-content">
        <div style="
          font-size: 13px;
          color: #374151;
          white-space: pre-line;
          line-height: 1.6;
          font-family: 'Courier New', monospace;
        ">
          ${e||"Dados do teste serão exibidos aqui..."}
        </div>
      </div>

      <div style="color: #666; font-size: 12px; margin-top: 10px;">
        💡 Salve essas informações para acessar seus canais
      </div>
    `,confirmButtonText:"📋 Copiar Dados",showCancelButton:!0,cancelButtonText:"✅ Entendi",confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",width:"700px",customClass:{...Pn.customClass,popup:"swal-clean-theme test-result-modal"},didOpen:()=>{const t=document.createElement("style");t.textContent=`
        .test-result-content::-webkit-scrollbar {
          display: none !important;
        }
        .test-result-modal .test-result-content {
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }
      `,document.head.appendChild(t)}}),D2=e=>{const{customerName:t,planType:n,amount:r,paymentId:o,paidAt:i}=e,s=new Date(i).toLocaleString("pt-BR"),l=`R$ ${r.toFixed(2).replace(".",",")}`;return R.fire({...Pn,title:"🎉 Pagamento Confirmado!",html:`
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">✅</span>
          <span style="font-size: 16px; font-weight: 500;">Pagamento Aprovado!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Seu acesso foi liberado com sucesso</div>
      </div>

      <div style="background: #f8fafc; border-radius: 10px; padding: 15px; margin: 15px 0; text-align: left;">
        <div style="font-size: 14px; color: #374151;">
          <div style="margin-bottom: 8px;"><strong>👤 Cliente:</strong> ${t}</div>
          <div style="margin-bottom: 8px;"><strong>📺 Plano:</strong> ${n}</div>
          <div style="margin-bottom: 8px;"><strong>💰 Valor:</strong> ${l}</div>
          <div style="margin-bottom: 8px;"><strong>📅 Data:</strong> ${s}</div>
          <div style="margin-bottom: 8px;"><strong>🔢 ID:</strong> ${o}</div>
        </div>
      </div>

      <div style="color: #666; font-size: 12px; margin-top: 10px;">
        🎯 Seu acesso aos canais foi liberado automaticamente!
      </div>
    `,confirmButtonText:"🚀 Acessar Canais",confirmButtonColor:"#10b981",width:"500px",customClass:{...Pn.customClass,popup:"swal-clean-theme payment-confirmed-modal"}})};const B2=({phoneNumber:e="5541995056052",message:t="Olá! Gostaria de saber mais sobre os planos SmartV."})=>{const n=()=>{const r=encodeURIComponent(t),o=`https://wa.me/${e}?text=${r}`;window.open(o,"_blank")};return f.jsx("div",{className:"fixed bottom-4 sm:bottom-6 right-4 sm:right-6 z-50",children:f.jsxs("div",{className:"relative group",children:[f.jsxs("div",{className:"absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap hidden sm:block",children:["Fale conosco no WhatsApp",f.jsx("div",{className:"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"})]}),f.jsxs("button",{onClick:n,className:"w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-[#4dc247] hover:bg-[#45b83d] rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center group touch-manipulation","aria-label":"Falar no WhatsApp",children:[f.jsx("svg",{viewBox:"0 0 32 32",xmlns:"http://www.w3.org/2000/svg",className:"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 fill-white flex-shrink-0",children:f.jsx("path",{d:"M19.11 17.205c-.372 0-1.088 1.39-1.518 1.39a.63.63 0 0 1-.315-.1c-.802-.402-1.504-.817-2.163-1.447-.545-.516-1.146-1.29-1.46-1.963a.426.426 0 0 1-.073-.215c0-.33.99-.945.99-1.49 0-.143-.73-2.09-.832-2.335-.143-.372-.214-.487-.6-.487-.187 0-.36-.043-.53-.043-.302 0-.53.115-.746.315-.688.645-1.032 1.318-1.06 2.264v.114c-.015.99.472 1.977 1.017 2.78 1.23 1.82 2.506 3.41 4.554 4.34.616.287 2.035.888 2.722.888.817 0 2.15-.515 2.478-1.318.13-.33.244-.73.244-1.088 0-.058 0-.144-.03-.215-.1-.172-2.434-1.39-2.678-1.39zm-2.908 7.593c-1.747 0-3.48-.53-4.942-1.49L7.793 24.41l1.132-3.337a8.955 8.955 0 0 1-1.72-5.272c0-4.955 4.04-8.995 8.997-8.995S25.2 10.845 25.2 15.8c0 4.958-4.04 8.998-8.998 8.998zm0-19.798c-5.96 0-10.8 4.842-10.8 10.8 0 1.964.53 3.898 1.546 5.574L5 27.176l5.974-1.92a10.807 10.807 0 0 0 16.03-9.455c0-5.958-4.842-10.8-10.802-10.8z"})}),f.jsx("div",{className:"absolute inset-0 rounded-full bg-[#4dc247] animate-ping opacity-20"})]})]})})},Qs=[{id:"basico",name:"Básico",price:19.9,description:"Canais essenciais para toda família",features:["Mais de 100 canais","Canais de notícias","Canais de entretenimento","Qualidade HD","Suporte 24h"],popular:!1,icon:ii},{id:"criancas",name:"Plano Infantil",price:29.9,description:"Diversão garantida para os pequenos",features:["Todos os canais do Básico","Canais infantis premium","Desenhos animados","Conteúdo educativo","Controle parental","Sem propagandas"],popular:!0,icon:J0},{id:"familiar-sport",name:"Familiar + Sport",price:34.9,description:"Entretenimento completo + esportes",features:["Todos os canais anteriores","Canais de esportes premium","Jogos ao vivo","Filmes e séries","Documentários","Múltiplas telas"],popular:!1,icon:ty}];function F2(){Bf();const[e,t]=b.useState(!1),[n,r]=b.useState({name:"",email:"",plan:""}),o=g=>{const{icon:w,...y}=g;R.fire({title:"🎉 Ótima Escolha!",html:`
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 18px;">📺 ${y.name}</h3>
            <p style="margin: 0; font-size: 24px; font-weight: bold;">R$ ${y.price.toFixed(2).replace(".",",")}</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 16px;">
            Preencha seus dados abaixo para gerar o PIX:
          </p>

          <form id="quickPaymentForm" style="text-align: left; margin-top: 20px;">
            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
              <input
                type="text"
                id="modalCustomerName"
                placeholder="Seu nome completo"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
              <input
                type="email"
                id="modalCustomerEmail"
                placeholder="<EMAIL>"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">CPF:</label>
              <input
                type="text"
                id="modalCustomerDocument"
                placeholder="000.000.000-00"
                maxlength="14"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">WhatsApp:</label>
              <input
                type="text"
                id="modalCustomerPhone"
                placeholder="(11) 99999-9999"
                maxlength="15"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>
          </form>

          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #495057; font-size: 13px;">
              💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
            </p>
          </div>
        </div>
      `,showCancelButton:!0,confirmButtonText:"🚀 Gerar PIX",cancelButtonText:"📝 Usar Exemplo",confirmButtonColor:"#667eea",cancelButtonColor:"#28a745",width:"min(95vw, 550px)",background:"#ffffff",color:"#333333",allowOutsideClick:!1,heightAuto:!1,customClass:{popup:"pix-modal"},didOpen:()=>{const x=document.getElementById("modalCustomerDocument"),c=document.getElementById("modalCustomerPhone");x&&x.addEventListener("input",d=>{const m=d.target;let C=m.value.replace(/\\D/g,"");C=C.replace(/(\\d{3})(\\d)/,"$1.$2"),C=C.replace(/(\\d{3})(\\d)/,"$1.$2"),C=C.replace(/(\\d{3})(\\d{1,2})$/,"$1-$2"),m.value=C}),c&&c.addEventListener("input",d=>{const m=d.target;let C=m.value.replace(/\\D/g,"");C=C.replace(/^(\\d{2})(\\d)/g,"($1) $2"),C=C.replace(/(\\d{5})(\\d)/,"$1-$2"),m.value=C}),setTimeout(()=>{const d=document.getElementById("modalCustomerName");d&&d.focus()},100)},preConfirm:()=>{var C,S,T,E;const x=(C=document.getElementById("modalCustomerName"))==null?void 0:C.value.trim(),c=(S=document.getElementById("modalCustomerEmail"))==null?void 0:S.value.trim(),d=(T=document.getElementById("modalCustomerDocument"))==null?void 0:T.value.trim(),m=(E=document.getElementById("modalCustomerPhone"))==null?void 0:E.value.trim();return!x||!c||!d||!m?(R.showValidationMessage("Por favor, preencha todos os campos"),!1):x.length<3?(R.showValidationMessage("Nome deve ter pelo menos 3 caracteres"),!1):c.includes("@")?d.replace(/\\D/g,"").length!==11?(R.showValidationMessage("CPF deve ter 11 dígitos"),!1):m.replace(/\\D/g,"").length<10?(R.showValidationMessage("Telefone inválido"),!1):{name:x,email:c,document:d,phone:m}:(R.showValidationMessage("E-mail inválido"),!1)}}).then(async x=>{if(x.isDismissed&&x.dismiss===R.DismissReason.cancel)await i({customerName:"Paulo Antonio Silva",customerEmail:"<EMAIL>",customerDocument:"123.456.789-00",customerPhone:"(11) 99999-9999"},y);else if(x.isConfirmed&&x.value){const{name:c,email:d,document:m,phone:C}=x.value;await i({customerName:c,customerEmail:d,customerDocument:m,customerPhone:C},y)}})},i=async(g,w)=>{try{R.fire({title:"Gerando PIX...",text:"Aguarde enquanto processamos seu pagamento",allowOutsideClick:!1,didOpen:()=>{R.showLoading()}});const y={customerName:g.customerName,customerEmail:g.customerEmail,customerDocument:g.customerDocument.replace(/\D/g,""),customerPhone:g.customerPhone.replace(/\D/g,""),planType:w.name,amount:w.price},x=await mn.payment.createPix(y);console.log("PIX Response:",x),console.log("QR Code URL:",x.qrCodeUrl),await s(x,w)}catch(y){console.error("Error creating PIX:",y),R.fire({title:"Erro ao gerar PIX",text:"Tente novamente em alguns instantes.",icon:"error",confirmButtonText:"OK",confirmButtonColor:"#667eea"})}},s=async(g,w)=>{let y="";return g.qrCodeUrl&&(g.qrCodeUrl.startsWith("data:image/")?y=g.qrCodeUrl:g.qrCodeUrl.length>100&&!g.qrCodeUrl.startsWith("http")?y=`data:image/png;base64,${g.qrCodeUrl}`:g.qrCodeUrl.startsWith("http")&&(y=g.qrCodeUrl)),R.fire({title:"💳 Pagamento PIX",html:`
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 16px;">💰 Valor: R$ ${w.price.toFixed(2).replace(".",",")}</h3>
            <p style="margin: 0; font-size: 14px;">${w.name}</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 14px;">
            Escaneie o QR Code ou copie o código PIX
          </p>

          <div style="background: white; padding: 20px; border-radius: 12px; margin: 20px 0; border: 2px solid #e0e0e0; display: flex; justify-content: center; align-items: center; min-height: 200px;">
            ${y?`<img src="${y}" alt="QR Code PIX" style="max-width: 200px; max-height: 200px; width: auto; height: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
               <div style="display: none; padding: 40px; color: #666; text-align: center;">
                 <p style="margin: 0; font-size: 16px;">📱 QR Code não disponível</p>
                 <p style="margin: 8px 0 0 0; font-size: 12px;">Use o código PIX abaixo</p>
               </div>`:`<div style="padding: 40px; color: #666; text-align: center;">
                <p style="margin: 0; font-size: 16px;">📱 QR Code PIX</p>
                <p style="margin: 8px 0 0 0; font-size: 12px;">Use o código abaixo para pagar</p>
              </div>`}
          </div>

          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0; word-break: break-all; font-family: monospace; font-size: 12px; color: #495057; max-height: 100px; overflow-y: auto;">
            ${g.pixKey}
          </div>

          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin: 16px 0;">
            <p style="margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">Como pagar:</p>
            <p style="margin: 0; font-size: 13px;">Abra o app do seu banco</p>
            <p style="margin: 0; font-size: 13px;">Escolha a opção PIX</p>
          </div>
        </div>
      `,showCancelButton:!0,confirmButtonText:"📋 Copiar Código",cancelButtonText:"✅ Fechar",confirmButtonColor:"#667eea",cancelButtonColor:"#28a745",width:"min(95vw, 500px)",background:"#ffffff",color:"#333333",allowOutsideClick:!1,customClass:{popup:"swal-pix-popup"},preConfirm:()=>{if(navigator.clipboard)navigator.clipboard.writeText(g.pixKey).then(()=>{R.fire({title:"Código copiado!",text:"O código PIX foi copiado para sua área de transferência",icon:"success",timer:2e3,showConfirmButton:!1})});else{const x=document.createElement("textarea");x.value=g.pixKey,document.body.appendChild(x),x.select(),document.execCommand("copy"),document.body.removeChild(x),R.fire({title:"Código copiado!",text:"O código PIX foi copiado para sua área de transferência",icon:"success",timer:2e3,showConfirmButton:!1})}return!1}})},l=async g=>{try{R.fire({title:"Gerando Teste...",text:"Aguarde enquanto processamos sua solicitação",allowOutsideClick:!1,didOpen:()=>{R.showLoading()}}),await new Promise(x=>setTimeout(x,3e3));const w={name:g.name,email:g.email,plan:g.planInterest},y=await mn.test.requestTest(w);await a(y)}catch(w){console.error("Error generating test:",w),R.fire({title:"Erro ao gerar teste",text:"Tente novamente em alguns instantes.",icon:"error",confirmButtonText:"OK",confirmButtonColor:"#667eea"})}},a=async g=>{console.log("Test Response:",g);let w="";const y=g.success!==void 0,x=g.username&&g.password&&g.dns;if(y&&g.success)if(g.rawResponse)w=g.rawResponse;else{if(w+=`🎯 TESTE LIBERADO COM SUCESSO!

`,g.credentials&&(w+=`👤 CREDENCIAIS DE ACESSO:
`,w+=`📧 Usuário: ${g.credentials.username}
`,w+=`🔐 Senha: ${g.credentials.password}

`),g.accessDetails){const c=g.accessDetails;c.code&&(w+=`🔑 CODE: ${c.code}

`),c.dnsStb&&(w+=`📡 DNS STB:
`,Array.isArray(c.dnsStb)?c.dnsStb.forEach(d=>w+=`${d}
`):w+=`${c.dnsStb}
`,w+=`
`),c.urlXciptv&&c.urlXciptv.length>0&&(w+=`📺 XCIPTV URLs:
`,c.urlXciptv.forEach(d=>{w+=`${d}
`}),w+=`
`),c.linkM3u&&(w+=`🔗 LINK M3U:
${c.linkM3u}

`),c.linkM3uShort&&(w+=`🔗 LINK M3U (Short):
${c.linkM3uShort}

`),c.linkHls&&(w+=`🎬 LINK HLS:
${c.linkHls}

`),c.linkHlsShort&&(w+=`🎬 LINK HLS (Short):
${c.linkHlsShort}

`),c.linkSsiptv&&(w+=`📱 LINK SSIPTV:
${c.linkSsiptv}

`),c.webPlayers&&c.webPlayers.length>0&&(w+=`🌐 WEB PLAYERS:
`,c.webPlayers.forEach(d=>{w+=`${d}
`}),w+=`
`),c.iptvStream&&(w+=`📺 IPTV STREAM:
${c.iptvStream}

`),c.expiresAt&&(w+=`⏰ Expira em: ${c.expiresAt}
`),c.connections&&(w+=`📱 Conexões: ${c.connections}
`),c.planName&&(w+=`📋 Plano: ${c.planName}
`)}if(!g.accessDetails&&g.rawResponse)try{const c=JSON.parse(g.rawResponse);c.urls&&(w+=`
📺 LINKS DE ACESSO:
`,c.urls.m3u&&(w+=`🔗 LINK M3U:
${c.urls.m3u}

`),c.urls.hls&&(w+=`🎬 LINK HLS:
${c.urls.hls}

`),c.urls.short_m3u&&(w+=`📱 LINK M3U (Short):
${c.urls.short_m3u}

`),c.urls.short_hls&&(w+=`📲 LINK HLS (Short):
${c.urls.short_hls}

`),c.urls.ssiptv&&(w+=`📺 LINK SSIPTV:
${c.urls.ssiptv}

`)),c.expirationDate&&(w+=`⏰ Expira em: ${c.expirationDate}
`,w+=`📅 Criado em: ${new Date().toLocaleString("pt-BR")}
`,w+=`📱 Conexões: 1
`),c.server&&(w+=`
🖥️ SERVIDOR:
`,w+=`🌐 Host: ${c.server.host}
`,w+=`🔌 Porta: ${c.server.port}
`)}catch(c){console.error("Error parsing rawResponse:",c)}}else if(x){const c=g.username||"N/A",d=g.password||"N/A",m=g.dns||"N/A",C=g.package||"IPTV TESTE",S=g.expiresAtFormatted||"N/A",T=g.createdAtFormatted||"N/A",E=g.connections||1;g.reply?w=g.reply:w=`🎬 *TESTE IPTV GERADO COM SUCESSO* 🎬

✅ *Usuário*: ${c}
✅ *Senha*: ${d}
📦 *Plano*: ${C}
🗓️ *Criado em*: ${T}
🗓️ *Vencimento*: ${S}
📶 *Conexões*: ${E}

📺 *DNS PRINCIPAL*: ${m}

🟢 *Link M3U*: ${m}/get.php?username=${c}&password=${d}&type=m3u_plus&output=mpegts

🟡 *Link HLS*: ${m}/get.php?username=${c}&password=${d}&type=m3u_plus&output=hls

🔴 *Link SSIPTV*: ${m.includes("bitbig.click")?`http://e.bitbig.click/p/${c}/${d}/ssiptv`:`${m}/p/${c}/${d}/ssiptv`}

📱 *APLICATIVOS RECOMENDADOS*:
• IPTV Smarters Pro
• TiviMate
• Perfect Player
• GSE Smart IPTV

💡 *INSTRUÇÕES*:
1. Baixe um dos aplicativos recomendados
2. Configure usando os dados acima
3. Use o DNS principal para configuração
4. Teste válido por 4 horas

${g.payUrl?`💳 *Assinar Plano Completo*: ${g.payUrl}`:""}`}else w=`❌ Erro ao gerar teste:
${g.message||"Erro desconhecido"}`;return R.fire({title:"🎯 Teste Gerado com Sucesso!",html:`
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
              <span style="font-size: 18px;">✅</span>
              <span style="font-size: 16px; font-weight: 500;">Teste Liberado!</span>
            </div>
            <div style="font-size: 14px; margin-top: 5px;">Seus dados de acesso estão prontos</div>
          </div>

          <div style="
            background: #f8fafc;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
          " class="test-result-content">
            <pre style="font-family: monospace; font-size: 12px; color: #374151; white-space: pre-wrap; margin: 0; line-height: 1.4;">${w}</pre>
          </div>

          <div style="color: #666; font-size: 12px; margin-top: 10px;">
            💡 Salve essas informações para acessar seus canais
          </div>
        </div>
      `,confirmButtonText:"📋 Copiar Dados",confirmButtonColor:"#10b981",width:"min(95vw, 600px)",customClass:{popup:"swal-clean-theme test-result-modal"},preConfirm:()=>{if(navigator.clipboard)navigator.clipboard.writeText(w).then(()=>{R.fire({title:"Dados copiados!",text:"As informações foram copiadas para sua área de transferência",icon:"success",timer:2e3,showConfirmButton:!1})});else{const c=document.createElement("textarea");c.value=w,document.body.appendChild(c),c.select(),document.execCommand("copy"),document.body.removeChild(c),R.fire({title:"Dados copiados!",text:"As informações foram copiadas para sua área de transferência",icon:"success",timer:2e3,showConfirmButton:!1})}return!1}})},u=()=>{const g=w=>{const y=document.getElementById("content-tutoriais"),x=document.getElementById("content-marcas"),c=document.getElementById("tab-tutoriais"),d=document.getElementById("tab-marcas");!y||!x||!c||!d||(y.style.display="none",x.style.display="none",c.style.background="#e5e7eb",c.style.color="#374151",d.style.background="#e5e7eb",d.style.color="#374151",w==="tutoriais"?(y.style.display="block",c.style.background="#3b82f6",c.style.color="white"):w==="marcas"&&(x.style.display="block",d.style.background="#3b82f6",d.style.color="white"))};R.fire({title:"📚 Tutoriais SmartV",html:`
        <div style="text-align: left; padding: 5px;">
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 12px; border-radius: 10px; margin-bottom: 15px;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
              <span style="font-size: 16px;">📖</span>
              <span style="font-size: 15px; font-weight: 500;">Guias de Configuração</span>
            </div>
            <div style="font-size: 13px; margin-top: 5px; text-align: center;">Aprenda a configurar seus dispositivos</div>
          </div>

          <!-- Abas de Navegação -->
          <div style="display: flex; margin-bottom: 15px; border-bottom: 2px solid #e5e7eb; gap: 2px;">
            <button id="tab-tutoriais" style="flex: 1; padding: 8px 12px; background: #3b82f6; color: white; border: none; border-radius: 6px 6px 0 0; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px;">
              🎯 Tutoriais
            </button>
            <button id="tab-marcas" style="flex: 1; padding: 8px 12px; background: #e5e7eb; color: #374151; border: none; border-radius: 6px 6px 0 0; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px;">
              📺 Marcas de TV
            </button>
          </div>

          <!-- Conteúdo da Aba Tutoriais -->
          <div id="content-tutoriais" style="background: #f8fafc; border-radius: 8px; padding: 12px;">
            <h4 style="color: #374151; margin-bottom: 12px; font-weight: 600; font-size: 16px;">🎯 Tutoriais Disponíveis:</h4>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📱 Configuração Mobile:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • XCIPTV (Android/iOS)<br>
                • IPTV Smarters Pro<br>
                • Perfect Player
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📺 Smart TV:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • Samsung Tizen<br>
                • LG WebOS<br>
                • Android TV
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">💻 Computador:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • VLC Media Player<br>
                • Kodi<br>
                • Web Player
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📡 TV Box:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • Android TV Box<br>
                • Configuração STB<br>
                • Portal MAG
              </div>
            </div>
          </div>

          <!-- Conteúdo da Aba Marcas de TV -->
          <div id="content-marcas" style="background: #f8fafc; border-radius: 8px; padding: 12px; display: none;">
            <h4 style="color: #374151; margin-bottom: 12px; font-weight: 600; font-size: 16px;">📺 Marcas de TV Compatíveis:</h4>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Samsung:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: QLED, Crystal UHD, The Frame, The Serif<br>
                • Sistema: Tizen OS • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 LG:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: OLED, NanoCell, UHD<br>
                • Sistema: webOS • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Sony:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Bravia, 4K, OLED<br>
                • Sistema: Google TV (Android TV) • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Philips:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Ambilight, 4K UHD<br>
                • Sistema: Android TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 AOC:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: LED, Smart TV 4K<br>
                • Sistema: Android TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 TCL:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: P8M, 4K<br>
                • Sistema: Google TV / Roku TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Hisense:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: ULED, 4K<br>
                • Sistema: Vidaa U / Google TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Semp Toshiba:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: LED, Smart TV<br>
                • Sistema: Google TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Gradiente:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Smart TV Full HD, 4K<br>
                • Sistema: Google TV • ✅ Total
              </div>
            </div>
          </div>

          <div style="color: #666; font-size: 11px; margin-top: 12px; text-align: center; padding: 8px; background: #f1f5f9; border-radius: 6px;">
            💡 Entre em contato via WhatsApp para receber os tutoriais específicos da sua marca
          </div>
        </div>
      `,confirmButtonText:"📱 Falar no WhatsApp",confirmButtonColor:"#25d366",cancelButtonText:"❌ Fechar",showCancelButton:!0,cancelButtonColor:"#6b7280",width:"min(95vw, 600px)",heightAuto:!1,customClass:{popup:"swal-clean-theme tutorials-modal"},didOpen:()=>{const w=document.getElementById("tab-tutoriais"),y=document.getElementById("tab-marcas");w&&w.addEventListener("click",()=>g("tutoriais")),y&&y.addEventListener("click",()=>g("marcas"))}}).then(w=>{if(w.isConfirmed){const c=`https://wa.me/5541995056052?text=${encodeURIComponent("Olá! Gostaria de receber os tutoriais de configuração do SmartV para meus dispositivos.")}`;window.open(c,"_blank")}})},h=()=>{R.fire({title:"🎉 Teste Grátis - SmartV",html:`
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 18px;">⏰ Teste Grátis por 4 Horas</h3>
            <p style="margin: 0; font-size: 14px;">Experimente nossos canais gratuitamente</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 16px;">
            Preencha seus dados abaixo para liberar o teste:
          </p>

          <form id="testForm" style="text-align: left; margin-top: 20px;">
            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
              <input
                type="text"
                id="testCustomerName"
                placeholder="Seu nome completo"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
              <input
                type="email"
                id="testCustomerEmail"
                placeholder="<EMAIL>"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Plano de Interesse:</label>
              <select
                id="testPlanInterest"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; background: white; font-family: 'Inter', sans-serif; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); cursor: pointer; appearance: none; background-image: url('data:image/svg+xml,%3csvg xmlns=\\'http://www.w3.org/2000/svg\\' fill=\\'none\\' viewBox=\\'0 0 20 20\\'%3e%3cpath stroke=\\'%236b7280\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\' stroke-width=\\'1.5\\' d=\\'m6 8 4 4 4-4\\'/%3e%3c/svg%3e'); background-position: right 12px center; background-repeat: no-repeat; background-size: 16px; padding-right: 40px;"
                required
              >
                <option value="">Selecione um plano</option>
                <option value="Básico">📺 Básico - R$ 19,90</option>
                <option value="Premium">🎬 Premium - R$ 29,90</option>
                <option value="Família">👨‍👩‍👧‍👦 Família - R$ 39,90</option>
              </select>
            </div>
          </form>



          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #495057; font-size: 13px;">
              💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
            </p>
          </div>
        </div>
      `,showCancelButton:!0,confirmButtonText:"📺 Liberar Teste Grátis",cancelButtonText:"📝 Usar Exemplo",confirmButtonColor:"#667eea",cancelButtonColor:"#28a745",width:"min(95vw, 550px)",background:"#ffffff",color:"#333333",allowOutsideClick:!1,heightAuto:!1,customClass:{popup:"test-modal"},didOpen:()=>{setTimeout(()=>{const g=document.getElementById("testCustomerName");g&&g.focus()},100)},preConfirm:()=>{var x,c,d;const g=(x=document.getElementById("testCustomerName"))==null?void 0:x.value.trim(),w=(c=document.getElementById("testCustomerEmail"))==null?void 0:c.value.trim(),y=(d=document.getElementById("testPlanInterest"))==null?void 0:d.value;return!g||!w||!y?(R.showValidationMessage("Por favor, preencha todos os campos"),!1):g.length<3?(R.showValidationMessage("Nome deve ter pelo menos 3 caracteres"),!1):w.includes("@")?{name:g,email:w,planInterest:y}:(R.showValidationMessage("E-mail inválido"),!1)}}).then(async g=>{if(g.isDismissed&&g.dismiss===R.DismissReason.cancel)await l({name:"Paulo Antonio Silva",email:"<EMAIL>",planInterest:"Premium"});else if(g.isConfirmed&&g.value){const{name:w,email:y,planInterest:x}=g.value;await l({name:w,email:y,planInterest:x})}})},p=()=>{t(!1),r({name:"",email:"",plan:""})},v=async()=>{if(!n.name||!n.email||!n.plan){R.fire({title:"Campos obrigatórios",text:"Por favor, preencha todos os campos.",icon:"warning",confirmButtonText:"OK",background:"#ffffff",color:"#333333",confirmButtonColor:"#667eea",customClass:{popup:"swal-clean-theme",title:"swal-clean-title"}});return}_2("⏳ Gerando Teste","Processando sua solicitação...");try{await new Promise(x=>setTimeout(x,3e3));const g={name:n.name,email:n.email,plan:n.plan},w=await mn.test.requestTest(g);R.close();const y=`
🎯 SEU TESTE IPTV FOI GERADO COM SUCESSO!

👤 CREDENCIAIS DE ACESSO:
✅ Usuário: ${w.user}
✅ Senha: ${w.password}
⏰ Válido até: ${w.expirationDate}

📡 SERVIDOR:
🌐 Host: ${w.server.host}
🔌 Porta: ${w.server.port}

🔗 LINKS DE ACESSO:

🟢 LINK M3U (MPEGTS):
${w.urls.m3u}

🟢 LINK M3U CURTO:
${w.urls.short_m3u}

🟡 LINK HLS:
${w.urls.hls}

🟡 LINK HLS CURTO:
${w.urls.short_hls}

🔴 LINK SSIPTV:
${w.urls.ssiptv}

📺 WEB PLAYERS:
http://webtv.iptvblinkplayer.com/
http://webtv.iptvsmarters.com/

📱 COMO USAR NA SUA TV:
1️⃣ Abra a galeria de aplicativos da sua TV
2️⃣ Pesquise por: IPTV Smarters, TiviMate ou SS IPTV
3️⃣ Baixe o aplicativo de sua preferência
4️⃣ Use as credenciais acima para fazer login

💡 DICAS IMPORTANTES:
• Teste válido por 24 horas
• Funciona em Smart TV, celular, tablet e PC
• Mais de 100 canais disponíveis
• Qualidade HD/Full HD
• Suporte 24h via WhatsApp

🎬 APROVEITE SEU TESTE GRÁTIS!
      `.trim();$2(y).then(x=>{x.isConfirmed&&navigator.clipboard.writeText(y).then(()=>{M2("📋 Copiado!","Dados copiados para a área de transferência")}).catch(()=>{gd("❌ Erro","Não foi possível copiar os dados")})}),p()}catch(g){console.error("Error generating test:",g),R.close(),gd("❌ Erro ao Gerar Teste","Não foi possível processar sua solicitação. Tente novamente.")}};return f.jsxs(f.Fragment,{children:[f.jsx("style",{children:`
        .swal-payment-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-payment-popup .swal2-title {
          font-size: 24px !important;
          margin-bottom: 10px !important;
        }
        .swal-payment-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
          max-height: 70vh !important;
          overflow-y: auto !important;
        }
        .swal-payment-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
          flex-direction: row !important;
        }
        .swal-payment-popup .swal2-confirm,
        .swal-payment-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        .swal-payment-popup input {
          transition: border-color 0.2s ease !important;
        }
        .swal-payment-popup input:focus {
          border-color: #667eea !important;
          outline: none !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }
        .swal-payment-popup label {
          user-select: none !important;
        }
        .swal-pix-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-pix-popup .swal2-title {
          font-size: 20px !important;
          margin-bottom: 10px !important;
        }
        .swal-pix-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
        }
        .swal-pix-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
        }
        .swal-pix-popup .swal2-confirm,
        .swal-pix-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        @media (max-width: 640px) {
          .swal-payment-popup .swal2-actions,
          .swal-pix-popup .swal2-actions {
            flex-direction: column !important;
          }
          .swal-payment-popup .swal2-confirm,
          .swal-payment-popup .swal2-cancel,
          .swal-pix-popup .swal2-confirm,
          .swal-pix-popup .swal2-cancel {
            width: 100% !important;
            margin: 0 !important;
          }
        }
      `}),f.jsxs("div",{className:"min-h-screen",children:[f.jsx("header",{className:"bg-black/20 backdrop-blur-sm border-b border-white/10 sticky top-0 z-50",children:f.jsx("div",{className:"container mx-auto px-3 sm:px-4 lg:px-6 py-3 md:py-4",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx(ii,{className:"h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-purple-400 flex-shrink-0"}),f.jsx("h1",{className:"text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-white",children:"SmartV"})]}),f.jsxs(kt,{onClick:h,className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white text-xs sm:text-sm md:text-base px-2 sm:px-3 md:px-4 py-2 md:py-2.5 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:[f.jsx(ey,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0"}),f.jsx("span",{className:"hidden xs:inline",children:"Teste Grátis"}),f.jsx("span",{className:"xs:hidden",children:"Teste"})]})]})})}),f.jsx("section",{className:"py-8 sm:py-12 md:py-16 lg:py-20 px-3 sm:px-4 lg:px-6",children:f.jsx("div",{className:"container mx-auto text-center",children:f.jsxs("div",{className:"max-w-5xl mx-auto",children:[f.jsxs("h2",{className:"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6 md:mb-8 leading-tight px-2",children:["Sua TV",f.jsxs("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400",children:[" ","Inteligente"]})]}),f.jsx("p",{className:"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 mb-6 sm:mb-8 md:mb-10 leading-relaxed px-2 sm:px-4 max-w-4xl mx-auto",children:"Acesso a centenas de canais de TV em alta qualidade. Entretenimento sem limites para toda a família."}),f.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4 mb-6 sm:mb-8 md:mb-12 px-2 sm:px-4",children:[f.jsxs(Uo,{variant:"secondary",className:"bg-purple-500/20 text-purple-300 border-purple-400 text-xs sm:text-sm px-2 sm:px-3 py-1",children:[f.jsx(ed,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0"}),f.jsx("span",{className:"hidden xs:inline",children:"Ativação Imediata"}),f.jsx("span",{className:"xs:hidden",children:"Imediato"})]}),f.jsxs(Uo,{variant:"secondary",className:"bg-green-500/20 text-green-300 border-green-400 text-xs sm:text-sm px-2 sm:px-3 py-1",children:[f.jsx(Yc,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0"}),f.jsx("span",{className:"hidden xs:inline",children:"100% Seguro"}),f.jsx("span",{className:"xs:hidden",children:"Seguro"})]}),f.jsxs(Uo,{variant:"secondary",className:"bg-blue-500/20 text-blue-300 border-blue-400 text-xs sm:text-sm px-2 sm:px-3 py-1",children:[f.jsx(Jc,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0"}),f.jsx("span",{className:"hidden xs:inline",children:"Múltiplas Telas"}),f.jsx("span",{className:"xs:hidden",children:"Multi-tela"})]})]})]})})}),f.jsx("section",{className:"py-8 sm:py-12 md:py-16 lg:py-20 px-3 sm:px-4 lg:px-6",children:f.jsxs("div",{className:"container mx-auto",children:[f.jsxs("div",{className:"text-center mb-8 sm:mb-12 md:mb-16",children:[f.jsx("h3",{className:"text-2xl xs:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6 px-2",children:"Escolha seu Plano"}),f.jsx("p",{className:"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 px-2 sm:px-4 max-w-3xl mx-auto",children:"Planos flexíveis para atender suas necessidades"})]}),f.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 max-w-7xl mx-auto",children:Qs.map(g=>{const w=g.icon;return f.jsxs(Zl,{className:`relative bg-white/5 backdrop-blur-sm border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:scale-105 ${g.popular?"ring-2 ring-purple-400":""}`,children:[g.popular&&f.jsx("div",{className:"absolute -top-3 md:-top-4 left-1/2 transform -translate-x-1/2",children:f.jsxs(Uo,{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs md:text-sm px-3 py-1",children:[f.jsx(Zc,{className:"h-3 w-3 mr-1"}),"Mais Popular"]})}),f.jsxs(jm,{className:"text-center p-4 md:p-6",children:[f.jsx("div",{className:"mx-auto mb-3 md:mb-4 p-2 md:p-3 bg-purple-500/20 rounded-full w-fit",children:f.jsx(w,{className:"h-6 w-6 md:h-8 md:w-8 text-purple-400"})}),f.jsx(Om,{className:"text-xl md:text-2xl text-white mb-2",children:g.name}),f.jsx(Rm,{className:"text-gray-300 text-sm md:text-base leading-relaxed",children:g.description}),f.jsxs("div",{className:"text-3xl md:text-4xl font-bold text-white mt-3 md:mt-4",children:["R$ ",g.price.toFixed(2).replace(".",","),f.jsx("span",{className:"text-base md:text-lg text-gray-400",children:"/mês"})]})]}),f.jsxs(Jl,{className:"p-4 md:p-6 pt-0",children:[f.jsx("ul",{className:"space-y-2 md:space-y-3 mb-4 md:mb-6",children:g.features.map((y,x)=>f.jsxs("li",{className:"flex items-start text-gray-300 text-sm md:text-base",children:[f.jsx("div",{className:"h-2 w-2 bg-purple-400 rounded-full mr-3 mt-2 flex-shrink-0"}),f.jsx("span",{className:"leading-relaxed",children:y})]},x))}),f.jsx(kt,{onClick:()=>o(g),className:"w-full bg-purple-600 hover:bg-purple-700 text-white text-sm md:text-base py-2 md:py-3",children:"Assinar Agora"})]})]},g.id)})})]})}),f.jsx("section",{className:"py-12 md:py-16 lg:py-20 px-4 bg-black/20",children:f.jsxs("div",{className:"container mx-auto",children:[f.jsx("div",{className:"text-center mb-12 md:mb-16",children:f.jsx("h3",{className:"text-3xl md:text-4xl font-bold text-white mb-3 md:mb-4",children:"Por que escolher SmartV?"})}),f.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8",children:[{icon:ed,title:"Ativação Imediata",description:"Comece a assistir em segundos após o pagamento"},{icon:Yc,title:"Totalmente Seguro",description:"Pagamentos protegidos e dados criptografados"},{icon:Jc,title:"Múltiplas Telas",description:"Assista em TV, celular, tablet e computador"},{icon:Zc,title:"Qualidade Premium",description:"Transmissão em HD com estabilidade garantida"}].map((g,w)=>f.jsxs("div",{className:"text-center p-4",children:[f.jsx("div",{className:"mx-auto mb-3 md:mb-4 p-3 md:p-4 bg-purple-500/20 rounded-full w-fit",children:f.jsx(g.icon,{className:"h-6 w-6 md:h-8 md:w-8 text-purple-400"})}),f.jsx("h4",{className:"text-lg md:text-xl font-semibold text-white mb-2",children:g.title}),f.jsx("p",{className:"text-gray-300 text-sm md:text-base leading-relaxed",children:g.description})]},w))})]})}),f.jsx("section",{className:"py-12 md:py-16 lg:py-20 px-4",children:f.jsx("div",{className:"container mx-auto text-center",children:f.jsxs("div",{className:"max-w-3xl mx-auto",children:[f.jsx("h3",{className:"text-3xl md:text-4xl font-bold text-white mb-4 md:mb-6",children:"Pronto para começar?"}),f.jsx("p",{className:"text-lg md:text-xl text-gray-300 mb-6 md:mb-8 leading-relaxed px-4",children:"Teste grátis por 4 horas ou assine agora e tenha acesso imediato"}),f.jsxs("div",{className:"flex flex-col gap-4 justify-center max-w-sm mx-auto",children:[f.jsxs(kt,{size:"lg",onClick:h,className:"relative bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white w-full py-5 px-8 text-lg font-bold shadow-2xl hover:shadow-xl transition-all duration-300 transform hover:scale-105 animate-pulse hover:animate-none border-2 border-green-400 overflow-hidden group rounded-xl",children:[f.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"}),f.jsxs("div",{className:"relative flex items-center justify-center gap-3",children:[f.jsx("span",{className:"text-2xl",children:"🎬"}),f.jsxs("div",{className:"flex flex-col",children:[f.jsx("span",{className:"font-extrabold text-lg",children:"TESTE GRÁTIS"}),f.jsx("span",{className:"text-sm font-normal opacity-90",children:"4 horas completas"})]}),f.jsx("span",{className:"text-2xl animate-bounce",children:"▶"})]})]}),f.jsx(kt,{size:"lg",onClick:()=>o(Qs[1]),className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white w-full py-4 px-8 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-lg",children:f.jsxs("div",{className:"flex items-center justify-center gap-3",children:[f.jsx("span",{className:"text-xl",children:"💎"}),f.jsx("span",{children:"Assinar Agora"})]})}),f.jsx(kt,{size:"lg",onClick:u,className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white w-full py-4 px-8 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-lg",children:f.jsxs("div",{className:"flex items-center justify-center gap-3",children:[f.jsx("span",{className:"text-xl",children:"📖"}),f.jsx("span",{children:"Tutoriais"})]})})]})]})})}),f.jsx("footer",{className:"bg-black/40 border-t border-white/10 py-4 sm:py-6 md:py-8 px-3 sm:px-4 lg:px-6",children:f.jsxs("div",{className:"container mx-auto text-center",children:[f.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-2 sm:mb-3 md:mb-4",children:[f.jsx(ii,{className:"h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-purple-400 flex-shrink-0"}),f.jsx("span",{className:"text-base sm:text-lg md:text-xl lg:text-2xl font-bold text-white",children:"SmartV"})]}),f.jsx("p",{className:"text-gray-400 text-xs sm:text-sm md:text-base px-2",children:"© 2024 - 2025 SmartV. Todos os direitos reservados."})]})}),e&&f.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50",children:f.jsxs("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-sm sm:max-w-md mx-auto relative max-h-[95vh] overflow-y-auto",children:[f.jsx("button",{onClick:p,className:"absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors z-10",children:f.jsx(bm,{className:"h-5 w-5 sm:h-6 sm:w-6"})}),f.jsxs("div",{className:"text-center p-4 sm:p-6 pb-0",children:[f.jsx("div",{className:"flex items-center justify-center mb-3 sm:mb-4",children:f.jsx("div",{className:"bg-orange-100 p-2 rounded-lg",children:f.jsx(ii,{className:"h-5 w-5 sm:h-6 sm:w-6 text-orange-600"})})}),f.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4",children:"📺 Teste Grátis - SmartV"})]}),f.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 sm:px-6 py-3 sm:py-4 mx-4 sm:mx-6 rounded-lg mb-3 sm:mb-4",children:[f.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[f.jsx(Z0,{className:"h-4 w-4 sm:h-5 sm:w-5"}),f.jsx("span",{className:"font-semibold text-base sm:text-lg",children:"Teste Grátis por 4 Horas"})]}),f.jsx("p",{className:"text-center text-xs sm:text-sm mt-1 opacity-90",children:"Experimente nossos canais gratuitamente"})]}),f.jsx("div",{className:"px-4 sm:px-6 pb-3 sm:pb-4",children:f.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[f.jsxs("div",{children:[f.jsx(Ws,{htmlFor:"name",className:"text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2",children:"Nome Completo"}),f.jsx(od,{id:"name",type:"text",placeholder:"Seu nome completo",value:n.name,onChange:g=>r({...n,name:g.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),f.jsxs("div",{children:[f.jsx(Ws,{htmlFor:"email",className:"text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2",children:"E-mail"}),f.jsx(od,{id:"email",type:"email",placeholder:"<EMAIL>",value:n.email,onChange:g=>r({...n,email:g.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),f.jsxs("div",{children:[f.jsx(Ws,{htmlFor:"plan",className:"text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2",children:"Plano de Interesse"}),f.jsxs("select",{id:"plan",value:n.plan,onChange:g=>r({...n,plan:g.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white",children:[f.jsx("option",{value:"",children:"Selecione um plano"}),Qs.map(g=>f.jsxs("option",{value:g.id,children:[g.name," - R$ ",g.price.toFixed(2)]},g.id))]})]})]})}),f.jsxs("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 px-4 sm:px-6 pb-4 sm:pb-6",children:[f.jsx(kt,{onClick:v,className:"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base",children:"📺 Liberar Teste Grátis"}),f.jsx(kt,{onClick:p,className:"px-4 sm:px-6 py-2.5 sm:py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-semibold text-sm sm:text-base",children:"✅ Fechar"})]})]})}),f.jsx(B2,{phoneNumber:"5541995056052",message:"Olá! Gostaria de saber mais sobre os planos SmartV."})]})]})}function U2(){var x;const e=tu(),t=Bf(),{toast:n}=Vf(),r=(x=e.state)==null?void 0:x.selectedPlan,[o,i]=b.useState({customerName:"",customerEmail:"",customerDocument:"",customerPhone:""}),[s,l]=b.useState(null),[a,u]=b.useState(null),[h,p]=b.useState(!1),[v,g]=b.useState(!1);b.useEffect(()=>{r||t("/")},[r,t]),b.useEffect(()=>{r&&!v&&(g(!0),R.fire({title:"🎉 Ótima Escolha!",html:`
          <div style="text-align: center; color: #333; padding: 10px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
              <h3 style="margin: 0 0 8px 0; font-size: 18px;">📺 ${r.name}</h3>
              <p style="margin: 0; font-size: 24px; font-weight: bold;">R$ ${r.price.toFixed(2).replace(".",",")}</p>
            </div>

            <p style="margin: 16px 0; color: #666; font-size: 16px;">
              Preencha seus dados abaixo para gerar o PIX:
            </p>

            <form id="quickPaymentForm" style="text-align: left; margin-top: 20px;">
              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
                <input
                  type="text"
                  id="modalCustomerName"
                  placeholder="Seu nome completo"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
                <input
                  type="email"
                  id="modalCustomerEmail"
                  placeholder="<EMAIL>"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">CPF:</label>
                <input
                  type="text"
                  id="modalCustomerDocument"
                  placeholder="000.000.000-00"
                  maxlength="14"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">WhatsApp:</label>
                <input
                  type="text"
                  id="modalCustomerPhone"
                  placeholder="(11) 99999-9999"
                  maxlength="15"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>
            </form>

            <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
              <p style="margin: 0; color: #495057; font-size: 13px;">
                💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
              </p>
            </div>
          </div>
        `,showCancelButton:!0,confirmButtonText:"🚀 Gerar PIX",cancelButtonText:"📝 Usar Exemplo",confirmButtonColor:"#667eea",cancelButtonColor:"#28a745",width:"min(95vw, 550px)",background:"#ffffff",color:"#333333",allowOutsideClick:!1,customClass:{popup:"swal-welcome-popup"},didOpen:()=>{const c=document.getElementById("modalCustomerDocument"),d=document.getElementById("modalCustomerPhone");c&&c.addEventListener("input",m=>{const C=m.target;let S=C.value.replace(/\\D/g,"");S=S.replace(/(\\d{3})(\\d)/,"$1.$2"),S=S.replace(/(\\d{3})(\\d)/,"$1.$2"),S=S.replace(/(\\d{3})(\\d{1,2})$/,"$1-$2"),C.value=S}),d&&d.addEventListener("input",m=>{const C=m.target;let S=C.value.replace(/\\D/g,"");S=S.replace(/^(\\d{2})(\\d)/g,"($1) $2"),S=S.replace(/(\\d{5})(\\d)/,"$1-$2"),C.value=S}),setTimeout(()=>{const m=document.getElementById("modalCustomerName");m&&m.focus()},100)},preConfirm:()=>{var S,T,E,N;const c=(S=document.getElementById("modalCustomerName"))==null?void 0:S.value.trim(),d=(T=document.getElementById("modalCustomerEmail"))==null?void 0:T.value.trim(),m=(E=document.getElementById("modalCustomerDocument"))==null?void 0:E.value.trim(),C=(N=document.getElementById("modalCustomerPhone"))==null?void 0:N.value.trim();return!c||!d||!m||!C?(R.showValidationMessage("Por favor, preencha todos os campos"),!1):c.length<3?(R.showValidationMessage("Nome deve ter pelo menos 3 caracteres"),!1):d.includes("@")?m.replace(/\\D/g,"").length!==11?(R.showValidationMessage("CPF deve ter 11 dígitos"),!1):C.replace(/\\D/g,"").length<10?(R.showValidationMessage("Telefone inválido"),!1):{name:c,email:d,document:m,phone:C}:(R.showValidationMessage("E-mail inválido"),!1)}}).then(c=>{if(c.isDismissed&&c.dismiss===R.DismissReason.cancel)i({customerName:"Paulo Antonio Silva",customerEmail:"<EMAIL>",customerDocument:"123.456.789-00",customerPhone:"(11) 99999-9999"}),setTimeout(async()=>{await y()},500);else if(c.isConfirmed&&c.value){const{name:d,email:m,document:C,phone:S}=c.value;i({customerName:d,customerEmail:m,customerDocument:C,customerPhone:S}),setTimeout(async()=>{await y()},500)}}))},[r,v,n]),b.useEffect(()=>{let c;return s&&!(a!=null&&a.paid)&&(c=setInterval(async()=>{try{const d=await mn.payment.checkPayment({paymentId:s.id});if(u(d),d.paid){await D2({customerName:o.customerName,planType:r.name,amount:r.price,paymentId:s.id,paidAt:d.paidAt||new Date().toISOString()}),n({title:"Pagamento confirmado!",description:"Seu acesso foi liberado com sucesso."});try{await mn.whatsapp.sendReceipt({paymentId:s.id,customerName:o.customerName,customerPhone:o.customerPhone,planType:r.name,amount:r.price,paidAt:d.paidAt||new Date().toISOString()})}catch(m){console.error("Failed to send WhatsApp receipt:",m)}try{await mn.whatsapp.sendAdminNotification({paymentId:s.id,customerName:o.customerName,customerPhone:o.customerPhone,planType:r.name,amount:r.price,paidAt:d.paidAt||new Date().toISOString()}),console.log("✅ Admin notification sent successfully")}catch(m){console.error("❌ Failed to send admin notification:",m)}}}catch(d){console.error("Error checking payment:",d)}},5e3)),()=>{c&&clearInterval(c)}},[s,a==null?void 0:a.paid,o,r,n]);const w=async c=>{const d=c.qrCode||"",m=new Date(Date.now()+15*60*1e3),C=(r==null?void 0:r.price)||0;await R.fire({title:"💳 Pagamento PIX",html:`
        <div style="text-align: center; color: #333; padding: 0 8px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; border-radius: 12px; margin-bottom: 16px;">
            <h3 style="margin: 0 0 6px 0; font-size: clamp(16px, 4vw, 18px);">💰 Valor: R$ ${C.toFixed(2).replace(".",",")}</h3>
            <p style="margin: 0; font-size: clamp(12px, 3vw, 14px); opacity: 0.9;">${(r==null?void 0:r.name)||"Plano Selecionado"}</p>
          </div>
          <p style="margin-bottom: 16px; color: #666; font-size: clamp(14px, 3.5vw, 16px);">Escaneie o QR Code ou copie o código PIX</p>

          <div style="background: white; padding: 16px; border-radius: 12px; margin: 16px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
            ${c.qrCodeUrl?`
              <img
                src="data:image/png;base64,${c.qrCodeUrl}"
                alt="QR Code PIX"
                style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; display: block; border-radius: 8px;"
                onload="console.log('QR Code carregado com sucesso!');"
                onerror="console.error('Erro ao carregar QR Code'); this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; background: #f0f0f0; border-radius: 8px; display: none; align-items: center; justify-content: center; border: 2px dashed #ccc;">
                <span style="color: #999; font-size: clamp(12px, 3vw, 14px);">Erro ao carregar QR Code</span>
              </div>
            `:`
              <div style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ccc;">
                <span style="color: #999; font-size: clamp(12px, 3vw, 14px);">QR Code não disponível</span>
              </div>
            `}
          </div>

          <div style="margin: 16px 0;">
            <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #333; font-size: clamp(12px, 3vw, 14px);">Código PIX:</label>
            <div style="position: relative;">
              <textarea
                id="pixCodeArea"
                readonly
                style="width: 100%; height: 70px; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-family: monospace; font-size: clamp(10px, 2.5vw, 12px); resize: none; background: #f9f9f9; box-sizing: border-box; padding-right: 50px;"
              >${d}</textarea>
              <button
                onclick="
                  const textarea = document.getElementById('pixCodeArea');
                  const pixCodeToCopy = textarea ? textarea.value.trim() : '';
                  const button = this;

                  console.log('Botão pequeno - Tentando copiar:', pixCodeToCopy);

                  if (!pixCodeToCopy) {
                    alert('Código PIX não encontrado');
                    return;
                  }

                  // Função para mostrar sucesso
                  const showSuccess = () => {
                    button.innerHTML = '✅';
                    button.style.background = '#28a745';
                    button.title = 'Copiado!';
                    setTimeout(() => {
                      button.innerHTML = '📋';
                      button.style.background = '#007bff';
                      button.title = 'Copiar código';
                    }, 2000);
                  };

                  // Função para mostrar erro
                  const showError = () => {
                    textarea.focus();
                    textarea.select();
                    textarea.setSelectionRange(0, 99999);
                    alert('Código selecionado! Use Ctrl+C para copiar');
                  };

                  // Tentar clipboard API primeiro
                  if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(pixCodeToCopy)
                      .then(() => {
                        console.log('Copiado via clipboard API');
                        showSuccess();
                      })
                      .catch((err) => {
                        console.log('Erro clipboard API, tentando fallback:', err);
                        // Fallback
                        try {
                          const tempTextArea = document.createElement('textarea');
                          tempTextArea.value = pixCodeToCopy;
                          tempTextArea.style.position = 'fixed';
                          tempTextArea.style.left = '-9999px';
                          tempTextArea.style.top = '-9999px';
                          document.body.appendChild(tempTextArea);
                          tempTextArea.focus();
                          tempTextArea.select();
                          tempTextArea.setSelectionRange(0, 99999);
                          const successful = document.execCommand('copy');
                          document.body.removeChild(tempTextArea);

                          if (successful) {
                            console.log('Copiado via execCommand');
                            showSuccess();
                          } else {
                            console.log('execCommand falhou');
                            showError();
                          }
                        } catch (e) {
                          console.log('Erro no fallback:', e);
                          showError();
                        }
                      });
                  } else {
                    // Fallback direto para navegadores antigos
                    try {
                      const tempTextArea = document.createElement('textarea');
                      tempTextArea.value = pixCodeToCopy;
                      tempTextArea.style.position = 'fixed';
                      tempTextArea.style.left = '-9999px';
                      tempTextArea.style.top = '-9999px';
                      document.body.appendChild(tempTextArea);
                      tempTextArea.focus();
                      tempTextArea.select();
                      tempTextArea.setSelectionRange(0, 99999);
                      const successful = document.execCommand('copy');
                      document.body.removeChild(tempTextArea);

                      if (successful) {
                        console.log('Copiado via execCommand (fallback direto)');
                        showSuccess();
                      } else {
                        console.log('execCommand falhou (fallback direto)');
                        showError();
                      }
                    } catch (e) {
                      console.log('Erro no fallback direto:', e);
                      showError();
                    }
                  }
                "
                title="Copiar código"
                style="position: absolute; right: 5px; top: 5px; background: #007bff; color: white; border: none; padding: 8px 10px; border-radius: 4px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; z-index: 10;">
                📋
              </button>
            </div>
          </div>

          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; border-radius: 12px; margin: 16px 0;">
            <h4 style="margin: 0 0 10px 0; font-size: clamp(14px, 3.5vw, 16px);">Como pagar:</h4>
            <ol style="text-align: left; margin: 0; padding-left: 16px; font-size: clamp(12px, 3vw, 14px); line-height: 1.5;">
              <li style="margin-bottom: 4px;">Abra o app do seu banco</li>
              <li style="margin-bottom: 4px;">Escolha a opção PIX</li>
              <li style="margin-bottom: 4px;">Escaneie o QR Code ou cole o código</li>
              <li>Confirme o pagamento</li>
            </ol>
          </div>

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 10px 12px; margin: 12px 0;">
            <p style="margin: 0; color: #856404; font-size: clamp(12px, 3vw, 14px); line-height: 1.4;">
              ⏰ <strong>PIX válido por 15 minutos</strong><br>
              Expira em: ${m.toLocaleTimeString("pt-BR")}<br>
              <span id="countdown-timer" style="font-weight: bold; color: #d63384; font-size: clamp(11px, 2.8vw, 13px);"></span>
            </p>
          </div>
        </div>
      `,showCancelButton:!0,confirmButtonText:"📋 Copiar Código",cancelButtonText:"✅ Fechar",allowOutsideClick:!1,preConfirm:async()=>{const S=document.getElementById("pixCodeArea"),T=S?S.value.trim():`${d}`.trim();if(console.log("Botão principal - Tentando copiar código PIX:",T),console.log("Clipboard disponível:",!!navigator.clipboard),console.log("Contexto seguro:",window.isSecureContext),console.log("Tamanho do código:",T.length),!T)return console.error("Código PIX vazio"),R.fire({title:"Erro",text:"Código PIX não encontrado",icon:"error",confirmButtonText:"OK"}),!1;try{let E=!1;if(navigator.clipboard&&window.isSecureContext){console.log("Usando navigator.clipboard");try{await navigator.clipboard.writeText(T),console.log("✅ Código copiado com sucesso via clipboard API"),E=!0}catch(N){console.log("❌ Erro no clipboard API:",N),E=!1}}if(!E){console.log("Usando fallback document.execCommand");try{const N=document.createElement("textarea");N.value=T,N.style.position="fixed",N.style.left="-9999px",N.style.top="-9999px",N.style.opacity="0",N.style.pointerEvents="none",N.setAttribute("readonly",""),document.body.appendChild(N),N.focus(),N.select(),N.setSelectionRange(0,99999);const M=document.execCommand("copy");document.body.removeChild(N),console.log("Resultado do execCommand:",M),E=M}catch(N){console.log("❌ Erro no execCommand:",N),E=!1}}return E?R.fire({title:"✅ Código Copiado!",text:"Cole no seu app do banco para pagar",timer:2500,showConfirmButton:!1,toast:!0,position:"top-end",icon:"success",background:"#d4edda",color:"#155724",timerProgressBar:!0}):(S&&(S.focus(),S.select(),S.setSelectionRange(0,99999)),R.fire({title:"📋 Código Selecionado!",text:"Use Ctrl+C (ou Cmd+C no Mac) para copiar",icon:"info",confirmButtonText:"OK",timer:5e3,timerProgressBar:!0})),!1}catch(E){return console.error("❌ Erro geral ao copiar:",E),S&&(S.focus(),S.select(),S.setSelectionRange(0,99999)),R.fire({title:"⚠️ Erro ao Copiar",text:"Código selecionado. Use Ctrl+C para copiar manualmente",icon:"warning",confirmButtonText:"OK"}),!1}},background:"#ffffff",color:"#333333",confirmButtonColor:"#667eea",cancelButtonColor:"#6c757d",width:"min(98vw, 500px)",padding:"16px",customClass:{popup:"swal-dark-theme swal-wide",title:"swal-title",htmlContainer:"swal-html-container",confirmButton:"swal-button",cancelButton:"swal-button swal-button--cancel"},didOpen:()=>{const S=document.getElementById("countdown-timer");if(S){const T=m.getTime(),E=()=>{const O=new Date().getTime(),K=T-O;if(K>0){const $=Math.floor(K/6e4),Ee=Math.floor(K%(1e3*60)/1e3);S.textContent="Tempo restante: "+$.toString().padStart(2,"0")+":"+Ee.toString().padStart(2,"0")}else S.textContent="PIX expirado!",S.style.color="#dc3545"};E();const N=setInterval(E,1e3),M=new MutationObserver(O=>{O.forEach(K=>{K.type==="childList"&&(document.querySelector(".swal2-container")||(clearInterval(N),M.disconnect()))})});M.observe(document.body,{childList:!0})}}})},y=async()=>{if(r){p(!0);try{const c={customerName:o.customerName,customerEmail:o.customerEmail,customerDocument:o.customerDocument.replace(/\D/g,""),customerPhone:o.customerPhone.replace(/\D/g,""),planType:r.name,amount:r.price},d=await mn.payment.createPix(c);l(d),await w(d)}catch(c){console.error("Error creating PIX:",c),n({title:"Erro ao gerar PIX",description:"Tente novamente em alguns instantes.",variant:"destructive"})}finally{p(!1)}}};return r?a!=null&&a.paid?f.jsx("div",{className:"min-h-screen flex items-center justify-center p-4",children:f.jsxs(Zl,{className:"w-full max-w-md bg-white/5 backdrop-blur-sm border-white/10",children:[f.jsxs(jm,{className:"text-center p-4 md:p-6",children:[f.jsx("div",{className:"mx-auto mb-3 md:mb-4 p-3 md:p-4 bg-green-500/20 rounded-full w-fit",children:f.jsx(Xc,{className:"h-8 w-8 md:h-12 md:w-12 text-green-400"})}),f.jsx(Om,{className:"text-xl md:text-2xl text-white",children:"Pagamento Confirmado!"}),f.jsx(Rm,{className:"text-gray-300 text-sm md:text-base",children:"Seu acesso foi liberado com sucesso"})]}),f.jsxs(Jl,{className:"space-y-4 p-4 md:p-6 pt-0",children:[f.jsx("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-3 md:p-4",children:f.jsxs("p",{className:"text-green-300 text-xs md:text-sm leading-relaxed",children:["✅ Plano: ",r.name,f.jsx("br",{}),"💰 Valor: R$ ",r.price.toFixed(2).replace(".",","),f.jsx("br",{}),"📱 Comprovante enviado via WhatsApp"]})}),f.jsx(kt,{onClick:()=>t("/"),className:"w-full bg-purple-600 hover:bg-purple-700 text-sm md:text-base py-2 md:py-3",children:"Voltar ao Início"})]})]})}):f.jsxs(f.Fragment,{children:[f.jsx("style",{children:`
        .swal-welcome-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-welcome-popup .swal2-title {
          font-size: 24px !important;
          margin-bottom: 10px !important;
        }
        .swal-welcome-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
          max-height: 70vh !important;
          overflow-y: auto !important;
        }
        .swal-welcome-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
          flex-direction: row !important;
        }
        .swal-welcome-popup .swal2-confirm,
        .swal-welcome-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        .swal-welcome-popup input {
          transition: border-color 0.2s ease !important;
        }
        .swal-welcome-popup input:focus {
          border-color: #667eea !important;
          outline: none !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }
        .swal-welcome-popup label {
          user-select: none !important;
        }
        @media (max-width: 640px) {
          .swal-welcome-popup .swal2-actions {
            flex-direction: column !important;
          }
          .swal-welcome-popup .swal2-confirm,
          .swal-welcome-popup .swal2-cancel {
            width: 100% !important;
            margin: 0 !important;
          }
        }
      `}),f.jsx("div",{className:"min-h-screen p-2 sm:p-4",children:f.jsxs("div",{className:"container mx-auto max-w-7xl",children:[f.jsx("div",{className:"mb-3 sm:mb-4 md:mb-6",children:f.jsxs(kt,{variant:"ghost",onClick:()=>t("/"),className:"text-white hover:text-purple-400 text-xs sm:text-sm md:text-base p-1.5 sm:p-2 md:p-3",children:[f.jsx(Y0,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 md:mr-2"}),"Voltar"]})}),f.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:f.jsx(Zl,{className:"bg-white/5 backdrop-blur-sm border-white/10 max-w-md w-full",children:f.jsxs(Jl,{className:"p-6 text-center",children:[f.jsxs("div",{className:"mb-6",children:[f.jsx("div",{className:"mx-auto mb-4 p-4 bg-purple-500/20 rounded-full w-fit",children:f.jsx(Xc,{className:"h-8 w-8 text-purple-400"})}),f.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Plano Selecionado"}),f.jsxs("p",{className:"text-gray-300 text-sm",children:["Você escolheu o plano ",f.jsx("strong",{children:r.name})]})]}),f.jsxs("div",{className:"bg-white/10 rounded-lg p-4 mb-6",children:[f.jsxs("div",{className:"flex justify-between items-center mb-2",children:[f.jsx("span",{className:"text-gray-300",children:"Plano:"}),f.jsx("span",{className:"text-white font-medium",children:r.name})]}),f.jsxs("div",{className:"flex justify-between items-center",children:[f.jsx("span",{className:"text-gray-300",children:"Valor:"}),f.jsxs("span",{className:"text-purple-400 font-bold text-lg",children:["R$ ",r.price.toFixed(2).replace(".",",")]})]})]}),f.jsx("p",{className:"text-gray-400 text-sm",children:"O modal de pagamento será aberto automaticamente para você preencher seus dados e gerar o PIX."})]})})})]})})]}):null}function V2(){return f.jsx(Yv,{children:f.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:[f.jsxs(Gv,{children:[f.jsx(Hl,{path:"/",element:f.jsx(F2,{})}),f.jsx(Hl,{path:"/payment",element:f.jsx(U2,{})})]}),f.jsx(Oy,{})]})})}Xs.createRoot(document.getElementById("root")).render(f.jsx(bt.StrictMode,{children:f.jsx(V2,{})}));
