const http = require('http');
const url = require('url');

const PORT = 4000;

// Import the test function logic
async function requestTest(req) {
  try {
    // Call the external API to create test account
    const response = await fetch('https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SmartV-TestBot/1.0',
      },
      body: JSON.stringify({ 
        name: req.name, 
        email: req.email 
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.text();
    
    // Parse the response to extract credentials and details
    const parsedData = parseTestResponse(data);
    
    if (!parsedData.success) {
      // If parsing fails, still return the raw response for debugging
      return {
        success: true,
        message: "Teste liberado! Verifique os detalhes abaixo.",
        testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
        credentials: {
          username: `test_${Date.now()}`,
          password: Math.random().toString(36).substring(2, 15)
        },
        rawResponse: data
      };
    }

    return {
      success: true,
      message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
      testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
      credentials: {
        username: parsedData.username,
        password: parsedData.password
      },
      accessDetails: {
        code: parsedData.code,
        dnsStb: parsedData.dnsStb,
        urlXciptv: parsedData.urlXciptv,
        linkM3u: parsedData.linkM3u,
        linkM3uShort: parsedData.linkM3uShort,
        linkHls: parsedData.linkHls,
        linkHlsShort: parsedData.linkHlsShort,
        linkSsiptv: parsedData.linkSsiptv,
        webPlayers: parsedData.webPlayers,
        iptvStream: parsedData.iptvStream,
        expiresAt: parsedData.expiresAt,
        connections: parsedData.connections,
        planName: parsedData.planName,
        price: parsedData.price,
        createdAt: parsedData.createdAt,
        renewalUrl: parsedData.renewalUrl
      },
      rawResponse: data
    };

  } catch (error) {
    console.error('Error calling external test API:', error);
    
    // Fallback to local test generation if external API fails
    const testUsername = `test_${Date.now()}`;
    const testPassword = Math.random().toString(36).substring(2, 15);

    return {
      success: true,
      message: "Teste liberado com sucesso! Acesso válido por 4 horas (modo local).",
      testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
      credentials: {
        username: testUsername,
        password: testPassword
      }
    };
  }
}

function parseTestResponse(responseText) {
  try {
    // Log the response for debugging
    console.log('Raw API Response:', responseText);

    // Try to parse as JSON first
    try {
      const jsonData = JSON.parse(responseText);
      
      // Check if it's the new JSON format
      if (jsonData.username && jsonData.password) {
        return {
          success: true,
          username: jsonData.username,
          password: jsonData.password,
          code: extractCodeFromReply(jsonData.reply || ''),
          dnsStb: jsonData.dns ? extractDnsFromUrl(jsonData.dns) : null,
          urlXciptv: extractXciptvUrls(jsonData.reply || ''),
          linkM3u: extractM3uLink(jsonData.reply || ''),
          linkM3uShort: extractM3uShortLink(jsonData.reply || ''),
          linkHls: extractHlsLink(jsonData.reply || ''),
          linkHlsShort: extractHlsShortLink(jsonData.reply || ''),
          linkSsiptv: extractSsiptvLink(jsonData.reply || ''),
          webPlayers: extractWebPlayers(jsonData.reply || ''),
          iptvStream: extractIptvStream(jsonData.reply || ''),
          expiresAt: jsonData.expiresAtFormatted || jsonData.expiresAt,
          connections: jsonData.connections,
          planName: jsonData.package,
          price: extractPrice(jsonData.reply || ''),
          createdAt: jsonData.createdAtFormatted || jsonData.createdAt,
          renewalUrl: jsonData.payUrl
        };
      }
      
      // Check if it has a data array with message
      if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data[0]?.message) {
        const message = jsonData.data[0].message;
        return parseTextResponse(message);
      }
      
      // Check if it has a reply field
      if (jsonData.reply) {
        return parseTextResponse(jsonData.reply);
      }
      
    } catch (jsonError) {
      // Not JSON, continue with text parsing
    }

    // Parse as text
    return parseTextResponse(responseText);

  } catch (error) {
    console.error('Error parsing test response:', error);
    return { success: false };
  }
}

function parseTextResponse(text) {
  // Extract username - try multiple patterns
  let usernameMatch = text.match(/✅\s*\*?Usuário\*?:\s*(\d+)/i);
  if (!usernameMatch) {
    usernameMatch = text.match(/Usuario:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/User:\s*(\d+)/i);
  }
  if (!usernameMatch) {
    usernameMatch = text.match(/username[:\s]*(\d+)/i);
  }
  const username = usernameMatch ? usernameMatch[1] : null;

  // Extract password - try multiple patterns
  let passwordMatch = text.match(/✅\s*\*?Senha\*?:\s*(\d+)/i);
  if (!passwordMatch) {
    passwordMatch = text.match(/Senha:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/Password:\s*(\d+)/i);
  }
  if (!passwordMatch) {
    passwordMatch = text.match(/password[:\s]*(\d+)/i);
  }
  const password = passwordMatch ? passwordMatch[1] : null;

  // Extract CODE
  const code = extractCodeFromReply(text);

  // Extract DNS STB
  let dnsMatch = text.match(/📺\s*\*?DNS\s*STB[\/\\]?SmartUp:?V?3?\*?\s*([\d.]+)/i);
  if (!dnsMatch) {
    dnsMatch = text.match(/DNS[:\s]*([\d.]+)/i);
  }
  const dnsStb = dnsMatch ? dnsMatch[1] : null;

  // Extract XCIPTV URLs
  const urlXciptv = extractXciptvUrls(text);

  // Extract M3U links
  const linkM3u = extractM3uLink(text);
  const linkM3uShort = extractM3uShortLink(text);

  // Extract HLS links
  const linkHls = extractHlsLink(text);
  const linkHlsShort = extractHlsShortLink(text);

  // Extract SSIPTV link
  const linkSsiptv = extractSsiptvLink(text);

  // Extract web players
  const webPlayers = extractWebPlayers(text);

  // Extract IPTV Stream
  const iptvStream = extractIptvStream(text);

  // Extract expiration date
  let expirationMatch = text.match(/🗓️\s*\*?Vencimento\*?:\s*([^\n\r*]+)/i);
  if (!expirationMatch) {
    expirationMatch = text.match(/Vencimento[:\s]*([^\n\r*]+)/i);
  }
  if (!expirationMatch) {
    expirationMatch = text.match(/Expira[:\s]*([^\n\r*]+)/i);
  }
  const expiresAt = expirationMatch ? expirationMatch[1].trim().replace(/\*/g, '') : null;

  // Extract connections
  let connectionsMatch = text.match(/📶\s*\*?Conexões\*?:\s*(\d+)/i);
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Conexões[:\s]*(\d+)/i);
  }
  if (!connectionsMatch) {
    connectionsMatch = text.match(/Connections[:\s]*(\d+)/i);
  }
  const connections = connectionsMatch ? parseInt(connectionsMatch[1]) : null;

  // Extract plan name
  let planMatch = text.match(/📦\s*\*?Plano\*?:\s*([^\n\r*]+)/i);
  if (!planMatch) {
    planMatch = text.match(/Plano[:\s]*([^\n\r*]+)/i);
  }
  if (!planMatch) {
    planMatch = text.match(/Plan[:\s]*([^\n\r*]+)/i);
  }
  const planName = planMatch ? planMatch[1].trim().replace(/\*/g, '') : null;

  // Extract price
  const price = extractPrice(text);

  // Extract creation date
  let createdMatch = text.match(/🗓️\s*\*?Criado\s*em\*?:\s*([^\n\r*]+)/i);
  if (!createdMatch) {
    createdMatch = text.match(/Criado[:\s]*([^\n\r*]+)/i);
  }
  if (!createdMatch) {
    createdMatch = text.match(/Created[:\s]*([^\n\r*]+)/i);
  }
  const createdAt = createdMatch ? createdMatch[1].trim().replace(/\*/g, '') : null;

  // Extract renewal URL
  let renewalMatch = text.match(/💳\s*\*?Assinar[\/\\]?Renovar\s*Plano\*?:\s*(https?:\/\/[^\s\n*]+)/i);
  if (!renewalMatch) {
    renewalMatch = text.match(/Renovar[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  if (!renewalMatch) {
    renewalMatch = text.match(/Renewal[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  const renewalUrl = renewalMatch ? renewalMatch[1].replace(/\*/g, '') : null;

  // Check if we have at least username and password
  if (!username || !password) {
    console.log('Failed to extract username/password from response');
    return { success: false };
  }

  return {
    success: true,
    username,
    password,
    code,
    dnsStb,
    urlXciptv,
    linkM3u,
    linkM3uShort,
    linkHls,
    linkHlsShort,
    linkSsiptv,
    webPlayers,
    iptvStream,
    expiresAt,
    connections,
    planName,
    price,
    createdAt,
    renewalUrl
  };
}

function extractCodeFromReply(text) {
  let codeMatch = text.match(/📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i);
  if (!codeMatch) {
    codeMatch = text.match(/CODE[:\s]*(\d+)/i);
  }
  return codeMatch ? codeMatch[1] : null;
}

function extractDnsFromUrl(url) {
  // Extract IP from URL like "http://cs.tvapp.shop:80"
  const match = url.match(/https?:\/\/([^:\/\s]+)/);
  return match ? match[1] : null;
}

function extractXciptvUrls(text) {
  const xciptvMatches = text.match(/🟠\s*\*?URL\s*XCIPTV\*?:\s*(http[s]?:\/\/[^\s\n*]+)/gi);
  return xciptvMatches ? xciptvMatches.map(match => {
    const urlMatch = match.match(/(http[s]?:\/\/[^\s\n*]+)/i);
    return urlMatch ? urlMatch[1].replace(/\*/g, '') : '';
  }).filter(url => url) : [];
}

function extractM3uLink(text) {
  let m3uMatch = text.match(/🟢\s*\*?Link\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uMatch) {
    m3uMatch = text.match(/M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uMatch ? m3uMatch[1].replace(/\*/g, '') : null;
}

function extractM3uShortLink(text) {
  let m3uShortMatch = text.match(/🟢\s*\*?Link\s*Curto\s*\(M3U\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!m3uShortMatch) {
    m3uShortMatch = text.match(/Link\s*Curto.*M3U[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return m3uShortMatch ? m3uShortMatch[1].replace(/\*/g, '') : null;
}

function extractHlsLink(text) {
  let hlsMatch = text.match(/🟡\s*\*?Link\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsMatch) {
    hlsMatch = text.match(/HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsMatch ? hlsMatch[1].replace(/\*/g, '') : null;
}

function extractHlsShortLink(text) {
  let hlsShortMatch = text.match(/🟡\s*\*?Link\s*Curto\s*\(HLS\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!hlsShortMatch) {
    hlsShortMatch = text.match(/Link\s*Curto.*HLS[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return hlsShortMatch ? hlsShortMatch[1].replace(/\*/g, '') : null;
}

function extractSsiptvLink(text) {
  let ssiptvMatch = text.match(/🔴\s*\*?Link\s*\(SSIPTV\)\*?:\s*(http[s]?:\/\/[^\s\n*]+)/i);
  if (!ssiptvMatch) {
    ssiptvMatch = text.match(/SSIPTV[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
  }
  return ssiptvMatch ? ssiptvMatch[1].replace(/\*/g, '') : null;
}

function extractWebPlayers(text) {
  const webPlayerSection = text.match(/📺\s*\*?WEB\s*PLAYER\*?:\s*((?:http[s]?:\/\/[^\s\n*]+\s*)+)/i);
  return webPlayerSection ?
    webPlayerSection[1].trim().split(/\s+/).filter(url => url.startsWith('http')).map(url => url.replace(/\*/g, '')) : [];
}

function extractIptvStream(text) {
  let iptvStreamMatch = text.match(/📺\s*\*?IPTV\s*STREAM\*?\s*(https?:\/\/[^\s\n*]+)/i);
  if (!iptvStreamMatch) {
    iptvStreamMatch = text.match(/IPTV\s*STREAM[:\s]*(https?:\/\/[^\s\n*]+)/i);
  }
  return iptvStreamMatch ? iptvStreamMatch[1].replace(/\*/g, '') : null;
}

function extractPrice(text) {
  let priceMatch = text.match(/💵\s*\*?Preço\s*do\s*Plano\*?:\s*([^\n\r*]+)/i);
  if (!priceMatch) {
    priceMatch = text.match(/Preço[:\s]*([^\n\r*]+)/i);
  }
  if (!priceMatch) {
    priceMatch = text.match(/Price[:\s]*([^\n\r*]+)/i);
  }
  return priceMatch ? priceMatch[1].trim().replace(/\*/g, '') : null;
}

// PIX Payment function
async function createPix(req) {
  try {
    // Validate required fields
    if (!req.customerName || !req.customerEmail || !req.customerDocument || !req.planType) {
      throw new Error("Missing required fields");
    }

    // Validate document (CPF/CNPJ)
    const document = req.customerDocument.replace(/\D/g, "");
    if (!validateDocument(document)) {
      throw new Error("Invalid document number");
    }

    // Validate amount
    if (req.amount <= 0) {
      throw new Error("Invalid amount");
    }

    // Get Mercado Pago credentials
    const accessToken = getMercadoPagoAccessToken();

    console.log('Using Mercado Pago Access Token (first 20 chars):', accessToken.substring(0, 20) + '...');

    // Create PIX payment using Mercado Pago API directly
    const paymentData = {
      transaction_amount: req.amount,
      description: `SmartV IPTV - Plano ${req.planType}`,
      payment_method_id: 'pix',
      payer: {
        email: req.customerEmail,
        first_name: req.customerName.split(' ')[0],
        last_name: req.customerName.split(' ').slice(1).join(' ') || req.customerName.split(' ')[0],
        identification: {
          type: document.length === 11 ? 'CPF' : 'CNPJ',
          number: document
        }
      },
      external_reference: `SMARTV_${req.planType}_${Date.now()}`,
      notification_url: 'https://smartv.shop/webhook/mercadopago',
      date_of_expiration: new Date(Date.now() + 15 * 60 * 1000).toISOString()
    };

    console.log('Creating Mercado Pago payment with data:', JSON.stringify(paymentData, null, 2));

    const response = await fetch('https://api.mercadopago.com/v1/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'X-Idempotency-Key': `SMARTV_${req.planType}_${Date.now()}`
      },
      body: JSON.stringify(paymentData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Mercado Pago API error:', response.status, errorData);
      throw new Error(`Mercado Pago API error: ${response.status} - ${errorData}`);
    }

    const paymentResponse = await response.json();

    console.log('Mercado Pago payment created successfully:', paymentResponse.id);
    console.log('Payment response:', JSON.stringify(paymentResponse, null, 2));

    if (!paymentResponse.id) {
      throw new Error("Failed to create Mercado Pago payment");
    }

    // Extract PIX information
    const pixInfo = paymentResponse.point_of_interaction?.transaction_data;

    if (!pixInfo?.qr_code || !pixInfo?.qr_code_base64) {
      throw new Error("PIX QR Code not generated");
    }

    return {
      id: paymentResponse.id.toString(),
      qrCode: pixInfo.qr_code,
      qrCodeUrl: `data:image/png;base64,${pixInfo.qr_code_base64}`,
      pixKey: pixInfo.qr_code,
      amount: req.amount,
      transactionAmount: paymentResponse.transaction_amount || req.amount,
      description: paymentResponse.description || `SmartV IPTV - Plano ${req.planType}`,
      expiresAt: paymentResponse.date_of_expiration || new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      status: paymentResponse.status || 'pending'
    };

  } catch (err) {
    console.error('Error in createPix:', err);
    const errorMessage = err instanceof Error ? err.message : String(err);
    throw new Error("Failed to create PIX payment with Mercado Pago: " + errorMessage);
  }
}

// Function to get Mercado Pago Access Token with fallback
function getMercadoPagoAccessToken() {
  // Using the provided test token
  return 'TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072';
}

function validateDocument(document) {
  if (document.length === 11) {
    return validateCPF(document);
  } else if (document.length === 14) {
    return validateCNPJ(document);
  }
  return false;
}

function validateCPF(cpf) {
  if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cpf[i]) * (10 - i);
  }
  let digit1 = (sum * 10) % 11;
  if (digit1 === 10) digit1 = 0;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cpf[i]) * (11 - i);
  }
  let digit2 = (sum * 10) % 11;
  if (digit2 === 10) digit2 = 0;

  return parseInt(cpf[9]) === digit1 && parseInt(cpf[10]) === digit2;
}

function validateCNPJ(cnpj) {
  if (cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj)) return false;

  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cnpj[i]) * weights1[i];
  }
  const digit1 = (sum % 11) < 2 ? 0 : 11 - (sum % 11);

  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cnpj[i]) * weights2[i];
  }
  const digit2 = (sum % 11) < 2 ? 0 : 11 - (sum % 11);

  return parseInt(cnpj[12]) === digit1 && parseInt(cnpj[13]) === digit2;
}

// Create HTTP server
const server = http.createServer((req, res) => {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} - ${method} ${path}`);

  // Handle POST /payment/pix
  if (method === 'POST' && path === '/payment/pix') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const requestData = JSON.parse(body);
        console.log('Received PIX payment request:', requestData);

        const result = await createPix(requestData);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        console.error('Error in PIX payment request:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'Erro interno do servidor: ' + error.message
        }));
      }
    });
    return;
  }

  // Handle POST /test/request
  if (method === 'POST' && path === '/test/request') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const requestData = JSON.parse(body);
        console.log('Received test request:', requestData);

        const result = await requestTest(requestData);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        console.error('Error in test request:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'Erro interno do servidor: ' + error.message
        }));
      }
    });
    return;
  }

  // Handle GET /health
  if (method === 'GET' && path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }));
    return;
  }

  // Handle 404
  console.log(`Unhandled route: ${method} ${path}`);
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Endpoint não encontrado' }));
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Real IPTV API server rodando em http://0.0.0.0:${PORT}`);
  console.log(`📱 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Usando API real: https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7`);
});
