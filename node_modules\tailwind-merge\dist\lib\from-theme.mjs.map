{"version": 3, "file": "from-theme.mjs", "sources": ["../../src/lib/from-theme.ts"], "sourcesContent": ["import { ThemeGetter, ThemeObject } from './types'\n\nexport function fromTheme(key: string): ThemeGetter {\n    const themeGetter = (theme: ThemeObject) => theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n"], "names": ["fromTheme", "key", "themeGetter", "theme", "isThemeGetter"], "mappings": "AAEM,SAAUA,SAAS,CAACC,GAAW,EAAA;AACjC,EAAA,IAAMC,WAAW,GAAG,SAAdA,WAAW,CAAIC,KAAkB,EAAA;AAAA,IAAA,OAAKA,KAAK,CAACF,GAAG,CAAC,IAAI,EAAE,CAAA;AAAA,GAAA,CAAA;EAE5DC,WAAW,CAACE,aAAa,GAAG,IAAa,CAAA;AAEzC,EAAA,OAAOF,WAAW,CAAA;AACtB;;;;"}