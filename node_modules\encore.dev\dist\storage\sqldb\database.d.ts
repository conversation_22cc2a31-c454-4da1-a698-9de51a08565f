/// <reference types="node" />
/// <reference types="node" />
import * as runtime from "../../internal/runtime/mod.js";
import { StringLiteral } from "../../internal/utils/constraints.js";
export interface SQLMigrationsConfig {
    path: string;
    source?: "prisma" | "drizzle";
}
export interface SQLDatabaseConfig {
    migrations?: string | SQLMigrationsConfig;
}
/**
 * Represents a single row from a query result
 */
export type Row = Record<string, any>;
/** Represents a type that can be used in query template literals */
export type Primitive = string | string[] | number | number[] | boolean | boolean[] | Buffer | Date | Date[] | Record<string, any> | Record<string, any>[] | null | undefined;
type SQLQueryExecutor = runtime.SQLConn | runtime.SQLDatabase | runtime.Transaction;
/** Base class containing shared query functionality */
declare class BaseQueryExecutor {
    protected readonly impl: SQLQueryExecutor;
    constructor(impl: SQLQueryExecutor);
    /**
     * query queries the database using a template string, replacing your placeholders in the template
     * with parametrised values without risking SQL injections.
     *
     * It returns an async generator, that allows iterating over the results
     * in a streaming fashion using `for await`.
     *
     * @example
     *
     * const email = "<EMAIL>";
     * const result = database.query`SELECT id FROM users WHERE email=${email}`
     *
     * This produces the query: "SELECT id FROM users WHERE email=$1".
     */
    query<T extends Row = Record<string, any>>(strings: TemplateStringsArray, ...params: Primitive[]): AsyncGenerator<T>;
    /**
     * rawQuery queries the database using a raw parametrised SQL query and parameters.
     *
     * It returns an async generator, that allows iterating over the results
     * in a streaming fashion using `for await`.
     *
     * @example
     * const query = "SELECT id FROM users WHERE email=$1";
     * const email = "<EMAIL>";
     * for await (const row of database.rawQuery(query, email)) {
     *   console.log(row);
     * }
     *
     * @param query - The raw SQL query string.
     * @param params - The parameters to be used in the query.
     * @returns An async generator that yields rows from the query result.
     */
    rawQuery<T extends Row = Record<string, any>>(query: string, ...params: Primitive[]): AsyncGenerator<T>;
    /**
     * queryAll queries the database using a template string, replacing your placeholders in the template
     * with parametrised values without risking SQL injections.
     *
     * It returns an array of all results.
     *
     * @example
     *
     * const email = "<EMAIL>";
     * const result = database.queryAll`SELECT id FROM users WHERE email=${email}`
     *
     * This produces the query: "SELECT id FROM users WHERE email=$1".
     */
    queryAll<T extends Row = Record<string, any>>(strings: TemplateStringsArray, ...params: Primitive[]): Promise<T[]>;
    /**
     * rawQueryAll queries the database using a raw parametrised SQL query and parameters.
     *
     * It returns an array of all results.
     *
     * @example
     *
     * const query = "SELECT id FROM users WHERE email=$1";
     * const email = "<EMAIL>";
     * const rows = await database.rawQueryAll(query, email);
     */
    rawQueryAll<T extends Row = Record<string, any>>(query: string, ...params: Primitive[]): Promise<T[]>;
    /**
     * queryRow is like query but returns only a single row.
     * If the query selects no rows it returns null.
     * Otherwise it returns the first row and discards the rest.
     *
     * @example
     * const email = "<EMAIL>";
     * const result = database.queryRow`SELECT id FROM users WHERE email=${email}`
     */
    queryRow<T extends Row = Record<string, any>>(strings: TemplateStringsArray, ...params: Primitive[]): Promise<T | null>;
    /**
     * rawQueryRow is like rawQuery but returns only a single row.
     * If the query selects no rows, it returns null.
     * Otherwise, it returns the first row and discards the rest.
     *
     * @example
     * const query = "SELECT id FROM users WHERE email=$1";
     * const email = "<EMAIL>";
     * const result = await database.rawQueryRow(query, email);
     * console.log(result);
     *
     * @param query - The raw SQL query string.
     * @param params - The parameters to be used in the query.
     * @returns A promise that resolves to a single row or null.
     */
    rawQueryRow<T extends Row = Record<string, any>>(query: string, ...params: Primitive[]): Promise<T | null>;
    /**
     * exec executes a query without returning any rows.
     *
     * @example
     * const email = "<EMAIL>";
     * const result = database.exec`DELETE FROM users WHERE email=${email}`
     */
    exec(strings: TemplateStringsArray, ...params: Primitive[]): Promise<void>;
    /**
     * rawExec executes a query without returning any rows.
     *
     * @example
     * const query = "DELETE FROM users WHERE email=$1";
     * const email = "<EMAIL>";
     * await database.rawExec(query, email);
     *
     * @param query - The raw SQL query string.
     * @param params - The parameters to be used in the query.
     * @returns A promise that resolves when the query has been executed.
     */
    rawExec(query: string, ...params: Primitive[]): Promise<void>;
}
/**
 * Constructing a new database object will result in Encore provisioning a database with
 * that name and returning this object to represent it.
 *
 * If you want to reference an existing database, use `Database.Named(name)` as it is a
 * compile error to create duplicate databases.
 */
export declare class SQLDatabase extends BaseQueryExecutor {
    protected readonly impl: runtime.SQLDatabase;
    constructor(name: string, cfg?: SQLDatabaseConfig);
    /**
     * Reference an existing database by name, if the database doesn't
     * exist yet, use `new Database(name)` instead.
     */
    static named<name extends string>(name: StringLiteral<name>): SQLDatabase;
    /**
     * Returns the connection string for the database
     */
    get connectionString(): string;
    /**
     * Acquires a database connection from the database pool.
     *
     * When the connection is closed or is garbage-collected, it is returned to the pool.
     * @returns a new connection to the database
     */
    acquire(): Promise<Connection>;
    /**
     * Begins a database transaction.
     *
     * Make sure to always call `rollback` or `commit` to prevent hanging transactions.
     * @returns a transaction object that implements AsycDisposable
     */
    begin(): Promise<Transaction>;
}
export declare class Transaction extends BaseQueryExecutor implements AsyncDisposable {
    protected readonly impl: runtime.Transaction;
    private done;
    constructor(impl: runtime.Transaction);
    /**
     * Commit the transaction.
     */
    commit(): Promise<void>;
    /**
     * Rollback the transaction.
     */
    rollback(): Promise<void>;
    [Symbol.asyncDispose](): Promise<void>;
}
/**
 * Represents a dedicated connection to a database.
 */
export declare class Connection extends BaseQueryExecutor {
    protected readonly impl: runtime.SQLConn;
    constructor(impl: runtime.SQLConn);
    /**
     * Returns the connection to the database pool.
     */
    close(): Promise<void>;
}
export {};
