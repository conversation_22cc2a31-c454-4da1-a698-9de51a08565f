#!/bin/bash

# Script para parar o SmartV em produção
echo "🛑 Parando SmartV..."

# Função para parar um processo usando PID file
stop_process() {
    local service_name=$1
    local pid_file="pids/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "🔄 Parando $service_name (PID: $pid)..."
            kill "$pid"
            sleep 2
            
            # Se ainda estiver rodando, forçar
            if kill -0 "$pid" 2>/dev/null; then
                echo "⚠️  Forçando parada do $service_name..."
                kill -9 "$pid"
            fi
            
            echo "✅ $service_name parado"
        else
            echo "⚠️  $service_name não estava rodando"
        fi
        rm -f "$pid_file"
    else
        echo "⚠️  Arquivo PID do $service_name não encontrado"
    fi
}

# Parar todos os serviços
stop_process "frontend"
stop_process "proxy"
stop_process "backend"

# Matar qualquer processo restante
echo "🔄 Verificando processos restantes..."
pkill -f "node.*simple-server.cjs" 2>/dev/null || true
pkill -f "node.*proxy-server.cjs" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

# Limpar diretório de PIDs
rm -rf pids

echo ""
echo "✅ SmartV parado com sucesso!"
echo "📋 Logs mantidos em logs/ para análise"
