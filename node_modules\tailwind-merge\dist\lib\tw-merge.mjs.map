{"version": 3, "file": "tw-merge.mjs", "sources": ["../../src/lib/tw-merge.ts"], "sourcesContent": ["import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "names": ["twMerge", "createTailwindMerge", "getDefaultConfig"], "mappings": ";;;IAGaA,OAAO,gBAAGC,mBAAmB,CAACC,gBAAgB;;;;"}