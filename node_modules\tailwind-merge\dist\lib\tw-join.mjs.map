{"version": 3, "file": "tw-join.mjs", "sources": ["../../src/lib/tw-join.ts"], "sourcesContent": ["/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nfunction toValue(mix: ClassNameArray | string) {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n"], "names": ["twJoin", "index", "argument", "resolvedValue", "string", "arguments", "length", "toValue", "mix", "k"], "mappings": "AAAA;;;;;;;;AAQG;SAMaA,MAAM,GAAA;EAClB,IAAIC,KAAK,GAAG,CAAC,CAAA;AACb,EAAA,IAAIC,QAAwB,CAAA;AAC5B,EAAA,IAAIC,aAAqB,CAAA;EACzB,IAAIC,MAAM,GAAG,EAAE,CAAA;AAEf,EAAA,OAAOH,KAAK,GAAGI,SAAS,CAACC,MAAM,EAAE;AAC7B,IAAA,IAAKJ,QAAQ,GAAGG,SAAS,CAACJ,KAAK,EAAE,CAAC,EAAG;AACjC,MAAA,IAAKE,aAAa,GAAGI,OAAO,CAACL,QAAQ,CAAC,EAAG;AACrCE,QAAAA,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC,CAAA;AACzBA,QAAAA,MAAM,IAAID,aAAa,CAAA;AAC1B,OAAA;AACJ,KAAA;AACJ,GAAA;AACD,EAAA,OAAOC,MAAM,CAAA;AACjB,CAAA;AAEA,SAASG,OAAO,CAACC,GAA4B,EAAA;AACzC,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;AACzB,IAAA,OAAOA,GAAG,CAAA;AACb,GAAA;AAED,EAAA,IAAIL,aAAqB,CAAA;EACzB,IAAIC,MAAM,GAAG,EAAE,CAAA;AAEf,EAAA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACF,MAAM,EAAEG,CAAC,EAAE,EAAE;AACjC,IAAA,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;MACR,IAAKN,aAAa,GAAGI,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;AAC9DL,QAAAA,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC,CAAA;AACzBA,QAAAA,MAAM,IAAID,aAAa,CAAA;AAC1B,OAAA;AACJ,KAAA;AACJ,GAAA;AAED,EAAA,OAAOC,MAAM,CAAA;AACjB;;;;"}