{"version": 3, "file": "create-tailwind-merge.mjs", "sources": ["../../src/lib/create-tailwind-merge.ts"], "sourcesContent": ["import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { Config } from './types'\n\ntype CreateConfigFirst = () => Config\ntype CreateConfigSubsequent = (config: Config) => Config\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    ...createConfig: [CreateConfigFirst, ...CreateConfigSubsequent[]]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const [firstCreateConfig, ...restCreateConfig] = createConfig\n\n        const config = restCreateConfig.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            firstCreateConfig(),\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n"], "names": ["createTailwindMerge", "createConfig", "configUtils", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "classList", "firstCreateConfig", "restCreateConfig", "config", "reduce", "previousConfig", "createConfigCurrent", "createConfigUtils", "cache", "get", "set", "tailwindMerge", "cachedResult", "result", "mergeClassList", "callTailwindMerge", "twJoin", "apply", "arguments"], "mappings": ";;;;AAUgB,SAAAA,mBAAmB,GACkC;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAA9DC,YAA8D,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAA9DA,YAA8D,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;AAEjE,EAAA,IAAIC,WAAwB,CAAA;AAC5B,EAAA,IAAIC,QAAqC,CAAA;AACzC,EAAA,IAAIC,QAAqC,CAAA;EACzC,IAAIC,cAAc,GAAGC,iBAAiB,CAAA;EAEtC,SAASA,iBAAiB,CAACC,SAAiB,EAAA;IACxC,IAAOC,iBAAiB,GAAyBP,YAAY,CAAA,CAAA,CAAA;AAAhCQ,MAAAA,gBAAgB,GAAIR,YAAY,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IAE7D,IAAMS,MAAM,GAAGD,gBAAgB,CAACE,MAAM,CAClC,UAACC,cAAc,EAAEC,mBAAmB,EAAA;MAAA,OAAKA,mBAAmB,CAACD,cAAc,CAAC,CAAA;KAC5EJ,EAAAA,iBAAiB,EAAE,CACtB,CAAA;AAEDN,IAAAA,WAAW,GAAGY,iBAAiB,CAACJ,MAAM,CAAC,CAAA;AACvCP,IAAAA,QAAQ,GAAGD,WAAW,CAACa,KAAK,CAACC,GAAG,CAAA;AAChCZ,IAAAA,QAAQ,GAAGF,WAAW,CAACa,KAAK,CAACE,GAAG,CAAA;AAChCZ,IAAAA,cAAc,GAAGa,aAAa,CAAA;IAE9B,OAAOA,aAAa,CAACX,SAAS,CAAC,CAAA;AACnC,GAAA;EAEA,SAASW,aAAa,CAACX,SAAiB,EAAA;AACpC,IAAA,IAAMY,YAAY,GAAGhB,QAAQ,CAACI,SAAS,CAAC,CAAA;AAExC,IAAA,IAAIY,YAAY,EAAE;AACd,MAAA,OAAOA,YAAY,CAAA;AACtB,KAAA;AAED,IAAA,IAAMC,MAAM,GAAGC,cAAc,CAACd,SAAS,EAAEL,WAAW,CAAC,CAAA;AACrDE,IAAAA,QAAQ,CAACG,SAAS,EAAEa,MAAM,CAAC,CAAA;AAE3B,IAAA,OAAOA,MAAM,CAAA;AACjB,GAAA;EAEA,OAAO,SAASE,iBAAiB,GAAA;IAC7B,OAAOjB,cAAc,CAACkB,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAgB,CAAC,CAAC,CAAA;GAC9D,CAAA;AACL;;;;"}