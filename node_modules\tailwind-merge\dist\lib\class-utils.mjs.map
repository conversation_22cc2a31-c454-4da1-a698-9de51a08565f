{"version": 3, "file": "class-utils.mjs", "sources": ["../../src/lib/class-utils.ts"], "sourcesContent": ["import { ClassGroup, ClassGroupId, ClassValidator, Config, ThemeGetter, ThemeObject } from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: ClassGroupId\n}\n\ninterface ClassValidatorObject {\n    classGroupId: ClassGroupId\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport function createClassUtils(config: Config) {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers = {} } = config\n\n    function getClassGroupId(className: string) {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    function getConflictingClassGroupIds(classGroupId: ClassGroupId, hasPostfixModifier: boolean) {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nfunction getGroupRecursive(\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): ClassGroupId | undefined {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nfunction getGroupIdForArbitraryProperty(className: string) {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport function createClassMap(config: Config) {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nfunction processClassesRecursively(\n    classGroup: ClassGroup,\n    classPartObject: ClassPartObject,\n    classGroupId: ClassGroupId,\n    theme: ThemeObject,\n) {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nfunction getPart(classPartObject: ClassPartObject, path: string) {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nfunction isThemeGetter(func: ClassValidator | ThemeGetter): func is ThemeGetter {\n    return (func as ThemeGetter).isThemeGetter\n}\n\nfunction getPrefixedClassGroupEntries(\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup]> {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n"], "names": ["CLASS_PART_SEPARATOR", "createClassUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "prefix", "Map", "prefixedClassGroupEntries", "getPrefixedClassGroupEntries", "Object", "entries", "classGroups", "for<PERSON>ach", "classGroup", "processClassesRecursively", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "classGroupEntries", "map", "prefixedClassGroup", "fromEntries", "value"], "mappings": "AAaA,IAAMA,oBAAoB,GAAG,GAAG,CAAA;AAE1B,SAAUC,gBAAgB,CAACC,MAAc,EAAA;AAC3C,EAAA,IAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC,CAAA;AACvC,EAAA,IAAQG,sBAAsB,GAA0CH,MAAM,CAAtEG,sBAAsB;IAAA,qBAA0CH,GAAAA,MAAM,CAA9CI,8BAA8B;IAA9BA,8BAA8B,GAAA,qBAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,qBAAA,CAAA;EAEnE,SAASC,eAAe,CAACC,SAAiB,EAAA;AACtC,IAAA,IAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACV,oBAAoB,CAAC,CAAA;AAExD;AACA,IAAA,IAAIS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;MACjDF,UAAU,CAACG,KAAK,EAAE,CAAA;AACrB,KAAA;IAED,OAAOC,iBAAiB,CAACJ,UAAU,EAAEN,QAAQ,CAAC,IAAIW,8BAA8B,CAACN,SAAS,CAAC,CAAA;AAC/F,GAAA;AAEA,EAAA,SAASO,2BAA2B,CAACC,YAA0B,EAAEC,kBAA2B,EAAA;AACxF,IAAA,IAAMC,SAAS,GAAGb,sBAAsB,CAACW,YAAY,CAAC,IAAI,EAAE,CAAA;AAE5D,IAAA,IAAIC,kBAAkB,IAAIX,8BAA8B,CAACU,YAAY,CAAC,EAAE;AACpE,MAAA,OAAA,EAAA,CAAA,MAAA,CAAWE,SAAS,EAAKZ,8BAA8B,CAACU,YAAY,CAAE,CAAA,CAAA;AACzE,KAAA;AAED,IAAA,OAAOE,SAAS,CAAA;AACpB,GAAA;EAEA,OAAO;AACHX,IAAAA,eAAe,EAAfA,eAAe;AACfQ,IAAAA,2BAA2B,EAA3BA,2BAAAA;GACH,CAAA;AACL,CAAA;AAEA,SAASF,iBAAiB,CACtBJ,UAAoB,EACpBU,eAAgC,EAAA;AAEhC,EAAA,IAAIV,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IACzB,OAAOQ,eAAe,CAACH,YAAY,CAAA;AACtC,GAAA;AAED,EAAA,IAAMI,gBAAgB,GAAGX,UAAU,CAAC,CAAC,CAAE,CAAA;EACvC,IAAMY,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC,CAAA;AAC1E,EAAA,IAAMI,2BAA2B,GAAGH,mBAAmB,GACjDR,iBAAiB,CAACJ,UAAU,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAC,GAC3DK,SAAS,CAAA;AAEf,EAAA,IAAIF,2BAA2B,EAAE;AAC7B,IAAA,OAAOA,2BAA2B,CAAA;AACrC,GAAA;AAED,EAAA,IAAIL,eAAe,CAACQ,UAAU,CAAChB,MAAM,KAAK,CAAC,EAAE;AACzC,IAAA,OAAOe,SAAS,CAAA;AACnB,GAAA;AAED,EAAA,IAAME,SAAS,GAAGnB,UAAU,CAACoB,IAAI,CAAC7B,oBAAoB,CAAC,CAAA;AAEvD,EAAA,OAAOmB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,UAAA,IAAA,EAAA;IAAA,IAAGC,SAAS,QAATA,SAAS,CAAA;IAAA,OAAOA,SAAS,CAACH,SAAS,CAAC,CAAA;AAAA,GAAA,CAAC,EAAEZ,YAAY,CAAA;AACjG,CAAA;AAEA,IAAMgB,sBAAsB,GAAG,YAAY,CAAA;AAE3C,SAASlB,8BAA8B,CAACN,SAAiB,EAAA;AACrD,EAAA,IAAIwB,sBAAsB,CAACC,IAAI,CAACzB,SAAS,CAAC,EAAE;IACxC,IAAM0B,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC3B,SAAS,CAAE,CAAC,CAAC,CAAC,CAAA;AAC7E,IAAA,IAAM4B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C,CAAA;AAED,IAAA,IAAIF,QAAQ,EAAE;AACV;MACA,OAAO,aAAa,GAAGA,QAAQ,CAAA;AAClC,KAAA;AACJ,GAAA;AACL,CAAA;AAEA;;AAEG;AACG,SAAUhC,cAAc,CAACF,MAAc,EAAA;AACzC,EAAA,IAAQqC,KAAK,GAAarC,MAAM,CAAxBqC,KAAK;IAAEC,MAAM,GAAKtC,MAAM,CAAjBsC,MAAM,CAAA;AACrB,EAAA,IAAMrC,QAAQ,GAAoB;IAC9BmB,QAAQ,EAAE,IAAImB,GAAG,EAA2B;AAC5Cd,IAAAA,UAAU,EAAE,EAAA;GACf,CAAA;AAED,EAAA,IAAMe,yBAAyB,GAAGC,4BAA4B,CAC1DC,MAAM,CAACC,OAAO,CAAC3C,MAAM,CAAC4C,WAAW,CAAC,EAClCN,MAAM,CACT,CAAA;EAEDE,yBAAyB,CAACK,OAAO,CAAC,UAA+B,KAAA,EAAA;AAAA,IAAA,IAA7B/B,YAAY,GAAA,KAAA,CAAA,CAAA,CAAA;MAAEgC,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACxDC,yBAAyB,CAACD,UAAU,EAAE7C,QAAQ,EAAEa,YAAY,EAAEuB,KAAK,CAAC,CAAA;AACxE,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOpC,QAAQ,CAAA;AACnB,CAAA;AAEA,SAAS8C,yBAAyB,CAC9BD,UAAsB,EACtB7B,eAAgC,EAChCH,YAA0B,EAC1BuB,KAAkB,EAAA;AAElBS,EAAAA,UAAU,CAACD,OAAO,CAAC,UAACG,eAAe,EAAI;AACnC,IAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;AACrC,MAAA,IAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAG/B,eAAe,GAAGiC,OAAO,CAACjC,eAAe,EAAE+B,eAAe,CAAC,CAAA;MACxFC,qBAAqB,CAACnC,YAAY,GAAGA,YAAY,CAAA;AACjD,MAAA,OAAA;AACH,KAAA;AAED,IAAA,IAAI,OAAOkC,eAAe,KAAK,UAAU,EAAE;AACvC,MAAA,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;QAChCD,yBAAyB,CACrBC,eAAe,CAACX,KAAK,CAAC,EACtBpB,eAAe,EACfH,YAAY,EACZuB,KAAK,CACR,CAAA;AACD,QAAA,OAAA;AACH,OAAA;AAEDpB,MAAAA,eAAe,CAACQ,UAAU,CAAC2B,IAAI,CAAC;AAC5BvB,QAAAA,SAAS,EAAEmB,eAAe;AAC1BlC,QAAAA,YAAY,EAAZA,YAAAA;AACH,OAAA,CAAC,CAAA;AAEF,MAAA,OAAA;AACH,KAAA;IAED4B,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACH,OAAO,CAAC,UAAsB,KAAA,EAAA;AAAA,MAAA,IAApBQ,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA;QAAEP,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACrDC,MAAAA,yBAAyB,CACrBD,UAAU,EACVI,OAAO,CAACjC,eAAe,EAAEoC,GAAG,CAAC,EAC7BvC,YAAY,EACZuB,KAAK,CACR,CAAA;AACL,KAAC,CAAC,CAAA;AACN,GAAC,CAAC,CAAA;AACN,CAAA;AAEA,SAASa,OAAO,CAACjC,eAAgC,EAAEqC,IAAY,EAAA;EAC3D,IAAIC,sBAAsB,GAAGtC,eAAe,CAAA;EAE5CqC,IAAI,CAAC9C,KAAK,CAACV,oBAAoB,CAAC,CAAC+C,OAAO,CAAC,UAACW,QAAQ,EAAI;IAClD,IAAI,CAACD,sBAAsB,CAACnC,QAAQ,CAACqC,GAAG,CAACD,QAAQ,CAAC,EAAE;AAChDD,MAAAA,sBAAsB,CAACnC,QAAQ,CAACsC,GAAG,CAACF,QAAQ,EAAE;QAC1CpC,QAAQ,EAAE,IAAImB,GAAG,EAAE;AACnBd,QAAAA,UAAU,EAAE,EAAA;AACf,OAAA,CAAC,CAAA;AACL,KAAA;IAED8B,sBAAsB,GAAGA,sBAAsB,CAACnC,QAAQ,CAACC,GAAG,CAACmC,QAAQ,CAAE,CAAA;AAC3E,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOD,sBAAsB,CAAA;AACjC,CAAA;AAEA,SAASJ,aAAa,CAACQ,IAAkC,EAAA;EACrD,OAAQA,IAAoB,CAACR,aAAa,CAAA;AAC9C,CAAA;AAEA,SAASV,4BAA4B,CACjCmB,iBAAwE,EACxEtB,MAA0B,EAAA;EAE1B,IAAI,CAACA,MAAM,EAAE;AACT,IAAA,OAAOsB,iBAAiB,CAAA;AAC3B,GAAA;AAED,EAAA,OAAOA,iBAAiB,CAACC,GAAG,CAAC,UAA+B,KAAA,EAAA;AAAA,IAAA,IAA7B/C,YAAY,GAAA,KAAA,CAAA,CAAA,CAAA;MAAEgC,UAAU,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACnD,IAAMgB,kBAAkB,GAAGhB,UAAU,CAACe,GAAG,CAAC,UAACb,eAAe,EAAI;AAC1D,MAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;QACrC,OAAOV,MAAM,GAAGU,eAAe,CAAA;AAClC,OAAA;AAED,MAAA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;AACrC,QAAA,OAAON,MAAM,CAACqB,WAAW,CACrBrB,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACa,GAAG,CAAC,UAAA,KAAA,EAAA;AAAA,UAAA,IAAER,GAAG,GAAA,KAAA,CAAA,CAAA,CAAA;YAAEW,KAAK,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,UAAA,OAAM,CAAC1B,MAAM,GAAGe,GAAG,EAAEW,KAAK,CAAC,CAAA;AAAA,SAAA,CAAC,CAC/E,CAAA;AACJ,OAAA;AAED,MAAA,OAAOhB,eAAe,CAAA;AAC1B,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,CAAClC,YAAY,EAAEgD,kBAAkB,CAAC,CAAA;AAC7C,GAAC,CAAC,CAAA;AACN;;;;"}