/* Responsividade consolidada para toda a aplicação */

/* Containers responsivos */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Extra small devices (phones, 320px and up) */
@media (max-width: 479px) {
  .container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* Card spacing */
  .card {
    margin: 0.25rem !important;
    border-radius: 0.5rem !important;
  }

  /* Button improvements */
  button {
    min-height: 44px !important; /* Touch target size */
    font-size: 0.875rem !important;
  }

  /* Text adjustments */
  .xs\:text-xs {
    font-size: 0.75rem !important;
  }

  .xs\:text-sm {
    font-size: 0.875rem !important;
  }

  /* Padding reduzido */
  .xs\:px-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* Hide elements on very small screens */
  .xs\:hidden {
    display: none !important;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Tipografia responsiva */
.text-responsive-xs {
  font-size: clamp(0.75rem, 2vw, 0.875rem);
}

.text-responsive-sm {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 3vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 3.5vw, 1.25rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 2rem);
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 6vw, 2.5rem);
}

.text-responsive-4xl {
  font-size: clamp(2.25rem, 7vw, 3rem);
}

/* Espaçamentos responsivos */
.spacing-responsive-sm {
  padding: clamp(0.5rem, 2vw, 1rem);
}

.spacing-responsive-md {
  padding: clamp(1rem, 3vw, 1.5rem);
}

.spacing-responsive-lg {
  padding: clamp(1.5rem, 4vw, 2rem);
}

.spacing-responsive-xl {
  padding: clamp(2rem, 5vw, 3rem);
}

/* Cards responsivos */
.card-responsive {
  border-radius: clamp(0.5rem, 2vw, 1rem);
  padding: clamp(1rem, 4vw, 2rem);
}

/* Botões responsivos */
.btn-responsive {
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
  font-size: clamp(0.875rem, 3vw, 1rem);
  border-radius: clamp(0.375rem, 1.5vw, 0.5rem);
}

.btn-responsive-lg {
  padding: clamp(0.75rem, 3vw, 1rem) clamp(1.5rem, 5vw, 2rem);
  font-size: clamp(1rem, 3.5vw, 1.125rem);
  border-radius: clamp(0.5rem, 2vw, 0.75rem);
}

/* Inputs responsivos */
.input-responsive {
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(0.75rem, 3vw, 1rem);
  font-size: clamp(0.875rem, 3vw, 1rem);
  border-radius: clamp(0.375rem, 1.5vw, 0.5rem);
}

/* Grids responsivos */
.grid-responsive-1-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
}

@media (min-width: 768px) {
  .grid-responsive-1-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid-responsive-1-2-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: clamp(1rem, 4vw, 2rem);
}

@media (min-width: 640px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Modais responsivos */
.modal-responsive {
  width: min(95vw, 500px);
  max-height: 90vh;
  margin: clamp(0.5rem, 2vw, 1rem);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow-y: auto;
}

.modal-responsive-wide {
  width: min(95vw, 800px);
  max-height: 90vh;
  margin: clamp(0.5rem, 2vw, 1rem);
  border-radius: clamp(0.75rem, 2vw, 1rem);
  overflow-y: auto;
}

/* Imagens responsivas */
.img-responsive {
  width: 100%;
  height: auto;
  max-width: 100%;
}

.img-responsive-square {
  width: min(200px, 80vw);
  height: min(200px, 80vw);
  object-fit: cover;
}

/* Flexbox responsivo */
.flex-responsive-col-row {
  display: flex;
  flex-direction: column;
  gap: clamp(0.5rem, 2vw, 1rem);
}

@media (min-width: 640px) {
  .flex-responsive-col-row {
    flex-direction: row;
    align-items: center;
  }
}

/* Utilitários de visibilidade */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-mobile {
    display: none;
  }
}

/* Ajustes específicos para telas muito pequenas */
@media (max-width: 360px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .text-responsive-xs {
    font-size: 0.75rem;
  }

  .text-responsive-sm {
    font-size: 0.8125rem;
  }

  .btn-responsive {
    padding: 0.5rem 0.875rem;
    font-size: 0.8125rem;
  }

  .input-responsive {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Breakpoint personalizado para xs (extra small) */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }

  .xs\:hidden {
    display: none !important;
  }

  .xs\:flex-row {
    flex-direction: row !important;
  }

  .xs\:w-auto {
    width: auto !important;
  }

  .xs\:text-sm {
    font-size: 0.875rem !important;
  }

  .xs\:text-base {
    font-size: 1rem !important;
  }

  .xs\:text-lg {
    font-size: 1.125rem !important;
  }

  .xs\:px-3 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  .xs\:py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* Utilitários para xs breakpoint quando não está disponível */
.xs\:hidden {
  display: none;
}

.xs\:inline {
  display: none;
}

@media (min-width: 475px) {
  .xs\:hidden {
    display: none !important;
  }

  .xs\:inline {
    display: inline !important;
  }
}

/* Ajustes para telas grandes */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  .text-responsive-4xl {
    font-size: 3.5rem;
  }

  .spacing-responsive-xl {
    padding: 4rem;
  }
}

/* Estilos específicos para botões responsivos */
.btn-group-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  max-width: 20rem;
  margin: 0 auto;
}

@media (min-width: 475px) {
  .btn-group-responsive {
    flex-direction: row;
    max-width: 32rem;
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .btn-group-responsive {
    max-width: 48rem;
    gap: 1.25rem;
  }
}

/* Estilos para modal de tutoriais */
.tutorials-modal {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.tutorials-modal .swal2-html-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* Melhorias para touch devices */
@media (hover: none) and (pointer: coarse) {
  /* Aumentar área de toque em dispositivos móveis */
  button, .btn, [role="button"] {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 0.875rem 1rem !important;
  }

  /* Melhorar espaçamento em cards */
  .card {
    padding: 1.25rem !important;
  }

  /* Aumentar fonte em dispositivos touch */
  body {
    font-size: 16px !important;
  }

  /* Melhorar badges em touch */
  .badge {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Otimizações para landscape em mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .hero-section {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .modal-responsive {
    max-height: 85vh !important;
    margin: 0.5rem !important;
  }
}

/* Melhorias para tablets */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  .grid-responsive-tablet {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }

  .text-tablet-lg {
    font-size: 1.375rem !important;
  }
}
