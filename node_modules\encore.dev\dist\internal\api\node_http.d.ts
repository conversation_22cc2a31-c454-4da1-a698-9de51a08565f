/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import type { IncomingHttpHeaders, OutgoingHttpHeader, OutgoingHttpHeaders } from "node:http";
import type { Socket } from "node:net";
import * as stream from "node:stream";
import * as runtime from "../runtime/mod.js";
export declare class RawRequest extends stream.Readable {
    complete: boolean;
    trailers: NodeJS.Dict<string>;
    trailersDistinct: NodeJS.Dict<string[]>;
    rawTrailers: string[];
    readonly connection: Socket | null;
    readonly socket: Socket | null;
    private body;
    private req;
    constructor(req: runtime.Request, body: runtime.BodyReader);
    get method(): string;
    _url: string | undefined;
    get url(): string;
    set url(value: string);
    get headers(): IncomingHttpHeaders;
    _headersDistinct: NodeJS.Dict<string[]> | undefined;
    get headersDistinct(): NodeJS.Dict<string[]>;
    _rawHeaders: string[] | undefined;
    get rawHeaders(): string[];
    private _meta;
    private get meta();
    _read(size: number): void;
    setTimeout(msecs: number, callback?: () => void): this;
}
export declare class RawResponse extends stream.Writable {
    readonly req: RawRequest;
    chunkedEncoding: boolean;
    shouldKeepAlive: boolean;
    sendDate: boolean;
    statusCode: number;
    statusMessage: string | undefined;
    finished: boolean;
    headersSent: boolean;
    strictContentLength: boolean;
    readonly connection: Socket | null;
    readonly socket: Socket | null;
    private w;
    private headers;
    constructor(req: RawRequest, w: runtime.ResponseWriter);
    write(chunk: any, callback?: ((error: Error | null | undefined) => void) | undefined): boolean;
    write(chunk: any, encoding: BufferEncoding, callback?: ((error: Error | null | undefined) => void) | undefined): boolean;
    _implicitHeader(): void;
    _writeHeaderIfNeeded(): void;
    _write(chunk: Buffer, _encoding: BufferEncoding, callback: (error?: Error | null) => void): void;
    _writev(chunks: Array<{
        chunk: Buffer;
    }>, callback: (error?: Error | null) => void): void;
    _final(callback: (error?: Error | null | undefined) => void): void;
    setTimeout(msecs: number, callback?: () => void): this;
    setHeader(name: string, value: number | string | string[]): this;
    appendHeader(name: string, value: number | string | string[]): this;
    getHeader(name: string): number | string | string[] | undefined;
    getHeaders(): OutgoingHttpHeaders;
    getHeaderNames(): string[];
    hasHeader(name: string): boolean;
    removeHeader(name: string): void;
    addTrailers(headers: OutgoingHttpHeaders | readonly [string, string][]): void;
    flushHeaders(): void;
    writeHead(statusCode: number, headers?: OutgoingHttpHeaders | OutgoingHttpHeader[]): this;
    writeHead(statusCode: number, statusMessage?: string, headers?: OutgoingHttpHeaders | OutgoingHttpHeader[]): this;
}
