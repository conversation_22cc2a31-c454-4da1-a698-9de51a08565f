#!/bin/bash

# =============================================================================
# SMARTV.SHOP - Script de Inicialização Completo
# =============================================================================
# Este script inicia todos os serviços necessários para https://smartv.shop/
# Frontend (Vite) + Backend (Proxy) + Encore + Gerenciamento de Processos
# =============================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configurações
DOMAIN="smartv.shop"
PROJECT_PATH="/www/wwwroot/smarttv"
FRONTEND_PORT=5173
BACKEND_PORT=3001
ENCORE_PORT=4000
PID_DIR="./pids"
LOG_DIR="./logs"

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Banner
echo -e "${PURPLE}"
cat << 'EOF'
 ____  __  __    _    ____  _______     __  ____  _   _  ___  ____  
/ ___||  \/  |  / \  |  _ \|_   _\ \   / / / ___|| | | |/ _ \|  _ \ 
\___ \| |\/| | / _ \ | |_) | | |  \ \ / /  \___ \| |_| | | | | |_) |
 ___) | |  | |/ ___ \|  _ <  | |   \ V /    ___) |  _  | |_| |  __/ 
|____/|_|  |_/_/   \_\_| \_\ |_|    \_/    |____/|_| |_|\___/|_|    
                                                                    
EOF
echo -e "${NC}"
echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}🚀 Iniciando SMARTV.SHOP - https://smartv.shop/${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Verificar se está no diretório correto
if [[ ! -f "package.json" ]]; then
    error "Execute este script no diretório raiz do projeto (onde está o package.json)"
fi

# Criar diretórios para PIDs e logs
mkdir -p "$PID_DIR" "$LOG_DIR"

# Função para matar processos existentes
kill_existing_processes() {
    log "🔄 Verificando e matando processos existentes..."
    
    # Matar processos por porta
    for port in $FRONTEND_PORT $BACKEND_PORT $ENCORE_PORT; do
        if lsof -ti:$port > /dev/null 2>&1; then
            warning "Matando processo na porta $port"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
            sleep 1
        fi
    done
    
    # Matar processos por PID files
    for pidfile in "$PID_DIR"/*.pid; do
        if [[ -f "$pidfile" ]]; then
            pid=$(cat "$pidfile")
            if kill -0 "$pid" 2>/dev/null; then
                warning "Matando processo PID: $pid"
                kill -TERM "$pid" 2>/dev/null || true
                sleep 2
                kill -KILL "$pid" 2>/dev/null || true
            fi
            rm -f "$pidfile"
        fi
    done
    
    # Matar processos por nome
    pkill -f "vite" 2>/dev/null || true
    pkill -f "proxy-server" 2>/dev/null || true
    pkill -f "encore run" 2>/dev/null || true
    
    sleep 2
    log "✅ Processos existentes finalizados"
}

# Função para verificar se uma porta está livre
wait_for_port() {
    local port=$1
    local timeout=30
    local count=0
    
    while lsof -ti:$port > /dev/null 2>&1; do
        if [[ $count -ge $timeout ]]; then
            error "Timeout aguardando porta $port ficar livre"
        fi
        sleep 1
        ((count++))
    done
}

# Função para verificar se um serviço está rodando
check_service() {
    local port=$1
    local name=$2
    local timeout=30
    local count=0
    
    info "Aguardando $name iniciar na porta $port..."
    
    while ! nc -z localhost $port 2>/dev/null; do
        if [[ $count -ge $timeout ]]; then
            error "$name não iniciou na porta $port dentro do timeout"
        fi
        sleep 1
        ((count++))
        echo -n "."
    done
    echo ""
    log "✅ $name está rodando na porta $port"
}

# Função para iniciar Encore backend
start_encore() {
    log "🎯 Iniciando Encore backend..."
    
    cd api
    nohup encore run > "../$LOG_DIR/encore.log" 2>&1 &
    echo $! > "../$PID_DIR/encore.pid"
    cd ..
    
    check_service $ENCORE_PORT "Encore Backend"
}

# Função para iniciar Proxy Server
start_proxy() {
    log "🔄 Iniciando Proxy Server..."
    
    nohup node proxy-server.cjs > "$LOG_DIR/proxy.log" 2>&1 &
    echo $! > "$PID_DIR/proxy.pid"
    
    check_service $BACKEND_PORT "Proxy Server"
}

# Função para iniciar Frontend
start_frontend() {
    log "⚡ Iniciando Frontend Vite..."
    
    # Verificar se deve usar build ou dev
    if [[ "$1" == "production" ]]; then
        log "📦 Fazendo build para produção..."
        npm run build
        
        log "🌐 Iniciando preview mode..."
        nohup npm run preview -- --host 0.0.0.0 --port $FRONTEND_PORT > "$LOG_DIR/frontend.log" 2>&1 &
    else
        log "🔧 Iniciando modo desenvolvimento..."
        nohup npm run dev -- --host 0.0.0.0 --port $FRONTEND_PORT > "$LOG_DIR/frontend.log" 2>&1 &
    fi
    
    echo $! > "$PID_DIR/frontend.pid"
    
    check_service $FRONTEND_PORT "Frontend Vite"
}

# Função para mostrar status
show_status() {
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}📊 STATUS DOS SERVIÇOS${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    
    # Verificar cada serviço
    services=(
        "$ENCORE_PORT:Encore Backend:encore"
        "$BACKEND_PORT:Proxy Server:proxy"
        "$FRONTEND_PORT:Frontend Vite:frontend"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r port name pidfile <<< "$service"
        
        if nc -z localhost $port 2>/dev/null; then
            echo -e "${GREEN}✅ $name - Rodando na porta $port${NC}"
        else
            echo -e "${RED}❌ $name - Não está rodando na porta $port${NC}"
        fi
    done
    
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}🌐 ACESSO:${NC}"
    echo -e "${YELLOW}   Frontend: https://smartv.shop/${NC}"
    echo -e "${YELLOW}   Backend:  https://smartv.shop/api/${NC}"
    echo -e "${YELLOW}   Local:    http://localhost:$FRONTEND_PORT/${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}📋 COMANDOS ÚTEIS:${NC}"
    echo -e "${YELLOW}   Parar tudo:    ./stop-smartv.sh${NC}"
    echo -e "${YELLOW}   Ver logs:      tail -f logs/*.log${NC}"
    echo -e "${YELLOW}   Status:        ./start-smartv.sh status${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
}

# Função principal
main() {
    case "${1:-start}" in
        "start"|"dev")
            kill_existing_processes
            
            # Aguardar portas ficarem livres
            for port in $ENCORE_PORT $BACKEND_PORT $FRONTEND_PORT; do
                wait_for_port $port
            done
            
            # Iniciar serviços em ordem
            start_encore
            sleep 3
            start_proxy
            sleep 2
            start_frontend "development"
            
            show_status
            
            log "🎉 Todos os serviços iniciados com sucesso!"
            log "🌐 Acesse: https://smartv.shop/"
            ;;
            
        "production"|"prod")
            kill_existing_processes
            
            # Aguardar portas ficarem livres
            for port in $ENCORE_PORT $BACKEND_PORT $FRONTEND_PORT; do
                wait_for_port $port
            done
            
            # Iniciar serviços em ordem
            start_encore
            sleep 3
            start_proxy
            sleep 2
            start_frontend "production"
            
            show_status
            
            log "🎉 Todos os serviços iniciados em modo produção!"
            log "🌐 Acesse: https://smartv.shop/"
            ;;
            
        "status")
            show_status
            ;;
            
        "help"|"-h"|"--help")
            echo -e "${GREEN}Uso: $0 [comando]${NC}"
            echo -e "${YELLOW}Comandos disponíveis:${NC}"
            echo -e "  start, dev     - Iniciar em modo desenvolvimento (padrão)"
            echo -e "  production     - Iniciar em modo produção"
            echo -e "  status         - Mostrar status dos serviços"
            echo -e "  help           - Mostrar esta ajuda"
            ;;
            
        *)
            error "Comando inválido: $1. Use '$0 help' para ver os comandos disponíveis."
            ;;
    esac
}

# Executar função principal
main "$@"
