import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

import Swal from 'sweetalert2';
import backend from '~backend/client';
import type { CreatePixRequest, PixResponse } from '~backend/payment/create-pix';
import type { PaymentStatus } from '~backend/payment/check-payment';
import { showPaymentConfirmedAlert } from '../src/utils/alerts.js';

export default function PaymentPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const selectedPlan = location.state?.selectedPlan;

  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerDocument: '',
    customerPhone: ''
  });

  const [pixData, setPixData] = useState<PixResponse | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [autoFillTriggered, setAutoFillTriggered] = useState(false);


  useEffect(() => {
    if (!selectedPlan) {
      navigate('/');
    }
  }, [selectedPlan, navigate]);

  // Auto-fill e mostrar modal de boas-vindas quando vem da landing page
  useEffect(() => {
    if (selectedPlan && !autoFillTriggered) {
      setAutoFillTriggered(true);

      // Mostrar modal com formulário integrado
      Swal.fire({
        title: `🎉 Ótima Escolha!`,
        html: `
          <div style="text-align: center; color: #333; padding: 10px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
              <h3 style="margin: 0 0 8px 0; font-size: 18px;">📺 ${selectedPlan.name}</h3>
              <p style="margin: 0; font-size: 24px; font-weight: bold;">R$ ${selectedPlan.price.toFixed(2).replace('.', ',')}</p>
            </div>

            <p style="margin: 16px 0; color: #666; font-size: 16px;">
              Preencha seus dados abaixo para gerar o PIX:
            </p>

            <form id="quickPaymentForm" style="text-align: left; margin-top: 20px;">
              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
                <input
                  type="text"
                  id="modalCustomerName"
                  placeholder="Seu nome completo"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
                <input
                  type="email"
                  id="modalCustomerEmail"
                  placeholder="<EMAIL>"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 16px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">CPF:</label>
                <input
                  type="text"
                  id="modalCustomerDocument"
                  placeholder="000.000.000-00"
                  maxlength="14"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>

              <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">WhatsApp:</label>
                <input
                  type="text"
                  id="modalCustomerPhone"
                  placeholder="(11) 99999-9999"
                  maxlength="15"
                  style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; box-sizing: border-box;"
                  required
                />
              </div>
            </form>

            <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
              <p style="margin: 0; color: #495057; font-size: 13px;">
                💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
              </p>
            </div>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: '🚀 Gerar PIX',
        cancelButtonText: '📝 Usar Exemplo',
        confirmButtonColor: '#667eea',
        cancelButtonColor: '#28a745',
        width: 'min(95vw, 550px)',
        background: '#ffffff',
        color: '#333333',
        allowOutsideClick: false,
        customClass: {
          popup: 'swal-welcome-popup'
        },
        didOpen: () => {
          // Adicionar máscaras aos campos
          const cpfInput = document.getElementById('modalCustomerDocument') as HTMLInputElement;
          const phoneInput = document.getElementById('modalCustomerPhone') as HTMLInputElement;

          if (cpfInput) {
            cpfInput.addEventListener('input', (e) => {
              const target = e.target as HTMLInputElement;
              let value = target.value.replace(/\\D/g, '');
              value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
              value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
              value = value.replace(/(\\d{3})(\\d{1,2})$/, '$1-$2');
              target.value = value;
            });
          }

          if (phoneInput) {
            phoneInput.addEventListener('input', (e) => {
              const target = e.target as HTMLInputElement;
              let value = target.value.replace(/\\D/g, '');
              value = value.replace(/^(\\d{2})(\\d)/g, '($1) $2');
              value = value.replace(/(\\d{5})(\\d)/, '$1-$2');
              target.value = value;
            });
          }

          // Focar no primeiro campo
          setTimeout(() => {
            const firstInput = document.getElementById('modalCustomerName') as HTMLInputElement;
            if (firstInput) {
              firstInput.focus();
            }
          }, 100);
        },
        preConfirm: () => {
          // Validar e coletar dados do formulário
          const name = (document.getElementById('modalCustomerName') as HTMLInputElement)?.value.trim();
          const email = (document.getElementById('modalCustomerEmail') as HTMLInputElement)?.value.trim();
          const cpfDocument = (document.getElementById('modalCustomerDocument') as HTMLInputElement)?.value.trim();
          const phone = (document.getElementById('modalCustomerPhone') as HTMLInputElement)?.value.trim();

          if (!name || !email || !cpfDocument || !phone) {
            Swal.showValidationMessage('Por favor, preencha todos os campos');
            return false;
          }

          if (name.length < 3) {
            Swal.showValidationMessage('Nome deve ter pelo menos 3 caracteres');
            return false;
          }

          if (!email.includes('@')) {
            Swal.showValidationMessage('E-mail inválido');
            return false;
          }

          if (cpfDocument.replace(/\\D/g, '').length !== 11) {
            Swal.showValidationMessage('CPF deve ter 11 dígitos');
            return false;
          }

          if (phone.replace(/\\D/g, '').length < 10) {
            Swal.showValidationMessage('Telefone inválido');
            return false;
          }

          return { name, email, document: cpfDocument, phone };
        }
      }).then((result) => {
        if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
          // Usuário escolheu usar dados de exemplo
          setFormData({
            customerName: 'Paulo Antonio Silva',
            customerEmail: '<EMAIL>',
            customerDocument: '123.456.789-00',
            customerPhone: '(11) 99999-9999'
          });

          // Gerar PIX automaticamente após preencher dados de exemplo
          setTimeout(async () => {
            await handleAutoSubmit();
          }, 500);

        } else if (result.isConfirmed && result.value) {
          // Usuário preencheu os dados e confirmou
          const { name, email, document, phone } = result.value;

          setFormData({
            customerName: name,
            customerEmail: email,
            customerDocument: document,
            customerPhone: phone
          });

          // Gerar PIX automaticamente
          setTimeout(async () => {
            await handleAutoSubmit();
          }, 500);
        }
      });
    }
  }, [selectedPlan, autoFillTriggered, toast]);

  useEffect(() => {
    let interval: number;
    
    if (pixData && !paymentStatus?.paid) {
      interval = setInterval(async () => {
        try {
          const status = await backend.payment.checkPayment({ paymentId: pixData.id });
          setPaymentStatus(status);

          if (status.paid) {
            // Show payment confirmation alert
            await showPaymentConfirmedAlert({
              customerName: formData.customerName,
              planType: selectedPlan.name,
              amount: selectedPlan.price,
              paymentId: pixData.id,
              paidAt: status.paidAt || new Date().toISOString()
            });

            toast({
              title: "Pagamento confirmado!",
              description: "Seu acesso foi liberado com sucesso.",
            });

            // Send WhatsApp receipt to customer
            try {
              await backend.whatsapp.sendReceipt({
                paymentId: pixData.id,
                customerName: formData.customerName,
                customerPhone: formData.customerPhone,
                planType: selectedPlan.name,
                amount: selectedPlan.price,
                paidAt: status.paidAt || new Date().toISOString()
              });
            } catch (error) {
              console.error('Failed to send WhatsApp receipt:', error);
            }

            // Send admin notification to +5541995056052
            try {
              await backend.whatsapp.sendAdminNotification({
                paymentId: pixData.id,
                customerName: formData.customerName,
                customerPhone: formData.customerPhone,
                planType: selectedPlan.name,
                amount: selectedPlan.price,
                paidAt: status.paidAt || new Date().toISOString()
              });
              console.log('✅ Admin notification sent successfully');
            } catch (error) {
              console.error('❌ Failed to send admin notification:', error);
            }
          }
        } catch (error) {
          console.error('Error checking payment:', error);
        }
      }, 5000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [pixData, paymentStatus?.paid, formData, selectedPlan, toast]);



  const showPixPopup = async (pixResponse: PixResponse) => {
    const pixCode = pixResponse.qrCode || '';
    const expirationTime = new Date(Date.now() + 15 * 60 * 1000); // 15 minutos
    const amount = selectedPlan?.price || 0;

    await Swal.fire({
      title: '💳 Pagamento PIX',
      html: `
        <div style="text-align: center; color: #333; padding: 0 8px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; border-radius: 12px; margin-bottom: 16px;">
            <h3 style="margin: 0 0 6px 0; font-size: clamp(16px, 4vw, 18px);">💰 Valor: R$ ${amount.toFixed(2).replace('.', ',')}</h3>
            <p style="margin: 0; font-size: clamp(12px, 3vw, 14px); opacity: 0.9;">${selectedPlan?.name || 'Plano Selecionado'}</p>
          </div>
          <p style="margin-bottom: 16px; color: #666; font-size: clamp(14px, 3.5vw, 16px);">Escaneie o QR Code ou copie o código PIX</p>

          <div style="background: white; padding: 16px; border-radius: 12px; margin: 16px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
            ${pixResponse.qrCodeUrl ? `
              <img
                src="data:image/png;base64,${pixResponse.qrCodeUrl}"
                alt="QR Code PIX"
                style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; display: block; border-radius: 8px;"
                onload="console.log('QR Code carregado com sucesso!');"
                onerror="console.error('Erro ao carregar QR Code'); this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              <div style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; background: #f0f0f0; border-radius: 8px; display: none; align-items: center; justify-content: center; border: 2px dashed #ccc;">
                <span style="color: #999; font-size: clamp(12px, 3vw, 14px);">Erro ao carregar QR Code</span>
              </div>
            ` : `
              <div style="width: min(180px, 80vw); height: min(180px, 80vw); margin: 0 auto; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ccc;">
                <span style="color: #999; font-size: clamp(12px, 3vw, 14px);">QR Code não disponível</span>
              </div>
            `}
          </div>

          <div style="margin: 16px 0;">
            <label style="display: block; margin-bottom: 6px; font-weight: bold; color: #333; font-size: clamp(12px, 3vw, 14px);">Código PIX:</label>
            <div style="position: relative;">
              <textarea
                id="pixCodeArea"
                readonly
                style="width: 100%; height: 70px; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-family: monospace; font-size: clamp(10px, 2.5vw, 12px); resize: none; background: #f9f9f9; box-sizing: border-box; padding-right: 50px;"
              >${pixCode}</textarea>
              <button
                onclick="
                  const textarea = document.getElementById('pixCodeArea');
                  const pixCodeToCopy = textarea ? textarea.value.trim() : '';
                  const button = this;

                  console.log('Botão pequeno - Tentando copiar:', pixCodeToCopy);

                  if (!pixCodeToCopy) {
                    alert('Código PIX não encontrado');
                    return;
                  }

                  // Função para mostrar sucesso
                  const showSuccess = () => {
                    button.innerHTML = '✅';
                    button.style.background = '#28a745';
                    button.title = 'Copiado!';
                    setTimeout(() => {
                      button.innerHTML = '📋';
                      button.style.background = '#007bff';
                      button.title = 'Copiar código';
                    }, 2000);
                  };

                  // Função para mostrar erro
                  const showError = () => {
                    textarea.focus();
                    textarea.select();
                    textarea.setSelectionRange(0, 99999);
                    alert('Código selecionado! Use Ctrl+C para copiar');
                  };

                  // Tentar clipboard API primeiro
                  if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(pixCodeToCopy)
                      .then(() => {
                        console.log('Copiado via clipboard API');
                        showSuccess();
                      })
                      .catch((err) => {
                        console.log('Erro clipboard API, tentando fallback:', err);
                        // Fallback
                        try {
                          const tempTextArea = document.createElement('textarea');
                          tempTextArea.value = pixCodeToCopy;
                          tempTextArea.style.position = 'fixed';
                          tempTextArea.style.left = '-9999px';
                          tempTextArea.style.top = '-9999px';
                          document.body.appendChild(tempTextArea);
                          tempTextArea.focus();
                          tempTextArea.select();
                          tempTextArea.setSelectionRange(0, 99999);
                          const successful = document.execCommand('copy');
                          document.body.removeChild(tempTextArea);

                          if (successful) {
                            console.log('Copiado via execCommand');
                            showSuccess();
                          } else {
                            console.log('execCommand falhou');
                            showError();
                          }
                        } catch (e) {
                          console.log('Erro no fallback:', e);
                          showError();
                        }
                      });
                  } else {
                    // Fallback direto para navegadores antigos
                    try {
                      const tempTextArea = document.createElement('textarea');
                      tempTextArea.value = pixCodeToCopy;
                      tempTextArea.style.position = 'fixed';
                      tempTextArea.style.left = '-9999px';
                      tempTextArea.style.top = '-9999px';
                      document.body.appendChild(tempTextArea);
                      tempTextArea.focus();
                      tempTextArea.select();
                      tempTextArea.setSelectionRange(0, 99999);
                      const successful = document.execCommand('copy');
                      document.body.removeChild(tempTextArea);

                      if (successful) {
                        console.log('Copiado via execCommand (fallback direto)');
                        showSuccess();
                      } else {
                        console.log('execCommand falhou (fallback direto)');
                        showError();
                      }
                    } catch (e) {
                      console.log('Erro no fallback direto:', e);
                      showError();
                    }
                  }
                "
                title="Copiar código"
                style="position: absolute; right: 5px; top: 5px; background: #007bff; color: white; border: none; padding: 8px 10px; border-radius: 4px; font-size: 14px; cursor: pointer; font-weight: 500; transition: all 0.2s; z-index: 10;">
                📋
              </button>
            </div>
          </div>

          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; border-radius: 12px; margin: 16px 0;">
            <h4 style="margin: 0 0 10px 0; font-size: clamp(14px, 3.5vw, 16px);">Como pagar:</h4>
            <ol style="text-align: left; margin: 0; padding-left: 16px; font-size: clamp(12px, 3vw, 14px); line-height: 1.5;">
              <li style="margin-bottom: 4px;">Abra o app do seu banco</li>
              <li style="margin-bottom: 4px;">Escolha a opção PIX</li>
              <li style="margin-bottom: 4px;">Escaneie o QR Code ou cole o código</li>
              <li>Confirme o pagamento</li>
            </ol>
          </div>

          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 10px 12px; margin: 12px 0;">
            <p style="margin: 0; color: #856404; font-size: clamp(12px, 3vw, 14px); line-height: 1.4;">
              ⏰ <strong>PIX válido por 15 minutos</strong><br>
              Expira em: ${expirationTime.toLocaleTimeString('pt-BR')}<br>
              <span id="countdown-timer" style="font-weight: bold; color: #d63384; font-size: clamp(11px, 2.8vw, 13px);"></span>
            </p>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: '📋 Copiar Código',
      cancelButtonText: '✅ Fechar',
      allowOutsideClick: false,
      preConfirm: async () => {
        // Pegar o código PIX do textarea para garantir que está correto
        const textarea = document.getElementById('pixCodeArea') as HTMLTextAreaElement;
        const pixCodeToCopy = textarea ? textarea.value.trim() : `${pixCode}`.trim();

        console.log('Botão principal - Tentando copiar código PIX:', pixCodeToCopy);
        console.log('Clipboard disponível:', !!navigator.clipboard);
        console.log('Contexto seguro:', window.isSecureContext);
        console.log('Tamanho do código:', pixCodeToCopy.length);

        if (!pixCodeToCopy) {
          console.error('Código PIX vazio');
          Swal.fire({
            title: 'Erro',
            text: 'Código PIX não encontrado',
            icon: 'error',
            confirmButtonText: 'OK'
          });
          return false;
        }

        try {
          let copySuccess = false;

          // Tentar método moderno primeiro
          if (navigator.clipboard && window.isSecureContext) {
            console.log('Usando navigator.clipboard');
            try {
              await navigator.clipboard.writeText(pixCodeToCopy);
              console.log('✅ Código copiado com sucesso via clipboard API');
              copySuccess = true;
            } catch (clipboardError) {
              console.log('❌ Erro no clipboard API:', clipboardError);
              copySuccess = false;
            }
          }

          // Se clipboard API falhou ou não está disponível, usar fallback
          if (!copySuccess) {
            console.log('Usando fallback document.execCommand');
            try {
              const textArea = document.createElement('textarea');
              textArea.value = pixCodeToCopy;
              textArea.style.position = 'fixed';
              textArea.style.left = '-9999px';
              textArea.style.top = '-9999px';
              textArea.style.opacity = '0';
              textArea.style.pointerEvents = 'none';
              textArea.setAttribute('readonly', '');
              document.body.appendChild(textArea);

              // Focar e selecionar
              textArea.focus();
              textArea.select();
              textArea.setSelectionRange(0, 99999);

              // Tentar copiar
              const successful = document.execCommand('copy');
              document.body.removeChild(textArea);

              console.log('Resultado do execCommand:', successful);
              copySuccess = successful;
            } catch (execError) {
              console.log('❌ Erro no execCommand:', execError);
              copySuccess = false;
            }
          }

          if (copySuccess) {
            // Mostrar toast de sucesso
            Swal.fire({
              title: '✅ Código Copiado!',
              text: 'Cole no seu app do banco para pagar',
              timer: 2500,
              showConfirmButton: false,
              toast: true,
              position: 'top-end',
              icon: 'success',
              background: '#d4edda',
              color: '#155724',
              timerProgressBar: true
            });
          } else {
            // Se tudo falhou, selecionar o texto para cópia manual
            if (textarea) {
              textarea.focus();
              textarea.select();
              textarea.setSelectionRange(0, 99999);
            }

            Swal.fire({
              title: '📋 Código Selecionado!',
              text: 'Use Ctrl+C (ou Cmd+C no Mac) para copiar',
              icon: 'info',
              confirmButtonText: 'OK',
              timer: 5000,
              timerProgressBar: true
            });
          }

          return false; // Não fechar o modal
        } catch (error) {
          console.error('❌ Erro geral ao copiar:', error);

          // Última tentativa: selecionar o texto no textarea
          if (textarea) {
            textarea.focus();
            textarea.select();
            textarea.setSelectionRange(0, 99999);
          }

          Swal.fire({
            title: '⚠️ Erro ao Copiar',
            text: 'Código selecionado. Use Ctrl+C para copiar manualmente',
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          return false;
        }
      },
      background: '#ffffff',
      color: '#333333',
      confirmButtonColor: '#667eea',
      cancelButtonColor: '#6c757d',
      width: 'min(98vw, 500px)',
      padding: '16px',
      customClass: {
        popup: 'swal-dark-theme swal-wide',
        title: 'swal-title',
        htmlContainer: 'swal-html-container',
        confirmButton: 'swal-button',
        cancelButton: 'swal-button swal-button--cancel'
      },
      didOpen: () => {

        // Timer countdown
        const countdownElement = document.getElementById('countdown-timer');
        if (countdownElement) {
          const expirationTimestamp = expirationTime.getTime();
          const updateCountdown = () => {
            const now = new Date().getTime();
            const timeLeft = expirationTimestamp - now;

            if (timeLeft > 0) {
              const minutes = Math.floor(timeLeft / (1000 * 60));
              const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
              countdownElement.textContent = 'Tempo restante: ' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            } else {
              countdownElement.textContent = 'PIX expirado!';
              countdownElement.style.color = '#dc3545';
            }
          };

          updateCountdown();
          const interval = setInterval(updateCountdown, 1000);

          // Limpar interval quando o popup fechar
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'childList') {
                const popup = document.querySelector('.swal2-container');
                if (!popup) {
                  clearInterval(interval);
                  observer.disconnect();
                }
              }
            });
          });
          observer.observe(document.body, { childList: true });
        }
      }
    });

    // O botão de copiar agora está integrado no preConfirm do modal
  };

  const handleAutoSubmit = async () => {
    if (!selectedPlan) return;

    setLoading(true);

    try {
      const request: CreatePixRequest = {
        customerName: formData.customerName,
        customerEmail: formData.customerEmail,
        customerDocument: formData.customerDocument.replace(/\D/g, ''),
        customerPhone: formData.customerPhone.replace(/\D/g, ''),
        planType: selectedPlan.name,
        amount: selectedPlan.price
      };

      const response = await backend.payment.createPix(request);
      setPixData(response);

      // Mostrar PIX em popup SweetAlert2
      await showPixPopup(response);
    } catch (error) {
      console.error('Error creating PIX:', error);
      toast({
        title: "Erro ao gerar PIX",
        description: "Tente novamente em alguns instantes.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };





  if (!selectedPlan) {
    return null;
  }

  if (paymentStatus?.paid) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-white/5 backdrop-blur-sm border-white/10">
          <CardHeader className="text-center p-4 md:p-6">
            <div className="mx-auto mb-3 md:mb-4 p-3 md:p-4 bg-green-500/20 rounded-full w-fit">
              <CheckCircle className="h-8 w-8 md:h-12 md:w-12 text-green-400" />
            </div>
            <CardTitle className="text-xl md:text-2xl text-white">Pagamento Confirmado!</CardTitle>
            <CardDescription className="text-gray-300 text-sm md:text-base">
              Seu acesso foi liberado com sucesso
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 p-4 md:p-6 pt-0">
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 md:p-4">
              <p className="text-green-300 text-xs md:text-sm leading-relaxed">
                ✅ Plano: {selectedPlan.name}<br/>
                💰 Valor: R$ {selectedPlan.price.toFixed(2).replace('.', ',')}<br/>
                📱 Comprovante enviado via WhatsApp
              </p>
            </div>
            <Button 
              onClick={() => navigate('/')}
              className="w-full bg-purple-600 hover:bg-purple-700 text-sm md:text-base py-2 md:py-3"
            >
              Voltar ao Início
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <style>{`
        .swal-welcome-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-welcome-popup .swal2-title {
          font-size: 24px !important;
          margin-bottom: 10px !important;
        }
        .swal-welcome-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
          max-height: 70vh !important;
          overflow-y: auto !important;
        }
        .swal-welcome-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
          flex-direction: row !important;
        }
        .swal-welcome-popup .swal2-confirm,
        .swal-welcome-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        .swal-welcome-popup input {
          transition: border-color 0.2s ease !important;
        }
        .swal-welcome-popup input:focus {
          border-color: #667eea !important;
          outline: none !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }
        .swal-welcome-popup label {
          user-select: none !important;
        }
        @media (max-width: 640px) {
          .swal-welcome-popup .swal2-actions {
            flex-direction: column !important;
          }
          .swal-welcome-popup .swal2-confirm,
          .swal-welcome-popup .swal2-cancel {
            width: 100% !important;
            margin: 0 !important;
          }
        }
      `}</style>

      <div className="min-h-screen p-2 sm:p-4">
        <div className="container mx-auto max-w-7xl">
        <div className="mb-3 sm:mb-4 md:mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-white hover:text-purple-400 text-xs sm:text-sm md:text-base p-1.5 sm:p-2 md:p-3"
          >
            <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 md:mr-2" />
            Voltar
          </Button>
        </div>

        {/* Aguardando dados do modal */}
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="bg-white/5 backdrop-blur-sm border-white/10 max-w-md w-full">
            <CardContent className="p-6 text-center">
              <div className="mb-6">
                <div className="mx-auto mb-4 p-4 bg-purple-500/20 rounded-full w-fit">
                  <CheckCircle className="h-8 w-8 text-purple-400" />
                </div>
                <h2 className="text-xl font-bold text-white mb-2">Plano Selecionado</h2>
                <p className="text-gray-300 text-sm">
                  Você escolheu o plano <strong>{selectedPlan.name}</strong>
                </p>
              </div>

              <div className="bg-white/10 rounded-lg p-4 mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-300">Plano:</span>
                  <span className="text-white font-medium">{selectedPlan.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Valor:</span>
                  <span className="text-purple-400 font-bold text-lg">
                    R$ {selectedPlan.price.toFixed(2).replace('.', ',')}
                  </span>
                </div>
              </div>

              <p className="text-gray-400 text-sm">
                O modal de pagamento será aberto automaticamente para você preencher seus dados e gerar o PIX.
              </p>
            </CardContent>
          </Card>
        </div>
        </div>
      </div>
    </>
  );
}
