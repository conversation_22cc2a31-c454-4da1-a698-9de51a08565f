{"version": 3, "file": "default-config.mjs", "sources": ["../../src/lib/default-config.ts"], "sourcesContent": ["import { fromTheme } from './from-theme'\nimport { Config } from './types'\nimport {\n    isAny,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryUrl,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport function getDefaultConfig() {\n    const colors = fromTheme('colors')\n    const spacing = fromTheme('spacing')\n    const blur = fromTheme('blur')\n    const brightness = fromTheme('brightness')\n    const borderColor = fromTheme('borderColor')\n    const borderRadius = fromTheme('borderRadius')\n    const borderSpacing = fromTheme('borderSpacing')\n    const borderWidth = fromTheme('borderWidth')\n    const contrast = fromTheme('contrast')\n    const grayscale = fromTheme('grayscale')\n    const hueRotate = fromTheme('hueRotate')\n    const invert = fromTheme('invert')\n    const gap = fromTheme('gap')\n    const gradientColorStops = fromTheme('gradientColorStops')\n    const gradientColorStopPositions = fromTheme('gradientColorStopPositions')\n    const inset = fromTheme('inset')\n    const margin = fromTheme('margin')\n    const opacity = fromTheme('opacity')\n    const padding = fromTheme('padding')\n    const saturate = fromTheme('saturate')\n    const scale = fromTheme('scale')\n    const sepia = fromTheme('sepia')\n    const skew = fromTheme('skew')\n    const space = fromTheme('space')\n    const translate = fromTheme('translate')\n\n    const getOverscroll = () => ['auto', 'contain', 'none'] as const\n    const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing] as const\n    const getSpacingWithArbitrary = () => [isArbitraryValue, spacing] as const\n    const getLengthWithEmpty = () => ['', isLength] as const\n    const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue] as const\n    const getPositions = () =>\n        [\n            'bottom',\n            'center',\n            'left',\n            'left-bottom',\n            'left-top',\n            'right',\n            'right-bottom',\n            'right-top',\n            'top',\n        ] as const\n    const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'] as const\n    const getBlendModes = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n            'plus-lighter',\n        ] as const\n    const getAlign = () =>\n        ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'] as const\n    const getZeroAndEmpty = () => ['', '0', isArbitraryValue] as const\n    const getBreaks = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const getNumber = () => [isNumber, isArbitraryNumber]\n    const getNumberAndArbitrary = () => [isNumber, isArbitraryValue]\n\n    return {\n        cacheSize: 500,\n        theme: {\n            colors: [isAny],\n            spacing: [isLength],\n            blur: ['none', '', isTshirtSize, isArbitraryValue],\n            brightness: getNumber(),\n            borderColor: [colors],\n            borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmpty(),\n            contrast: getNumber(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [colors],\n            gradientColorStopPositions: [isPercent, isArbitraryLength],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumber(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumber(),\n            scale: getNumber(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary(),\n        },\n        classGroups: {\n            // Layout\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [{ aspect: ['auto', 'square', 'video', isArbitraryValue] }],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [{ columns: [isTshirtSize] }],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': getBreaks() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': getBreaks() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: [...getPositions(), isArbitraryValue] }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: getOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': getOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': getOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: getOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': getOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': getOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: [inset] }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': [inset] }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': [inset] }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: [inset] }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: [inset] }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: [inset] }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: [inset] }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: [inset] }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: [inset] }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: ['auto', isInteger] }],\n            // Flexbox and Grid\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [{ basis: getSpacingWithAutoAndArbitrary() }],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['wrap', 'wrap-reverse', 'nowrap'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: ['1', 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: getZeroAndEmpty() }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: getZeroAndEmpty() }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [{ order: ['first', 'last', 'none', isInteger] }],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': [isAny] }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: ['auto', { span: ['full', isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': [isAny] }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: ['auto', { span: [isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: [gap] }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': [gap] }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': [gap] }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: ['normal', ...getAlign()] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': ['start', 'end', 'center', 'stretch'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...getAlign(), 'baseline'] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [{ self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline'] }],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': [...getAlign(), 'baseline'] }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: [padding] }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: [padding] }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: [padding] }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: [padding] }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: [padding] }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: [padding] }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: [padding] }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: [padding] }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: [padding] }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: [margin] }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: [margin] }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: [margin] }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: [margin] }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: [margin] }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: [margin] }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: [margin] }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: [margin] }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: [margin] }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x': [{ 'space-x': [space] }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y': [{ 'space-y': [space] }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y-reverse': ['space-y-reverse'],\n            // Sizing\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: ['auto', 'min', 'max', 'fit', isArbitraryValue, spacing] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [{ 'min-w': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        '0',\n                        'none',\n                        'full',\n                        'min',\n                        'max',\n                        'fit',\n                        'prose',\n                        { screen: [isTshirtSize] },\n                        isTshirtSize,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit'] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit'] }],\n            // Typography\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [{ text: ['base', isTshirtSize, isArbitraryLength] }],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [\n                {\n                    font: [\n                        'thin',\n                        'extralight',\n                        'light',\n                        'normal',\n                        'medium',\n                        'semibold',\n                        'bold',\n                        'extrabold',\n                        'black',\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isAny] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [\n                {\n                    tracking: [\n                        'tighter',\n                        'tight',\n                        'normal',\n                        'wide',\n                        'wider',\n                        'widest',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [{ 'line-clamp': ['none', isNumber, isArbitraryNumber] }],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        'none',\n                        'tight',\n                        'snug',\n                        'normal',\n                        'relaxed',\n                        'loose',\n                        isArbitraryValue,\n                        isLength,\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryValue] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [{ list: ['none', 'disc', 'decimal', isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: [colors] }],\n            /**\n             * Placeholder Opacity\n             * @see https://tailwindcss.com/docs/placeholder-opacity\n             */\n            'placeholder-opacity': [{ 'placeholder-opacity': [opacity] }],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: [colors] }],\n            /**\n             * Text Opacity\n             * @see https://tailwindcss.com/docs/text-opacity\n             */\n            'text-opacity': [{ 'text-opacity': [opacity] }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...getLineStyles(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [{ decoration: ['auto', 'from-font', isLength] }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [{ 'underline-offset': ['auto', isArbitraryValue, isLength] }],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: [colors] }],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: getSpacingWithArbitrary() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryValue] }],\n            // Backgrounds\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Opacity\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/background-opacity\n             */\n            'bg-opacity': [{ 'bg-opacity': [opacity] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: [...getPositions(), isArbitraryPosition] }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: ['no-repeat', { repeat: ['', 'x', 'y', 'round', 'space'] }] }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: ['auto', 'cover', 'contain', isArbitrarySize] }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        { 'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                        isArbitraryUrl,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: [colors] }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: [gradientColorStops] }],\n            // Borders\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: [borderRadius] }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': [borderRadius] }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': [borderRadius] }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': [borderRadius] }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': [borderRadius] }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': [borderRadius] }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': [borderRadius] }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': [borderRadius] }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': [borderRadius] }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': [borderRadius] }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': [borderRadius] }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': [borderRadius] }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': [borderRadius] }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': [borderRadius] }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': [borderRadius] }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: [borderWidth] }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': [borderWidth] }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': [borderWidth] }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': [borderWidth] }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': [borderWidth] }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': [borderWidth] }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': [borderWidth] }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': [borderWidth] }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': [borderWidth] }],\n            /**\n             * Border Opacity\n             * @see https://tailwindcss.com/docs/border-opacity\n             */\n            'border-opacity': [{ 'border-opacity': [opacity] }],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...getLineStyles(), 'hidden'] }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x': [{ 'divide-x': [borderWidth] }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y': [{ 'divide-y': [borderWidth] }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Divide Opacity\n             * @see https://tailwindcss.com/docs/divide-opacity\n             */\n            'divide-opacity': [{ 'divide-opacity': [opacity] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/divide-style\n             */\n            'divide-style': [{ divide: getLineStyles() }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: [borderColor] }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': [borderColor] }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': [borderColor] }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': [borderColor] }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': [borderColor] }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': [borderColor] }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': [borderColor] }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: [borderColor] }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: ['', ...getLineStyles()] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [{ 'outline-offset': [isArbitraryValue, isLength] }],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [{ outline: [isLength] }],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: [colors] }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w': [{ ring: getLengthWithEmpty() }],\n            /**\n             * Ring Width Inset\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/ring-color\n             */\n            'ring-color': [{ ring: [colors] }],\n            /**\n             * Ring Opacity\n             * @see https://tailwindcss.com/docs/ring-opacity\n             */\n            'ring-opacity': [{ 'ring-opacity': [opacity] }],\n            /**\n             * Ring Offset Width\n             * @see https://tailwindcss.com/docs/ring-offset-width\n             */\n            'ring-offset-w': [{ 'ring-offset': [isLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://tailwindcss.com/docs/ring-offset-color\n             */\n            'ring-offset-color': [{ 'ring-offset': [colors] }],\n            // Effects\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [{ shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow] }],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow-color\n             */\n            'shadow-color': [{ shadow: [isAny] }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [opacity] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': getBlendModes() }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': getBlendModes() }],\n            // Filters\n            /**\n             * Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [{ filter: ['', 'none'] }],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: [blur] }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [brightness] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [contrast] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [{ 'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue] }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: [grayscale] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [hueRotate] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: [invert] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [saturate] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: [sepia] }],\n            /**\n             * Backdrop Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [{ 'backdrop-filter': ['', 'none'] }],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': [blur] }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [{ 'backdrop-brightness': [brightness] }],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [{ 'backdrop-contrast': [contrast] }],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [{ 'backdrop-grayscale': [grayscale] }],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [{ 'backdrop-hue-rotate': [hueRotate] }],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [{ 'backdrop-invert': [invert] }],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [{ 'backdrop-opacity': [opacity] }],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [{ 'backdrop-saturate': [saturate] }],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [{ 'backdrop-sepia': [sepia] }],\n            // Tables\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': [borderSpacing] }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': [borderSpacing] }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': [borderSpacing] }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n            // Transitions and Animation\n            /**\n             * Tranisition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        'none',\n                        'all',\n                        '',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: getNumberAndArbitrary() }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [{ ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue] }],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: getNumberAndArbitrary() }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue] }],\n            // Transforms\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [{ transform: ['', 'gpu', 'none'] }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: [scale] }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': [scale] }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': [scale] }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: [isInteger, isArbitraryValue] }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': [translate] }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': [translate] }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': [skew] }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': [skew] }],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [\n                {\n                    origin: [\n                        'center',\n                        'top',\n                        'top-right',\n                        'right',\n                        'bottom-right',\n                        'bottom',\n                        'bottom-left',\n                        'left',\n                        'top-left',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            // Interactivity\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: ['auto', colors] }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: ['appearance-none'],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: [colors] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['none', 'auto'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', 'y', 'x', ''] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'pinch-zoom',\n                        'manipulation',\n                        { pan: ['x', 'left', 'right', 'y', 'up', 'down'] },\n                    ],\n                },\n            ],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                { 'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue] },\n            ],\n            // SVG\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: [colors, 'none'] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [{ stroke: [isLength, isArbitraryNumber] }],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: [colors, 'none'] }],\n            // Accessibility\n            /**\n             * Screen Readers\n             * @see https://tailwindcss.com/docs/screen-readers\n             */\n            sr: ['sr-only', 'not-sr-only'],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n    } as const satisfies Config\n}\n"], "names": ["getDefaultConfig", "colors", "fromTheme", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getOverscroll", "getOverflow", "getSpacingWithAutoAndArbitrary", "isArbitraryValue", "getSpacingWithArbitrary", "getLengthWithEmpty", "<PERSON><PERSON><PERSON><PERSON>", "getNumberWithAutoAndArbitrary", "isNumber", "getPositions", "getLineStyles", "getBlendModes", "getAlign", "getZeroAndEmpty", "getBreaks", "getNumber", "isArbitraryNumber", "getNumberAndArbitrary", "cacheSize", "theme", "isAny", "isTshirtSize", "isPercent", "isArbitraryLength", "classGroups", "aspect", "container", "columns", "box", "display", "clear", "isolation", "object", "overflow", "overscroll", "position", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "isInteger", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "indent", "align", "whitespace", "hyphens", "bg", "isArbitraryPosition", "repeat", "isArbitrarySize", "isArbitraryUrl", "from", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "isArbitraryShadow", "filter", "table", "caption", "transition", "duration", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "pan", "select", "fill", "stroke", "sr", "conflictingClassGroups", "conflictingClassGroupModifiers"], "mappings": ";;;SAkBgBA,gBAAgB,GAAA;AAC5B,EAAA,IAAMC,MAAM,GAAGC,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAMC,OAAO,GAAGD,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAME,IAAI,GAAGF,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9B,EAAA,IAAMG,UAAU,GAAGH,SAAS,CAAC,YAAY,CAAC,CAAA;AAC1C,EAAA,IAAMI,WAAW,GAAGJ,SAAS,CAAC,aAAa,CAAC,CAAA;AAC5C,EAAA,IAAMK,YAAY,GAAGL,SAAS,CAAC,cAAc,CAAC,CAAA;AAC9C,EAAA,IAAMM,aAAa,GAAGN,SAAS,CAAC,eAAe,CAAC,CAAA;AAChD,EAAA,IAAMO,WAAW,GAAGP,SAAS,CAAC,aAAa,CAAC,CAAA;AAC5C,EAAA,IAAMQ,QAAQ,GAAGR,SAAS,CAAC,UAAU,CAAC,CAAA;AACtC,EAAA,IAAMS,SAAS,GAAGT,SAAS,CAAC,WAAW,CAAC,CAAA;AACxC,EAAA,IAAMU,SAAS,GAAGV,SAAS,CAAC,WAAW,CAAC,CAAA;AACxC,EAAA,IAAMW,MAAM,GAAGX,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAMY,GAAG,GAAGZ,SAAS,CAAC,KAAK,CAAC,CAAA;AAC5B,EAAA,IAAMa,kBAAkB,GAAGb,SAAS,CAAC,oBAAoB,CAAC,CAAA;AAC1D,EAAA,IAAMc,0BAA0B,GAAGd,SAAS,CAAC,4BAA4B,CAAC,CAAA;AAC1E,EAAA,IAAMe,KAAK,GAAGf,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMgB,MAAM,GAAGhB,SAAS,CAAC,QAAQ,CAAC,CAAA;AAClC,EAAA,IAAMiB,OAAO,GAAGjB,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAMkB,OAAO,GAAGlB,SAAS,CAAC,SAAS,CAAC,CAAA;AACpC,EAAA,IAAMmB,QAAQ,GAAGnB,SAAS,CAAC,UAAU,CAAC,CAAA;AACtC,EAAA,IAAMoB,KAAK,GAAGpB,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMqB,KAAK,GAAGrB,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMsB,IAAI,GAAGtB,SAAS,CAAC,MAAM,CAAC,CAAA;AAC9B,EAAA,IAAMuB,KAAK,GAAGvB,SAAS,CAAC,OAAO,CAAC,CAAA;AAChC,EAAA,IAAMwB,SAAS,GAAGxB,SAAS,CAAC,WAAW,CAAC,CAAA;EAExC,IAAMyB,aAAa,GAAG,SAAhBA,aAAa,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAU,CAAA;AAAA,GAAA,CAAA;EAChE,IAAMC,WAAW,GAAG,SAAdA,WAAW,GAAA;IAAA,OAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EAClF,IAAMC,8BAA8B,GAAG,SAAjCA,8BAA8B,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAEC,gBAAgB,EAAE3B,OAAO,CAAU,CAAA;AAAA,GAAA,CAAA;EACzF,IAAM4B,uBAAuB,GAAG,SAA1BA,uBAAuB,GAAA;AAAA,IAAA,OAAS,CAACD,gBAAgB,EAAE3B,OAAO,CAAU,CAAA;AAAA,GAAA,CAAA;EAC1E,IAAM6B,kBAAkB,GAAG,SAArBA,kBAAkB,GAAA;AAAA,IAAA,OAAS,CAAC,EAAE,EAAEC,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EACxD,IAAMC,6BAA6B,GAAG,SAAhCA,6BAA6B,GAAA;AAAA,IAAA,OAAS,CAAC,MAAM,EAAEC,QAAQ,EAAEL,gBAAgB,CAAU,CAAA;AAAA,GAAA,CAAA;EACzF,IAAMM,YAAY,GAAG,SAAfA,YAAY,GAAA;AAAA,IAAA,OACd,CACI,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,aAAa,EACb,UAAU,EACV,OAAO,EACP,cAAc,EACd,WAAW,EACX,KAAK,CACC,CAAA;AAAA,GAAA,CAAA;EACd,IAAMC,aAAa,GAAG,SAAhBA,aAAa,GAAA;IAAA,OAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAU,CAAA;AAAA,GAAA,CAAA;EACpF,IAAMC,aAAa,GAAG,SAAhBA,aAAa,GAAA;AAAA,IAAA,OACf,CACI,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,KAAK,EACL,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,cAAc,CACR,CAAA;AAAA,GAAA,CAAA;EACd,IAAMC,QAAQ,GAAG,SAAXA,QAAQ,GAAA;AAAA,IAAA,OACV,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAU,CAAA;AAAA,GAAA,CAAA;EACjF,IAAMC,eAAe,GAAG,SAAlBA,eAAe,GAAA;AAAA,IAAA,OAAS,CAAC,EAAE,EAAE,GAAG,EAAEV,gBAAgB,CAAU,CAAA;AAAA,GAAA,CAAA;EAClE,IAAMW,SAAS,GAAG,SAAZA,SAAS,GAAA;AAAA,IAAA,OACX,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAU,CAAA;AAAA,GAAA,CAAA;EACtF,IAAMC,SAAS,GAAG,SAAZA,SAAS,GAAA;AAAA,IAAA,OAAS,CAACP,QAAQ,EAAEQ,iBAAiB,CAAC,CAAA;AAAA,GAAA,CAAA;EACrD,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqB,GAAA;AAAA,IAAA,OAAS,CAACT,QAAQ,EAAEL,gBAAgB,CAAC,CAAA;AAAA,GAAA,CAAA;EAEhE,OAAO;AACHe,IAAAA,SAAS,EAAE,GAAG;AACdC,IAAAA,KAAK,EAAE;MACH7C,MAAM,EAAE,CAAC8C,KAAK,CAAC;MACf5C,OAAO,EAAE,CAAC8B,QAAQ,CAAC;MACnB7B,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE4C,YAAY,EAAElB,gBAAgB,CAAC;MAClDzB,UAAU,EAAEqC,SAAS,EAAE;MACvBpC,WAAW,EAAE,CAACL,MAAM,CAAC;MACrBM,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAEyC,YAAY,EAAElB,gBAAgB,CAAC;MAClEtB,aAAa,EAAEuB,uBAAuB,EAAE;MACxCtB,WAAW,EAAEuB,kBAAkB,EAAE;MACjCtB,QAAQ,EAAEgC,SAAS,EAAE;MACrB/B,SAAS,EAAE6B,eAAe,EAAE;MAC5B5B,SAAS,EAAEgC,qBAAqB,EAAE;MAClC/B,MAAM,EAAE2B,eAAe,EAAE;MACzB1B,GAAG,EAAEiB,uBAAuB,EAAE;MAC9BhB,kBAAkB,EAAE,CAACd,MAAM,CAAC;AAC5Be,MAAAA,0BAA0B,EAAE,CAACiC,SAAS,EAAEC,iBAAiB,CAAC;MAC1DjC,KAAK,EAAEY,8BAA8B,EAAE;MACvCX,MAAM,EAAEW,8BAA8B,EAAE;MACxCV,OAAO,EAAEuB,SAAS,EAAE;MACpBtB,OAAO,EAAEW,uBAAuB,EAAE;MAClCV,QAAQ,EAAEqB,SAAS,EAAE;MACrBpB,KAAK,EAAEoB,SAAS,EAAE;MAClBnB,KAAK,EAAEiB,eAAe,EAAE;MACxBhB,IAAI,EAAEoB,qBAAqB,EAAE;MAC7BnB,KAAK,EAAEM,uBAAuB,EAAE;AAChCL,MAAAA,SAAS,EAAEK,uBAAuB,EAAA;KACrC;AACDoB,IAAAA,WAAW,EAAE;AACT;AACA;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAEtB,gBAAgB,CAAA;OAAG,CAAC;AACnE;;;AAGG;MACHuB,SAAS,EAAE,CAAC,WAAW,CAAC;AACxB;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAACN,YAAY,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;AAAE,QAAA,aAAa,EAAEP,SAAS,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,SAAS,EAAA;OAAI,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,CAAA;OAAG,CAAC;AACrF;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAA;AAAC,OAAE,CAAC;AAC5D;;;AAGG;AACHc,MAAAA,GAAG,EAAE,CAAC;AAAEA,QAAAA,GAAG,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAA;AAAC,OAAE,CAAC;AACrC;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CACL,OAAO,EACP,cAAc,EACd,QAAQ,EACR,MAAM,EACN,aAAa,EACb,OAAO,EACP,cAAc,EACd,eAAe,EACf,YAAY,EACZ,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,MAAM,EACN,aAAa,EACb,UAAU,EACV,WAAW,EACX,QAAQ,CACX;AACD;;;AAGG;AACH,MAAA,OAAA,EAAO,CAAC;AAAE,QAAA,OAAA,EAAO,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAA;OAAG,CAAC;AAC7C;;;AAGG;AACHC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAA;OAAG,CAAC;AACrD;;;AAGG;AACHC,MAAAA,SAAS,EAAE,CAAC,SAAS,EAAE,gBAAgB,CAAC;AACxC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAMvB,EAAAA,CAAAA,MAAAA,CAAAA,YAAY,EAAE,EAAA,CAAEN,gBAAgB,CAAA,CAAA;OAAG,CAAC;AACtE;;;AAGG;AACH8B,MAAAA,QAAQ,EAAE,CAAC;AAAEA,QAAAA,QAAQ,EAAEhC,WAAW,EAAA;OAAI,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAEA,WAAW,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAEA,WAAW,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACHiC,MAAAA,UAAU,EAAE,CAAC;AAAEA,QAAAA,UAAU,EAAElC,aAAa,EAAA;OAAI,CAAC;AAC7C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,aAAa,EAAA;OAAI,CAAC;AACrD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE,QAAA,cAAc,EAAEA,aAAa,EAAA;OAAI,CAAC;AACrD;;;AAGG;MACHmC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC/D;;;AAGG;AACH7C,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACH8C,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAAC9C,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACH+C,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAAC/C,KAAK,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHgD,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAAChD,KAAK,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHiD,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACjD,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACHkD,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAClD,KAAK,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACHmD,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAACnD,KAAK,CAAA;AAAC,OAAE,CAAC;AACzB;;;AAGG;AACHoD,MAAAA,UAAU,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;AAChD;;;AAGG;AACHC,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAC,MAAM,EAAEC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/B;AACA;;;AAGG;AACHC,MAAAA,KAAK,EAAE,CAAC;AAAEA,QAAAA,KAAK,EAAE3C,8BAA8B,EAAA;OAAI,CAAC;AACpD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE4C,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,CAAA;OAAG,CAAC;AAC1E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAA;OAAG,CAAC;AAC3D;;;AAGG;AACHA,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE3C,gBAAgB,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACH4C,MAAAA,IAAI,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAElC,eAAe,EAAA;OAAI,CAAC;AACnC;;;AAGG;AACHmC,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAEnC,eAAe,EAAA;OAAI,CAAC;AACvC;;;AAGG;AACHoC,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAEL,SAAS,CAAA;OAAG,CAAC;AACxD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACxB,KAAK,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE8B,GAAG,EAAE,CAAC,MAAM,EAAE;AAAEC,UAAAA,IAAI,EAAE,CAAC,MAAM,EAAEP,SAAS,CAAA;SAAG,EAAEzC,gBAAgB,CAAA;AAAC,OAAE,CAAC;AACrF;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEI,6BAA6B,EAAA;OAAI,CAAC;AAC/D;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;AAAE,QAAA,SAAS,EAAEA,6BAA6B,EAAA;OAAI,CAAC;AAC3D;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACa,KAAK,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAEgC,GAAG,EAAE,CAAC,MAAM,EAAE;UAAED,IAAI,EAAE,CAACP,SAAS,CAAA;AAAC,SAAE,EAAEzC,gBAAgB,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEI,6BAA6B,EAAA;OAAI,CAAC;AAC/D;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;AAAE,QAAA,SAAS,EAAEA,6BAA6B,EAAA;OAAI,CAAC;AAC3D;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAA;OAAG,CAAC;AACjF;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAEJ,gBAAgB,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAEA,gBAAgB,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACHhB,MAAAA,GAAG,EAAE,CAAC;QAAEA,GAAG,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AACrB;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACA,GAAG,CAAA;AAAC,OAAE,CAAC;AAC7B;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEkE,QAAAA,OAAO,EAAG,CAAA,QAAQ,CAAKzC,CAAAA,MAAAA,CAAAA,QAAQ,EAAE,CAAA;OAAG,CAAC;AAC3D;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AACnF;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAE0C,QAAAA,OAAO,GAAG,QAAQ,CAAA,CAAA,MAAA,CAAK1C,QAAQ,EAAE,GAAE,UAAU,CAAA,CAAA;OAAG,CAAC;AACrE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE2C,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAA;OAAG,CAAC;AAC7E;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAEC,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAA;OAAG,CAAC;AACnF;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAE,QAAA,eAAe,EAAM5C,EAAAA,CAAAA,MAAAA,CAAAA,QAAQ,EAAE,EAAA,CAAE,UAAU,CAAA,CAAA;OAAG,CAAC;AACnE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAA;OAAG,CAAC;AACrF;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAA;OAAG,CAAC;AAC/E;AACA;;;AAGG;AACH6C,MAAAA,CAAC,EAAE,CAAC;QAAEA,CAAC,EAAE,CAAChE,OAAO,CAAA;AAAC,OAAE,CAAC;AACrB;;;AAGG;AACHiE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACjE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHkE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAClE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHmE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACnE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHoE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACpE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHqE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACrE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHsE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACtE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHuE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACvE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHwE,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACxE,OAAO,CAAA;AAAC,OAAE,CAAC;AACvB;;;AAGG;AACHyE,MAAAA,CAAC,EAAE,CAAC;QAAEA,CAAC,EAAE,CAAC3E,MAAM,CAAA;AAAC,OAAE,CAAC;AACpB;;;AAGG;AACH4E,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC5E,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACH6E,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC7E,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACH8E,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC9E,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACH+E,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAC/E,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHgF,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAChF,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHiF,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACjF,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHkF,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAAClF,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACHmF,MAAAA,EAAE,EAAE,CAAC;QAAEA,EAAE,EAAE,CAACnF,MAAM,CAAA;AAAC,OAAE,CAAC;AACtB;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACO,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;MACH,iBAAiB,EAAE,CAAC,iBAAiB,CAAC;AACtC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;MACH,iBAAiB,EAAE,CAAC,iBAAiB,CAAC;AACtC;AACA;;;AAGG;AACH6E,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAExE,gBAAgB,EAAE3B,OAAO,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE2B,gBAAgB,EAAEG,QAAQ,CAAA;OAAG,CAAC;AACzE;;;AAGG;AACH,MAAA,OAAO,EAAE,CACL;AACI,QAAA,OAAO,EAAE,CACL,GAAG,EACH,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP;UAAEsE,MAAM,EAAE,CAACvD,YAAY,CAAA;SAAG,EAC1BA,YAAY,EACZlB,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH0E,MAAAA,CAAC,EAAE,CAAC;AAAEA,QAAAA,CAAC,EAAE,CAAC1E,gBAAgB,EAAE3B,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAA;OAAG,CAAC;AACpE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE2B,gBAAgB,EAAEG,QAAQ,CAAA;OAAG,CAAC;AACzE;;;AAGG;AACH,MAAA,OAAO,EAAE,CAAC;QAAE,OAAO,EAAE,CAACH,gBAAgB,EAAE3B,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAA;OAAG,CAAC;AACxE;AACA;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAEsG,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAEzD,YAAY,EAAEE,iBAAiB,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC,aAAa,EAAE,sBAAsB,CAAC;AACzD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;AACtC;;;AAGG;AACH,MAAA,aAAa,EAAE,CACX;QACIwD,IAAI,EAAE,CACF,MAAM,EACN,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,MAAM,EACN,WAAW,EACX,OAAO,EACP/D,iBAAiB,CAAA;AAExB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE+D,IAAI,EAAE,CAAC3D,KAAK,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;MACH,YAAY,EAAE,CAAC,aAAa,CAAC;AAC7B;;;AAGG;MACH,aAAa,EAAE,CAAC,SAAS,CAAC;AAC1B;;;AAGG;MACH,kBAAkB,EAAE,CAAC,cAAc,CAAC;AACpC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;AAC9C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;AACpD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC;AAC1D;;;AAGG;AACH4D,MAAAA,QAAQ,EAAE,CACN;AACIA,QAAAA,QAAQ,EAAE,CACN,SAAS,EACT,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,QAAQ,EACR7E,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAE,CAAC,MAAM,EAAEK,QAAQ,EAAEQ,iBAAiB,CAAA;OAAG,CAAC;AACvE;;;AAGG;AACHiE,MAAAA,OAAO,EAAE,CACL;AACIA,QAAAA,OAAO,EAAE,CACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,EACP9E,gBAAgB,EAChBG,QAAQ,CAAA;AAEf,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;AAAE,QAAA,YAAY,EAAE,CAAC,MAAM,EAAEH,gBAAgB,CAAA;AAAC,OAAE,CAAC;AAC5D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAE+E,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE/E,gBAAgB,CAAA;OAAG,CAAC;AAC5E;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;AAAE+E,QAAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAA;AAAC,OAAE,CAAC;AACxD;;;;AAIG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAEC,WAAW,EAAE,CAAC7G,MAAM,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACkB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAEsF,QAAAA,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAA;OAAG,CAAC;AACpF;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEA,IAAI,EAAE,CAACxG,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAACkB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;MACH,iBAAiB,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,CAAC;AAC5E;;;AAGG;AACH,MAAA,uBAAuB,EAAE,CAAC;AAAE4F,QAAAA,UAAU,EAAM1E,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,EAAE,EAAA,CAAE,MAAM,CAAA,CAAA;OAAG,CAAC;AACvE;;;AAGG;AACH,MAAA,2BAA2B,EAAE,CAAC;AAAE0E,QAAAA,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE9E,QAAQ,CAAA;OAAG,CAAC;AAC9E;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;AAAE,QAAA,kBAAkB,EAAE,CAAC,MAAM,EAAEH,gBAAgB,EAAEG,QAAQ,CAAA;OAAG,CAAC;AAClF;;;AAGG;AACH,MAAA,uBAAuB,EAAE,CAAC;QAAE8E,UAAU,EAAE,CAAC9G,MAAM,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;MACH,gBAAgB,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,CAAC;AACzE;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,WAAW,CAAC;AAC3D;;;AAGG;AACH+G,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAEjF,uBAAuB,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CACd;AACIkF,QAAAA,KAAK,EAAE,CACH,UAAU,EACV,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,aAAa,EACb,KAAK,EACL,OAAO,EACPnF,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACHoF,MAAAA,UAAU,EAAE,CACR;AAAEA,QAAAA,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAA;AAAG,OAAA,CACtF;AACD;;;AAGG;AACH,MAAA,OAAA,EAAO,CAAC;AAAE,QAAA,OAAA,EAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AACtD;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAA;OAAG,CAAC;AAClD;;;AAGG;AACHlC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAEnD,gBAAgB,CAAA;AAAC,OAAE,CAAC;AAClD;AACA;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAEsF,QAAAA,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAA;OAAG,CAAC;AACvD;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAA;OAAG,CAAC;AACpE;;;;AAIG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACjG,OAAO,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAA;OAAG,CAAC;AAChE;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;AAAEiG,QAAAA,EAAE,EAAMhF,EAAAA,CAAAA,MAAAA,CAAAA,YAAY,EAAE,EAAA,CAAEiF,mBAAmB,CAAA,CAAA;OAAG,CAAC;AACjE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAED,EAAE,EAAE,CAAC,WAAW,EAAE;UAAEE,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAA;SAAG,CAAA;AAAC,OAAE,CAAC;AAClF;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAEF,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAEG,eAAe,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,UAAU,EAAE,CACR;QACIH,EAAE,EAAE,CACA,MAAM,EACN;AAAE,UAAA,aAAa,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAA;AAAG,SAAA,EAC/DI,cAAc,CAAA;AAErB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAEJ,EAAE,EAAE,CAACnH,MAAM,CAAA;AAAC,OAAE,CAAC;AAC9B;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAEwH,IAAI,EAAE,CAACzG,0BAA0B,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE0G,GAAG,EAAE,CAAC1G,0BAA0B,CAAA;AAAC,OAAE,CAAC;AAC3D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAE2G,EAAE,EAAE,CAAC3G,0BAA0B,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAEyG,IAAI,EAAE,CAAC1G,kBAAkB,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE2G,GAAG,EAAE,CAAC3G,kBAAkB,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE4G,EAAE,EAAE,CAAC5G,kBAAkB,CAAA;AAAC,OAAE,CAAC;AAC7C;AACA;;;AAGG;AACH6G,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAACrH,YAAY,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE,WAAW,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACA,YAAY,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAEsH,MAAM,EAAE,CAACpH,WAAW,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACU,OAAO,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE0G,QAAAA,MAAM,EAAMxF,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,EAAE,EAAA,CAAE,QAAQ,CAAA,CAAA;OAAG,CAAC;AAC5D;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAE,UAAU,EAAE,CAAC5B,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;MACH,kBAAkB,EAAE,CAAC,kBAAkB,CAAC;AACxC;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;MACH,kBAAkB,EAAE,CAAC,kBAAkB,CAAC;AACxC;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACU,OAAO,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE2G,QAAAA,MAAM,EAAEzF,aAAa,EAAA;OAAI,CAAC;AAC7C;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAEwF,MAAM,EAAE,CAACvH,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,UAAU,EAAE,CAACA,WAAW,CAAA;AAAC,OAAE,CAAC;AACjD;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAEwH,MAAM,EAAE,CAACxH,WAAW,CAAA;AAAC,OAAE,CAAC;AAC3C;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;AAAEyH,QAAAA,OAAO,EAAG,CAAA,EAAE,CAAK1F,CAAAA,MAAAA,CAAAA,aAAa,EAAE,CAAA;OAAG,CAAC;AACxD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAACP,gBAAgB,EAAEG,QAAQ,CAAA;AAAC,OAAE,CAAC;AACtE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAE8F,OAAO,EAAE,CAAC9F,QAAQ,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE8F,OAAO,EAAE,CAAC9H,MAAM,CAAA;AAAC,OAAE,CAAC;AACxC;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;AAAE+H,QAAAA,IAAI,EAAEhG,kBAAkB,EAAA;OAAI,CAAC;AAC1C;;;AAGG;MACH,cAAc,EAAE,CAAC,YAAY,CAAC;AAC9B;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEgG,IAAI,EAAE,CAAC/H,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAE,cAAc,EAAE,CAACkB,OAAO,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,aAAa,EAAE,CAACc,QAAQ,CAAA;AAAC,OAAE,CAAC;AAChD;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,aAAa,EAAE,CAAChC,MAAM,CAAA;AAAC,OAAE,CAAC;AAClD;AACA;;;AAGG;AACHgI,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAEjF,YAAY,EAAEkF,iBAAiB,CAAA;OAAG,CAAC;AAC5E;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;QAAED,MAAM,EAAE,CAAClF,KAAK,CAAA;AAAC,OAAE,CAAC;AACrC;;;AAGG;AACH5B,MAAAA,OAAO,EAAE,CAAC;QAAEA,OAAO,EAAE,CAACA,OAAO,CAAA;AAAC,OAAE,CAAC;AACjC;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEmB,aAAa,EAAA;OAAI,CAAC;AAC/C;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEA,aAAa,EAAA;OAAI,CAAC;AAC7C;AACA;;;;AAIG;AACH6F,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH/H,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAACA,IAAI,CAAA;AAAC,OAAE,CAAC;AACxB;;;AAGG;AACHC,MAAAA,UAAU,EAAE,CAAC;QAAEA,UAAU,EAAE,CAACA,UAAU,CAAA;AAAC,OAAE,CAAC;AAC1C;;;AAGG;AACHK,MAAAA,QAAQ,EAAE,CAAC;QAAEA,QAAQ,EAAE,CAACA,QAAQ,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAAC,EAAE,EAAE,MAAM,EAAEsC,YAAY,EAAElB,gBAAgB,CAAA;OAAG,CAAC;AAChF;;;AAGG;AACHnB,MAAAA,SAAS,EAAE,CAAC;QAAEA,SAAS,EAAE,CAACA,SAAS,CAAA;AAAC,OAAE,CAAC;AACvC;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAE,YAAY,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAACA,MAAM,CAAA;AAAC,OAAE,CAAC;AAC9B;;;AAGG;AACHQ,MAAAA,QAAQ,EAAE,CAAC;QAAEA,QAAQ,EAAE,CAACA,QAAQ,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACHE,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;;AAIG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAE,QAAA,iBAAiB,EAAE,CAAC,EAAE,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AACxD;;;AAGG;AACH,MAAA,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAACnB,IAAI,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACC,UAAU,CAAA;AAAC,OAAE,CAAC;AAChE;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,mBAAmB,EAAE,CAACK,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACH,MAAA,oBAAoB,EAAE,CAAC;QAAE,oBAAoB,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,qBAAqB,EAAE,CAAC;QAAE,qBAAqB,EAAE,CAACC,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/D;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;QAAE,iBAAiB,EAAE,CAACC,MAAM,CAAA;AAAC,OAAE,CAAC;AACpD;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACM,OAAO,CAAA;AAAC,OAAE,CAAC;AACvD;;;AAGG;AACH,MAAA,mBAAmB,EAAE,CAAC;QAAE,mBAAmB,EAAE,CAACE,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACE,KAAK,CAAA;AAAC,OAAE,CAAC;AACjD;AACA;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEsG,QAAAA,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;QAAE,gBAAgB,EAAE,CAACrH,aAAa,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACA,aAAa,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAAC;QAAE,kBAAkB,EAAE,CAACA,aAAa,CAAA;AAAC,OAAE,CAAC;AAC7D;;;AAGG;AACH,MAAA,cAAc,EAAE,CAAC;AAAE4H,QAAAA,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAA;AAAC,OAAE,CAAC;AAC9C;;;AAGG;AACHC,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AACzC;AACA;;;AAGG;AACHC,MAAAA,UAAU,EAAE,CACR;AACIA,QAAAA,UAAU,EAAE,CACR,MAAM,EACN,KAAK,EACL,EAAE,EACF,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,EACXxG,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACHyG,MAAAA,QAAQ,EAAE,CAAC;AAAEA,QAAAA,QAAQ,EAAE3F,qBAAqB,EAAA;OAAI,CAAC;AACjD;;;AAGG;AACH4F,MAAAA,IAAI,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE1G,gBAAgB,CAAA;OAAG,CAAC;AACrE;;;AAGG;AACH2G,MAAAA,KAAK,EAAE,CAAC;AAAEA,QAAAA,KAAK,EAAE7F,qBAAqB,EAAA;OAAI,CAAC;AAC3C;;;AAGG;AACH8F,MAAAA,OAAO,EAAE,CAAC;AAAEA,QAAAA,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE5G,gBAAgB,CAAA;OAAG,CAAC;AACrF;AACA;;;AAGG;AACH6G,MAAAA,SAAS,EAAE,CAAC;AAAEA,QAAAA,SAAS,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AAC/C;;;AAGG;AACHrH,MAAAA,KAAK,EAAE,CAAC;QAAEA,KAAK,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AAC3B;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACH,MAAA,SAAS,EAAE,CAAC;QAAE,SAAS,EAAE,CAACA,KAAK,CAAA;AAAC,OAAE,CAAC;AACnC;;;AAGG;AACHsH,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAACrE,SAAS,EAAEzC,gBAAgB,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAACJ,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAE,aAAa,EAAE,CAACA,SAAS,CAAA;AAAC,OAAE,CAAC;AAC/C;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;QAAE,QAAQ,EAAE,CAACF,IAAI,CAAA;AAAC,OAAE,CAAC;AAChC;;;AAGG;AACH,MAAA,QAAQ,EAAE,CAAC;QAAE,QAAQ,EAAE,CAACA,IAAI,CAAA;AAAC,OAAE,CAAC;AAChC;;;AAGG;AACH,MAAA,kBAAkB,EAAE,CAChB;QACIqH,MAAM,EAAE,CACJ,QAAQ,EACR,KAAK,EACL,WAAW,EACX,OAAO,EACP,cAAc,EACd,QAAQ,EACR,aAAa,EACb,MAAM,EACN,UAAU,EACV/G,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;AACA;;;AAGG;AACHgH,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAAC,MAAM,EAAE7I,MAAM,CAAA;AAAC,OAAE,CAAC;AACtC;;;AAGG;MACH8I,UAAU,EAAE,CAAC,iBAAiB,CAAC;AAC/B;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CACJ;AACIA,QAAAA,MAAM,EAAE,CACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,aAAa,EACb,MAAM,EACN,cAAc,EACd,UAAU,EACV,MAAM,EACN,WAAW,EACX,eAAe,EACf,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,aAAa,EACb,aAAa,EACb,SAAS,EACT,UAAU,EACVlH,gBAAgB,CAAA;AAEvB,OAAA,CACJ;AACD;;;AAGG;AACH,MAAA,aAAa,EAAE,CAAC;QAAEmH,KAAK,EAAE,CAAChJ,MAAM,CAAA;AAAC,OAAE,CAAC;AACpC;;;AAGG;AACH,MAAA,gBAAgB,EAAE,CAAC;AAAE,QAAA,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAC1D;;;AAGG;AACHiJ,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAA;OAAG,CAAC;AAC5C;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEC,QAAAA,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AACnD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEpH,uBAAuB,EAAA;OAAI,CAAC;AACvD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAE,QAAA,UAAU,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACvD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAE,QAAA,WAAW,EAAEA,uBAAuB,EAAA;OAAI,CAAC;AACzD;;;AAGG;AACH,MAAA,YAAY,EAAE,CAAC;QAAEqH,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAA;OAAG,CAAC;AAClE;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAA;AAAC,OAAE,CAAC;AAC7C;;;AAGG;AACH,MAAA,WAAW,EAAE,CAAC;QAAEA,IAAI,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAA;OAAG,CAAC;AACnD;;;AAGG;AACH,MAAA,iBAAiB,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAA;AAAC,OAAE,CAAC;AACzD;;;AAGG;AACHC,MAAAA,KAAK,EAAE,CACH;QACIA,KAAK,EAAE,CACH,MAAM,EACN,MAAM,EACN,YAAY,EACZ,cAAc,EACd;AAAEC,UAAAA,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAA;SAAG,CAAA;AAEzD,OAAA,CACJ;AACD;;;AAGG;AACHC,MAAAA,MAAM,EAAE,CAAC;QAAEA,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAA;OAAG,CAAC;AACrD;;;AAGG;AACH,MAAA,aAAa,EAAE,CACX;QAAE,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAEzH,gBAAgB,CAAA;AAAG,OAAA,CACnF;AACD;AACA;;;AAGG;AACH0H,MAAAA,IAAI,EAAE,CAAC;AAAEA,QAAAA,IAAI,EAAE,CAACvJ,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AAClC;;;AAGG;AACH,MAAA,UAAU,EAAE,CAAC;AAAEwJ,QAAAA,MAAM,EAAE,CAACxH,QAAQ,EAAEU,iBAAiB,CAAA;AAAC,OAAE,CAAC;AACvD;;;AAGG;AACH8G,MAAAA,MAAM,EAAE,CAAC;AAAEA,QAAAA,MAAM,EAAE,CAACxJ,MAAM,EAAE,MAAM,CAAA;AAAC,OAAE,CAAC;AACtC;AACA;;;AAGG;AACHyJ,MAAAA,EAAE,EAAE,CAAC,SAAS,EAAE,aAAa,CAAA;KAChC;AACDC,IAAAA,sBAAsB,EAAE;AACpB/F,MAAAA,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACtCC,MAAAA,UAAU,EAAE,CAAC,cAAc,EAAE,cAAc,CAAC;AAC5C5C,MAAAA,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC/E,MAAA,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC5B,MAAA,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC5BwD,MAAAA,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjC3D,MAAAA,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;AACvBsE,MAAAA,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACnDC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBO,MAAAA,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACnDC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAChBC,MAAAA,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MAChB,WAAW,EAAE,CAAC,SAAS,CAAC;MACxB,YAAY,EAAE,CACV,aAAa,EACb,kBAAkB,EAClB,YAAY,EACZ,aAAa,EACb,cAAc,CACjB;MACD,aAAa,EAAE,CAAC,YAAY,CAAC;MAC7B,kBAAkB,EAAE,CAAC,YAAY,CAAC;MAClC,YAAY,EAAE,CAAC,YAAY,CAAC;MAC5B,aAAa,EAAE,CAAC,YAAY,CAAC;MAC7B,cAAc,EAAE,CAAC,YAAY,CAAC;AAC9B6B,MAAAA,OAAO,EAAE,CACL,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,CACf;AACD,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,WAAW,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACzC,MAAA,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AAC1D,MAAA,UAAU,EAAE,CACR,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,CACf;AACD,MAAA,YAAY,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAA,YAAY,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;MAC1C,cAAc,EAAE,CACZ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,CACnB;AACD,MAAA,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACtD,MAAA,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AACtD,MAAA,UAAU,EAAE,CACR,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACd;AACD,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,UAAU,EAAE,CACR,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACd;AACD,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACvC,MAAA,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,CAAA;KACzC;AACDgC,IAAAA,8BAA8B,EAAE;MAC5B,WAAW,EAAE,CAAC,SAAS,CAAA;AAC1B,KAAA;GACsB,CAAA;AAC/B;;;;"}