/**
 * lucide-react v0.0.1 - ISC
 */

const dynamicIconImports = {
  "accessibility": () => import('./icons/accessibility.mjs'),
  "activity-square": () => import('./icons/activity-square.mjs'),
  "activity": () => import('./icons/activity.mjs'),
  "air-vent": () => import('./icons/air-vent.mjs'),
  "airplay": () => import('./icons/airplay.mjs'),
  "alarm-check": () => import('./icons/alarm-check.mjs'),
  "alarm-clock-off": () => import('./icons/alarm-clock-off.mjs'),
  "alarm-clock": () => import('./icons/alarm-clock.mjs'),
  "alarm-minus": () => import('./icons/alarm-minus.mjs'),
  "alarm-plus": () => import('./icons/alarm-plus.mjs'),
  "album": () => import('./icons/album.mjs'),
  "alert-circle": () => import('./icons/alert-circle.mjs'),
  "alert-octagon": () => import('./icons/alert-octagon.mjs'),
  "alert-triangle": () => import('./icons/alert-triangle.mjs'),
  "align-center-horizontal": () => import('./icons/align-center-horizontal.mjs'),
  "align-center-vertical": () => import('./icons/align-center-vertical.mjs'),
  "align-center": () => import('./icons/align-center.mjs'),
  "align-end-horizontal": () => import('./icons/align-end-horizontal.mjs'),
  "align-end-vertical": () => import('./icons/align-end-vertical.mjs'),
  "align-horizontal-distribute-center": () => import('./icons/align-horizontal-distribute-center.mjs'),
  "align-horizontal-distribute-end": () => import('./icons/align-horizontal-distribute-end.mjs'),
  "align-horizontal-distribute-start": () => import('./icons/align-horizontal-distribute-start.mjs'),
  "align-horizontal-justify-center": () => import('./icons/align-horizontal-justify-center.mjs'),
  "align-horizontal-justify-end": () => import('./icons/align-horizontal-justify-end.mjs'),
  "align-horizontal-justify-start": () => import('./icons/align-horizontal-justify-start.mjs'),
  "align-horizontal-space-around": () => import('./icons/align-horizontal-space-around.mjs'),
  "align-horizontal-space-between": () => import('./icons/align-horizontal-space-between.mjs'),
  "align-justify": () => import('./icons/align-justify.mjs'),
  "align-left": () => import('./icons/align-left.mjs'),
  "align-right": () => import('./icons/align-right.mjs'),
  "align-start-horizontal": () => import('./icons/align-start-horizontal.mjs'),
  "align-start-vertical": () => import('./icons/align-start-vertical.mjs'),
  "align-vertical-distribute-center": () => import('./icons/align-vertical-distribute-center.mjs'),
  "align-vertical-distribute-end": () => import('./icons/align-vertical-distribute-end.mjs'),
  "align-vertical-distribute-start": () => import('./icons/align-vertical-distribute-start.mjs'),
  "align-vertical-justify-center": () => import('./icons/align-vertical-justify-center.mjs'),
  "align-vertical-justify-end": () => import('./icons/align-vertical-justify-end.mjs'),
  "align-vertical-justify-start": () => import('./icons/align-vertical-justify-start.mjs'),
  "align-vertical-space-around": () => import('./icons/align-vertical-space-around.mjs'),
  "align-vertical-space-between": () => import('./icons/align-vertical-space-between.mjs'),
  "ampersand": () => import('./icons/ampersand.mjs'),
  "ampersands": () => import('./icons/ampersands.mjs'),
  "anchor": () => import('./icons/anchor.mjs'),
  "angry": () => import('./icons/angry.mjs'),
  "annoyed": () => import('./icons/annoyed.mjs'),
  "antenna": () => import('./icons/antenna.mjs'),
  "aperture": () => import('./icons/aperture.mjs'),
  "app-window": () => import('./icons/app-window.mjs'),
  "apple": () => import('./icons/apple.mjs'),
  "archive-restore": () => import('./icons/archive-restore.mjs'),
  "archive": () => import('./icons/archive.mjs'),
  "area-chart": () => import('./icons/area-chart.mjs'),
  "armchair": () => import('./icons/armchair.mjs'),
  "arrow-big-down-dash": () => import('./icons/arrow-big-down-dash.mjs'),
  "arrow-big-down": () => import('./icons/arrow-big-down.mjs'),
  "arrow-big-left-dash": () => import('./icons/arrow-big-left-dash.mjs'),
  "arrow-big-left": () => import('./icons/arrow-big-left.mjs'),
  "arrow-big-right-dash": () => import('./icons/arrow-big-right-dash.mjs'),
  "arrow-big-right": () => import('./icons/arrow-big-right.mjs'),
  "arrow-big-up-dash": () => import('./icons/arrow-big-up-dash.mjs'),
  "arrow-big-up": () => import('./icons/arrow-big-up.mjs'),
  "arrow-down-0-1": () => import('./icons/arrow-down-0-1.mjs'),
  "arrow-down-1-0": () => import('./icons/arrow-down-1-0.mjs'),
  "arrow-down-a-z": () => import('./icons/arrow-down-a-z.mjs'),
  "arrow-down-circle": () => import('./icons/arrow-down-circle.mjs'),
  "arrow-down-from-line": () => import('./icons/arrow-down-from-line.mjs'),
  "arrow-down-left-from-circle": () => import('./icons/arrow-down-left-from-circle.mjs'),
  "arrow-down-left-square": () => import('./icons/arrow-down-left-square.mjs'),
  "arrow-down-left": () => import('./icons/arrow-down-left.mjs'),
  "arrow-down-narrow-wide": () => import('./icons/arrow-down-narrow-wide.mjs'),
  "arrow-down-right-from-circle": () => import('./icons/arrow-down-right-from-circle.mjs'),
  "arrow-down-right-square": () => import('./icons/arrow-down-right-square.mjs'),
  "arrow-down-right": () => import('./icons/arrow-down-right.mjs'),
  "arrow-down-square": () => import('./icons/arrow-down-square.mjs'),
  "arrow-down-to-dot": () => import('./icons/arrow-down-to-dot.mjs'),
  "arrow-down-to-line": () => import('./icons/arrow-down-to-line.mjs'),
  "arrow-down-up": () => import('./icons/arrow-down-up.mjs'),
  "arrow-down-wide-narrow": () => import('./icons/arrow-down-wide-narrow.mjs'),
  "arrow-down-z-a": () => import('./icons/arrow-down-z-a.mjs'),
  "arrow-down": () => import('./icons/arrow-down.mjs'),
  "arrow-left-circle": () => import('./icons/arrow-left-circle.mjs'),
  "arrow-left-from-line": () => import('./icons/arrow-left-from-line.mjs'),
  "arrow-left-right": () => import('./icons/arrow-left-right.mjs'),
  "arrow-left-square": () => import('./icons/arrow-left-square.mjs'),
  "arrow-left-to-line": () => import('./icons/arrow-left-to-line.mjs'),
  "arrow-left": () => import('./icons/arrow-left.mjs'),
  "arrow-right-circle": () => import('./icons/arrow-right-circle.mjs'),
  "arrow-right-from-line": () => import('./icons/arrow-right-from-line.mjs'),
  "arrow-right-left": () => import('./icons/arrow-right-left.mjs'),
  "arrow-right-square": () => import('./icons/arrow-right-square.mjs'),
  "arrow-right-to-line": () => import('./icons/arrow-right-to-line.mjs'),
  "arrow-right": () => import('./icons/arrow-right.mjs'),
  "arrow-up-0-1": () => import('./icons/arrow-up-0-1.mjs'),
  "arrow-up-1-0": () => import('./icons/arrow-up-1-0.mjs'),
  "arrow-up-a-z": () => import('./icons/arrow-up-a-z.mjs'),
  "arrow-up-circle": () => import('./icons/arrow-up-circle.mjs'),
  "arrow-up-down": () => import('./icons/arrow-up-down.mjs'),
  "arrow-up-from-dot": () => import('./icons/arrow-up-from-dot.mjs'),
  "arrow-up-from-line": () => import('./icons/arrow-up-from-line.mjs'),
  "arrow-up-left-from-circle": () => import('./icons/arrow-up-left-from-circle.mjs'),
  "arrow-up-left-square": () => import('./icons/arrow-up-left-square.mjs'),
  "arrow-up-left": () => import('./icons/arrow-up-left.mjs'),
  "arrow-up-narrow-wide": () => import('./icons/arrow-up-narrow-wide.mjs'),
  "arrow-up-right-from-circle": () => import('./icons/arrow-up-right-from-circle.mjs'),
  "arrow-up-right-square": () => import('./icons/arrow-up-right-square.mjs'),
  "arrow-up-right": () => import('./icons/arrow-up-right.mjs'),
  "arrow-up-square": () => import('./icons/arrow-up-square.mjs'),
  "arrow-up-to-line": () => import('./icons/arrow-up-to-line.mjs'),
  "arrow-up-wide-narrow": () => import('./icons/arrow-up-wide-narrow.mjs'),
  "arrow-up-z-a": () => import('./icons/arrow-up-z-a.mjs'),
  "arrow-up": () => import('./icons/arrow-up.mjs'),
  "arrows-up-from-line": () => import('./icons/arrows-up-from-line.mjs'),
  "asterisk": () => import('./icons/asterisk.mjs'),
  "at-sign": () => import('./icons/at-sign.mjs'),
  "atom": () => import('./icons/atom.mjs'),
  "award": () => import('./icons/award.mjs'),
  "axe": () => import('./icons/axe.mjs'),
  "axis-3d": () => import('./icons/axis-3d.mjs'),
  "baby": () => import('./icons/baby.mjs'),
  "backpack": () => import('./icons/backpack.mjs'),
  "badge-alert": () => import('./icons/badge-alert.mjs'),
  "badge-check": () => import('./icons/badge-check.mjs'),
  "badge-dollar-sign": () => import('./icons/badge-dollar-sign.mjs'),
  "badge-help": () => import('./icons/badge-help.mjs'),
  "badge-info": () => import('./icons/badge-info.mjs'),
  "badge-minus": () => import('./icons/badge-minus.mjs'),
  "badge-percent": () => import('./icons/badge-percent.mjs'),
  "badge-plus": () => import('./icons/badge-plus.mjs'),
  "badge-x": () => import('./icons/badge-x.mjs'),
  "badge": () => import('./icons/badge.mjs'),
  "baggage-claim": () => import('./icons/baggage-claim.mjs'),
  "ban": () => import('./icons/ban.mjs'),
  "banana": () => import('./icons/banana.mjs'),
  "banknote": () => import('./icons/banknote.mjs'),
  "bar-chart-2": () => import('./icons/bar-chart-2.mjs'),
  "bar-chart-3": () => import('./icons/bar-chart-3.mjs'),
  "bar-chart-4": () => import('./icons/bar-chart-4.mjs'),
  "bar-chart-big": () => import('./icons/bar-chart-big.mjs'),
  "bar-chart-horizontal-big": () => import('./icons/bar-chart-horizontal-big.mjs'),
  "bar-chart-horizontal": () => import('./icons/bar-chart-horizontal.mjs'),
  "bar-chart": () => import('./icons/bar-chart.mjs'),
  "baseline": () => import('./icons/baseline.mjs'),
  "bath": () => import('./icons/bath.mjs'),
  "battery-charging": () => import('./icons/battery-charging.mjs'),
  "battery-full": () => import('./icons/battery-full.mjs'),
  "battery-low": () => import('./icons/battery-low.mjs'),
  "battery-medium": () => import('./icons/battery-medium.mjs'),
  "battery-warning": () => import('./icons/battery-warning.mjs'),
  "battery": () => import('./icons/battery.mjs'),
  "beaker": () => import('./icons/beaker.mjs'),
  "bean-off": () => import('./icons/bean-off.mjs'),
  "bean": () => import('./icons/bean.mjs'),
  "bed-double": () => import('./icons/bed-double.mjs'),
  "bed-single": () => import('./icons/bed-single.mjs'),
  "bed": () => import('./icons/bed.mjs'),
  "beef": () => import('./icons/beef.mjs'),
  "beer": () => import('./icons/beer.mjs'),
  "bell-dot": () => import('./icons/bell-dot.mjs'),
  "bell-minus": () => import('./icons/bell-minus.mjs'),
  "bell-off": () => import('./icons/bell-off.mjs'),
  "bell-plus": () => import('./icons/bell-plus.mjs'),
  "bell-ring": () => import('./icons/bell-ring.mjs'),
  "bell": () => import('./icons/bell.mjs'),
  "bike": () => import('./icons/bike.mjs'),
  "binary": () => import('./icons/binary.mjs'),
  "biohazard": () => import('./icons/biohazard.mjs'),
  "bird": () => import('./icons/bird.mjs'),
  "bitcoin": () => import('./icons/bitcoin.mjs'),
  "blinds": () => import('./icons/blinds.mjs'),
  "bluetooth-connected": () => import('./icons/bluetooth-connected.mjs'),
  "bluetooth-off": () => import('./icons/bluetooth-off.mjs'),
  "bluetooth-searching": () => import('./icons/bluetooth-searching.mjs'),
  "bluetooth": () => import('./icons/bluetooth.mjs'),
  "bold": () => import('./icons/bold.mjs'),
  "bomb": () => import('./icons/bomb.mjs'),
  "bone": () => import('./icons/bone.mjs'),
  "book-copy": () => import('./icons/book-copy.mjs'),
  "book-down": () => import('./icons/book-down.mjs'),
  "book-key": () => import('./icons/book-key.mjs'),
  "book-lock": () => import('./icons/book-lock.mjs'),
  "book-marked": () => import('./icons/book-marked.mjs'),
  "book-minus": () => import('./icons/book-minus.mjs'),
  "book-open-check": () => import('./icons/book-open-check.mjs'),
  "book-open": () => import('./icons/book-open.mjs'),
  "book-plus": () => import('./icons/book-plus.mjs'),
  "book-template": () => import('./icons/book-template.mjs'),
  "book-up-2": () => import('./icons/book-up-2.mjs'),
  "book-up": () => import('./icons/book-up.mjs'),
  "book-x": () => import('./icons/book-x.mjs'),
  "book": () => import('./icons/book.mjs'),
  "bookmark-minus": () => import('./icons/bookmark-minus.mjs'),
  "bookmark-plus": () => import('./icons/bookmark-plus.mjs'),
  "bookmark": () => import('./icons/bookmark.mjs'),
  "boom-box": () => import('./icons/boom-box.mjs'),
  "bot": () => import('./icons/bot.mjs'),
  "box-select": () => import('./icons/box-select.mjs'),
  "box": () => import('./icons/box.mjs'),
  "boxes": () => import('./icons/boxes.mjs'),
  "braces": () => import('./icons/braces.mjs'),
  "brackets": () => import('./icons/brackets.mjs'),
  "brain-circuit": () => import('./icons/brain-circuit.mjs'),
  "brain-cog": () => import('./icons/brain-cog.mjs'),
  "brain": () => import('./icons/brain.mjs'),
  "briefcase": () => import('./icons/briefcase.mjs'),
  "bring-to-front": () => import('./icons/bring-to-front.mjs'),
  "brush": () => import('./icons/brush.mjs'),
  "bug": () => import('./icons/bug.mjs'),
  "building-2": () => import('./icons/building-2.mjs'),
  "building": () => import('./icons/building.mjs'),
  "bus": () => import('./icons/bus.mjs'),
  "cable": () => import('./icons/cable.mjs'),
  "cake-slice": () => import('./icons/cake-slice.mjs'),
  "cake": () => import('./icons/cake.mjs'),
  "calculator": () => import('./icons/calculator.mjs'),
  "calendar-check-2": () => import('./icons/calendar-check-2.mjs'),
  "calendar-check": () => import('./icons/calendar-check.mjs'),
  "calendar-clock": () => import('./icons/calendar-clock.mjs'),
  "calendar-days": () => import('./icons/calendar-days.mjs'),
  "calendar-heart": () => import('./icons/calendar-heart.mjs'),
  "calendar-minus": () => import('./icons/calendar-minus.mjs'),
  "calendar-off": () => import('./icons/calendar-off.mjs'),
  "calendar-plus": () => import('./icons/calendar-plus.mjs'),
  "calendar-range": () => import('./icons/calendar-range.mjs'),
  "calendar-search": () => import('./icons/calendar-search.mjs'),
  "calendar-x-2": () => import('./icons/calendar-x-2.mjs'),
  "calendar-x": () => import('./icons/calendar-x.mjs'),
  "calendar": () => import('./icons/calendar.mjs'),
  "camera-off": () => import('./icons/camera-off.mjs'),
  "camera": () => import('./icons/camera.mjs'),
  "candlestick-chart": () => import('./icons/candlestick-chart.mjs'),
  "candy-cane": () => import('./icons/candy-cane.mjs'),
  "candy-off": () => import('./icons/candy-off.mjs'),
  "candy": () => import('./icons/candy.mjs'),
  "car": () => import('./icons/car.mjs'),
  "carrot": () => import('./icons/carrot.mjs'),
  "case-lower": () => import('./icons/case-lower.mjs'),
  "case-sensitive": () => import('./icons/case-sensitive.mjs'),
  "case-upper": () => import('./icons/case-upper.mjs'),
  "cassette-tape": () => import('./icons/cassette-tape.mjs'),
  "cast": () => import('./icons/cast.mjs'),
  "castle": () => import('./icons/castle.mjs'),
  "cat": () => import('./icons/cat.mjs'),
  "check-check": () => import('./icons/check-check.mjs'),
  "check-circle-2": () => import('./icons/check-circle-2.mjs'),
  "check-circle": () => import('./icons/check-circle.mjs'),
  "check-square": () => import('./icons/check-square.mjs'),
  "check": () => import('./icons/check.mjs'),
  "chef-hat": () => import('./icons/chef-hat.mjs'),
  "cherry": () => import('./icons/cherry.mjs'),
  "chevron-down-circle": () => import('./icons/chevron-down-circle.mjs'),
  "chevron-down-square": () => import('./icons/chevron-down-square.mjs'),
  "chevron-down": () => import('./icons/chevron-down.mjs'),
  "chevron-first": () => import('./icons/chevron-first.mjs'),
  "chevron-last": () => import('./icons/chevron-last.mjs'),
  "chevron-left-circle": () => import('./icons/chevron-left-circle.mjs'),
  "chevron-left-square": () => import('./icons/chevron-left-square.mjs'),
  "chevron-left": () => import('./icons/chevron-left.mjs'),
  "chevron-right-circle": () => import('./icons/chevron-right-circle.mjs'),
  "chevron-right-square": () => import('./icons/chevron-right-square.mjs'),
  "chevron-right": () => import('./icons/chevron-right.mjs'),
  "chevron-up-circle": () => import('./icons/chevron-up-circle.mjs'),
  "chevron-up-square": () => import('./icons/chevron-up-square.mjs'),
  "chevron-up": () => import('./icons/chevron-up.mjs'),
  "chevrons-down-up": () => import('./icons/chevrons-down-up.mjs'),
  "chevrons-down": () => import('./icons/chevrons-down.mjs'),
  "chevrons-left-right": () => import('./icons/chevrons-left-right.mjs'),
  "chevrons-left": () => import('./icons/chevrons-left.mjs'),
  "chevrons-right-left": () => import('./icons/chevrons-right-left.mjs'),
  "chevrons-right": () => import('./icons/chevrons-right.mjs'),
  "chevrons-up-down": () => import('./icons/chevrons-up-down.mjs'),
  "chevrons-up": () => import('./icons/chevrons-up.mjs'),
  "chrome": () => import('./icons/chrome.mjs'),
  "church": () => import('./icons/church.mjs'),
  "cigarette-off": () => import('./icons/cigarette-off.mjs'),
  "cigarette": () => import('./icons/cigarette.mjs'),
  "circle-dashed": () => import('./icons/circle-dashed.mjs'),
  "circle-dollar-sign": () => import('./icons/circle-dollar-sign.mjs'),
  "circle-dot-dashed": () => import('./icons/circle-dot-dashed.mjs'),
  "circle-dot": () => import('./icons/circle-dot.mjs'),
  "circle-ellipsis": () => import('./icons/circle-ellipsis.mjs'),
  "circle-equal": () => import('./icons/circle-equal.mjs'),
  "circle-off": () => import('./icons/circle-off.mjs'),
  "circle-slash-2": () => import('./icons/circle-slash-2.mjs'),
  "circle-slash": () => import('./icons/circle-slash.mjs'),
  "circle": () => import('./icons/circle.mjs'),
  "circuit-board": () => import('./icons/circuit-board.mjs'),
  "citrus": () => import('./icons/citrus.mjs'),
  "clapperboard": () => import('./icons/clapperboard.mjs'),
  "clipboard-check": () => import('./icons/clipboard-check.mjs'),
  "clipboard-copy": () => import('./icons/clipboard-copy.mjs'),
  "clipboard-edit": () => import('./icons/clipboard-edit.mjs'),
  "clipboard-list": () => import('./icons/clipboard-list.mjs'),
  "clipboard-paste": () => import('./icons/clipboard-paste.mjs'),
  "clipboard-signature": () => import('./icons/clipboard-signature.mjs'),
  "clipboard-type": () => import('./icons/clipboard-type.mjs'),
  "clipboard-x": () => import('./icons/clipboard-x.mjs'),
  "clipboard": () => import('./icons/clipboard.mjs'),
  "clock-1": () => import('./icons/clock-1.mjs'),
  "clock-10": () => import('./icons/clock-10.mjs'),
  "clock-11": () => import('./icons/clock-11.mjs'),
  "clock-12": () => import('./icons/clock-12.mjs'),
  "clock-2": () => import('./icons/clock-2.mjs'),
  "clock-3": () => import('./icons/clock-3.mjs'),
  "clock-4": () => import('./icons/clock-4.mjs'),
  "clock-5": () => import('./icons/clock-5.mjs'),
  "clock-6": () => import('./icons/clock-6.mjs'),
  "clock-7": () => import('./icons/clock-7.mjs'),
  "clock-8": () => import('./icons/clock-8.mjs'),
  "clock-9": () => import('./icons/clock-9.mjs'),
  "clock": () => import('./icons/clock.mjs'),
  "cloud-cog": () => import('./icons/cloud-cog.mjs'),
  "cloud-drizzle": () => import('./icons/cloud-drizzle.mjs'),
  "cloud-fog": () => import('./icons/cloud-fog.mjs'),
  "cloud-hail": () => import('./icons/cloud-hail.mjs'),
  "cloud-lightning": () => import('./icons/cloud-lightning.mjs'),
  "cloud-moon-rain": () => import('./icons/cloud-moon-rain.mjs'),
  "cloud-moon": () => import('./icons/cloud-moon.mjs'),
  "cloud-off": () => import('./icons/cloud-off.mjs'),
  "cloud-rain-wind": () => import('./icons/cloud-rain-wind.mjs'),
  "cloud-rain": () => import('./icons/cloud-rain.mjs'),
  "cloud-snow": () => import('./icons/cloud-snow.mjs'),
  "cloud-sun-rain": () => import('./icons/cloud-sun-rain.mjs'),
  "cloud-sun": () => import('./icons/cloud-sun.mjs'),
  "cloud": () => import('./icons/cloud.mjs'),
  "cloudy": () => import('./icons/cloudy.mjs'),
  "clover": () => import('./icons/clover.mjs'),
  "club": () => import('./icons/club.mjs'),
  "code-2": () => import('./icons/code-2.mjs'),
  "code": () => import('./icons/code.mjs'),
  "codepen": () => import('./icons/codepen.mjs'),
  "codesandbox": () => import('./icons/codesandbox.mjs'),
  "coffee": () => import('./icons/coffee.mjs'),
  "cog": () => import('./icons/cog.mjs'),
  "coins": () => import('./icons/coins.mjs'),
  "columns": () => import('./icons/columns.mjs'),
  "combine": () => import('./icons/combine.mjs'),
  "command": () => import('./icons/command.mjs'),
  "compass": () => import('./icons/compass.mjs'),
  "component": () => import('./icons/component.mjs'),
  "computer": () => import('./icons/computer.mjs'),
  "concierge-bell": () => import('./icons/concierge-bell.mjs'),
  "construction": () => import('./icons/construction.mjs'),
  "contact-2": () => import('./icons/contact-2.mjs'),
  "contact": () => import('./icons/contact.mjs'),
  "container": () => import('./icons/container.mjs'),
  "contrast": () => import('./icons/contrast.mjs'),
  "cookie": () => import('./icons/cookie.mjs'),
  "copy-check": () => import('./icons/copy-check.mjs'),
  "copy-minus": () => import('./icons/copy-minus.mjs'),
  "copy-plus": () => import('./icons/copy-plus.mjs'),
  "copy-slash": () => import('./icons/copy-slash.mjs'),
  "copy-x": () => import('./icons/copy-x.mjs'),
  "copy": () => import('./icons/copy.mjs'),
  "copyleft": () => import('./icons/copyleft.mjs'),
  "copyright": () => import('./icons/copyright.mjs'),
  "corner-down-left": () => import('./icons/corner-down-left.mjs'),
  "corner-down-right": () => import('./icons/corner-down-right.mjs'),
  "corner-left-down": () => import('./icons/corner-left-down.mjs'),
  "corner-left-up": () => import('./icons/corner-left-up.mjs'),
  "corner-right-down": () => import('./icons/corner-right-down.mjs'),
  "corner-right-up": () => import('./icons/corner-right-up.mjs'),
  "corner-up-left": () => import('./icons/corner-up-left.mjs'),
  "corner-up-right": () => import('./icons/corner-up-right.mjs'),
  "cpu": () => import('./icons/cpu.mjs'),
  "creative-commons": () => import('./icons/creative-commons.mjs'),
  "credit-card": () => import('./icons/credit-card.mjs'),
  "croissant": () => import('./icons/croissant.mjs'),
  "crop": () => import('./icons/crop.mjs'),
  "cross": () => import('./icons/cross.mjs'),
  "crosshair": () => import('./icons/crosshair.mjs'),
  "crown": () => import('./icons/crown.mjs'),
  "cup-soda": () => import('./icons/cup-soda.mjs'),
  "currency": () => import('./icons/currency.mjs'),
  "database-backup": () => import('./icons/database-backup.mjs'),
  "database": () => import('./icons/database.mjs'),
  "delete": () => import('./icons/delete.mjs'),
  "dessert": () => import('./icons/dessert.mjs'),
  "diamond": () => import('./icons/diamond.mjs'),
  "dice-1": () => import('./icons/dice-1.mjs'),
  "dice-2": () => import('./icons/dice-2.mjs'),
  "dice-3": () => import('./icons/dice-3.mjs'),
  "dice-4": () => import('./icons/dice-4.mjs'),
  "dice-5": () => import('./icons/dice-5.mjs'),
  "dice-6": () => import('./icons/dice-6.mjs'),
  "dices": () => import('./icons/dices.mjs'),
  "diff": () => import('./icons/diff.mjs'),
  "disc-2": () => import('./icons/disc-2.mjs'),
  "disc-3": () => import('./icons/disc-3.mjs'),
  "disc": () => import('./icons/disc.mjs'),
  "divide-circle": () => import('./icons/divide-circle.mjs'),
  "divide-square": () => import('./icons/divide-square.mjs'),
  "divide": () => import('./icons/divide.mjs'),
  "dna-off": () => import('./icons/dna-off.mjs'),
  "dna": () => import('./icons/dna.mjs'),
  "dog": () => import('./icons/dog.mjs'),
  "dollar-sign": () => import('./icons/dollar-sign.mjs'),
  "donut": () => import('./icons/donut.mjs'),
  "door-closed": () => import('./icons/door-closed.mjs'),
  "door-open": () => import('./icons/door-open.mjs'),
  "dot": () => import('./icons/dot.mjs'),
  "download-cloud": () => import('./icons/download-cloud.mjs'),
  "download": () => import('./icons/download.mjs'),
  "dribbble": () => import('./icons/dribbble.mjs'),
  "droplet": () => import('./icons/droplet.mjs'),
  "droplets": () => import('./icons/droplets.mjs'),
  "drumstick": () => import('./icons/drumstick.mjs'),
  "dumbbell": () => import('./icons/dumbbell.mjs'),
  "ear-off": () => import('./icons/ear-off.mjs'),
  "ear": () => import('./icons/ear.mjs'),
  "egg-fried": () => import('./icons/egg-fried.mjs'),
  "egg-off": () => import('./icons/egg-off.mjs'),
  "egg": () => import('./icons/egg.mjs'),
  "equal-not": () => import('./icons/equal-not.mjs'),
  "equal": () => import('./icons/equal.mjs'),
  "eraser": () => import('./icons/eraser.mjs'),
  "euro": () => import('./icons/euro.mjs'),
  "expand": () => import('./icons/expand.mjs'),
  "external-link": () => import('./icons/external-link.mjs'),
  "eye-off": () => import('./icons/eye-off.mjs'),
  "eye": () => import('./icons/eye.mjs'),
  "facebook": () => import('./icons/facebook.mjs'),
  "factory": () => import('./icons/factory.mjs'),
  "fan": () => import('./icons/fan.mjs'),
  "fast-forward": () => import('./icons/fast-forward.mjs'),
  "feather": () => import('./icons/feather.mjs'),
  "ferris-wheel": () => import('./icons/ferris-wheel.mjs'),
  "figma": () => import('./icons/figma.mjs'),
  "file-archive": () => import('./icons/file-archive.mjs'),
  "file-audio-2": () => import('./icons/file-audio-2.mjs'),
  "file-audio": () => import('./icons/file-audio.mjs'),
  "file-axis-3d": () => import('./icons/file-axis-3d.mjs'),
  "file-badge-2": () => import('./icons/file-badge-2.mjs'),
  "file-badge": () => import('./icons/file-badge.mjs'),
  "file-bar-chart-2": () => import('./icons/file-bar-chart-2.mjs'),
  "file-bar-chart": () => import('./icons/file-bar-chart.mjs'),
  "file-box": () => import('./icons/file-box.mjs'),
  "file-check-2": () => import('./icons/file-check-2.mjs'),
  "file-check": () => import('./icons/file-check.mjs'),
  "file-clock": () => import('./icons/file-clock.mjs'),
  "file-code-2": () => import('./icons/file-code-2.mjs'),
  "file-code": () => import('./icons/file-code.mjs'),
  "file-cog-2": () => import('./icons/file-cog-2.mjs'),
  "file-cog": () => import('./icons/file-cog.mjs'),
  "file-diff": () => import('./icons/file-diff.mjs'),
  "file-digit": () => import('./icons/file-digit.mjs'),
  "file-down": () => import('./icons/file-down.mjs'),
  "file-edit": () => import('./icons/file-edit.mjs'),
  "file-heart": () => import('./icons/file-heart.mjs'),
  "file-image": () => import('./icons/file-image.mjs'),
  "file-input": () => import('./icons/file-input.mjs'),
  "file-json-2": () => import('./icons/file-json-2.mjs'),
  "file-json": () => import('./icons/file-json.mjs'),
  "file-key-2": () => import('./icons/file-key-2.mjs'),
  "file-key": () => import('./icons/file-key.mjs'),
  "file-line-chart": () => import('./icons/file-line-chart.mjs'),
  "file-lock-2": () => import('./icons/file-lock-2.mjs'),
  "file-lock": () => import('./icons/file-lock.mjs'),
  "file-minus-2": () => import('./icons/file-minus-2.mjs'),
  "file-minus": () => import('./icons/file-minus.mjs'),
  "file-output": () => import('./icons/file-output.mjs'),
  "file-pie-chart": () => import('./icons/file-pie-chart.mjs'),
  "file-plus-2": () => import('./icons/file-plus-2.mjs'),
  "file-plus": () => import('./icons/file-plus.mjs'),
  "file-question": () => import('./icons/file-question.mjs'),
  "file-scan": () => import('./icons/file-scan.mjs'),
  "file-search-2": () => import('./icons/file-search-2.mjs'),
  "file-search": () => import('./icons/file-search.mjs'),
  "file-signature": () => import('./icons/file-signature.mjs'),
  "file-spreadsheet": () => import('./icons/file-spreadsheet.mjs'),
  "file-stack": () => import('./icons/file-stack.mjs'),
  "file-symlink": () => import('./icons/file-symlink.mjs'),
  "file-terminal": () => import('./icons/file-terminal.mjs'),
  "file-text": () => import('./icons/file-text.mjs'),
  "file-type-2": () => import('./icons/file-type-2.mjs'),
  "file-type": () => import('./icons/file-type.mjs'),
  "file-up": () => import('./icons/file-up.mjs'),
  "file-video-2": () => import('./icons/file-video-2.mjs'),
  "file-video": () => import('./icons/file-video.mjs'),
  "file-volume-2": () => import('./icons/file-volume-2.mjs'),
  "file-volume": () => import('./icons/file-volume.mjs'),
  "file-warning": () => import('./icons/file-warning.mjs'),
  "file-x-2": () => import('./icons/file-x-2.mjs'),
  "file-x": () => import('./icons/file-x.mjs'),
  "file": () => import('./icons/file.mjs'),
  "files": () => import('./icons/files.mjs'),
  "film": () => import('./icons/film.mjs'),
  "filter-x": () => import('./icons/filter-x.mjs'),
  "filter": () => import('./icons/filter.mjs'),
  "fingerprint": () => import('./icons/fingerprint.mjs'),
  "fish-off": () => import('./icons/fish-off.mjs'),
  "fish": () => import('./icons/fish.mjs'),
  "flag-off": () => import('./icons/flag-off.mjs'),
  "flag-triangle-left": () => import('./icons/flag-triangle-left.mjs'),
  "flag-triangle-right": () => import('./icons/flag-triangle-right.mjs'),
  "flag": () => import('./icons/flag.mjs'),
  "flame": () => import('./icons/flame.mjs'),
  "flashlight-off": () => import('./icons/flashlight-off.mjs'),
  "flashlight": () => import('./icons/flashlight.mjs'),
  "flask-conical-off": () => import('./icons/flask-conical-off.mjs'),
  "flask-conical": () => import('./icons/flask-conical.mjs'),
  "flask-round": () => import('./icons/flask-round.mjs'),
  "flip-horizontal-2": () => import('./icons/flip-horizontal-2.mjs'),
  "flip-horizontal": () => import('./icons/flip-horizontal.mjs'),
  "flip-vertical-2": () => import('./icons/flip-vertical-2.mjs'),
  "flip-vertical": () => import('./icons/flip-vertical.mjs'),
  "flower-2": () => import('./icons/flower-2.mjs'),
  "flower": () => import('./icons/flower.mjs'),
  "focus": () => import('./icons/focus.mjs'),
  "fold-horizontal": () => import('./icons/fold-horizontal.mjs'),
  "fold-vertical": () => import('./icons/fold-vertical.mjs'),
  "folder-archive": () => import('./icons/folder-archive.mjs'),
  "folder-check": () => import('./icons/folder-check.mjs'),
  "folder-clock": () => import('./icons/folder-clock.mjs'),
  "folder-closed": () => import('./icons/folder-closed.mjs'),
  "folder-cog-2": () => import('./icons/folder-cog-2.mjs'),
  "folder-cog": () => import('./icons/folder-cog.mjs'),
  "folder-dot": () => import('./icons/folder-dot.mjs'),
  "folder-down": () => import('./icons/folder-down.mjs'),
  "folder-edit": () => import('./icons/folder-edit.mjs'),
  "folder-git-2": () => import('./icons/folder-git-2.mjs'),
  "folder-git": () => import('./icons/folder-git.mjs'),
  "folder-heart": () => import('./icons/folder-heart.mjs'),
  "folder-input": () => import('./icons/folder-input.mjs'),
  "folder-kanban": () => import('./icons/folder-kanban.mjs'),
  "folder-key": () => import('./icons/folder-key.mjs'),
  "folder-lock": () => import('./icons/folder-lock.mjs'),
  "folder-minus": () => import('./icons/folder-minus.mjs'),
  "folder-open-dot": () => import('./icons/folder-open-dot.mjs'),
  "folder-open": () => import('./icons/folder-open.mjs'),
  "folder-output": () => import('./icons/folder-output.mjs'),
  "folder-plus": () => import('./icons/folder-plus.mjs'),
  "folder-root": () => import('./icons/folder-root.mjs'),
  "folder-search-2": () => import('./icons/folder-search-2.mjs'),
  "folder-search": () => import('./icons/folder-search.mjs'),
  "folder-symlink": () => import('./icons/folder-symlink.mjs'),
  "folder-sync": () => import('./icons/folder-sync.mjs'),
  "folder-tree": () => import('./icons/folder-tree.mjs'),
  "folder-up": () => import('./icons/folder-up.mjs'),
  "folder-x": () => import('./icons/folder-x.mjs'),
  "folder": () => import('./icons/folder.mjs'),
  "folders": () => import('./icons/folders.mjs'),
  "footprints": () => import('./icons/footprints.mjs'),
  "forklift": () => import('./icons/forklift.mjs'),
  "form-input": () => import('./icons/form-input.mjs'),
  "forward": () => import('./icons/forward.mjs'),
  "frame": () => import('./icons/frame.mjs'),
  "framer": () => import('./icons/framer.mjs'),
  "frown": () => import('./icons/frown.mjs'),
  "fuel": () => import('./icons/fuel.mjs'),
  "function-square": () => import('./icons/function-square.mjs'),
  "gallery-horizontal-end": () => import('./icons/gallery-horizontal-end.mjs'),
  "gallery-horizontal": () => import('./icons/gallery-horizontal.mjs'),
  "gallery-thumbnails": () => import('./icons/gallery-thumbnails.mjs'),
  "gallery-vertical-end": () => import('./icons/gallery-vertical-end.mjs'),
  "gallery-vertical": () => import('./icons/gallery-vertical.mjs'),
  "gamepad-2": () => import('./icons/gamepad-2.mjs'),
  "gamepad": () => import('./icons/gamepad.mjs'),
  "gantt-chart-square": () => import('./icons/gantt-chart-square.mjs'),
  "gantt-chart": () => import('./icons/gantt-chart.mjs'),
  "gauge-circle": () => import('./icons/gauge-circle.mjs'),
  "gauge": () => import('./icons/gauge.mjs'),
  "gavel": () => import('./icons/gavel.mjs'),
  "gem": () => import('./icons/gem.mjs'),
  "ghost": () => import('./icons/ghost.mjs'),
  "gift": () => import('./icons/gift.mjs'),
  "git-branch-plus": () => import('./icons/git-branch-plus.mjs'),
  "git-branch": () => import('./icons/git-branch.mjs'),
  "git-commit": () => import('./icons/git-commit.mjs'),
  "git-compare": () => import('./icons/git-compare.mjs'),
  "git-fork": () => import('./icons/git-fork.mjs'),
  "git-merge": () => import('./icons/git-merge.mjs'),
  "git-pull-request-closed": () => import('./icons/git-pull-request-closed.mjs'),
  "git-pull-request-draft": () => import('./icons/git-pull-request-draft.mjs'),
  "git-pull-request": () => import('./icons/git-pull-request.mjs'),
  "github": () => import('./icons/github.mjs'),
  "gitlab": () => import('./icons/gitlab.mjs'),
  "glass-water": () => import('./icons/glass-water.mjs'),
  "glasses": () => import('./icons/glasses.mjs'),
  "globe-2": () => import('./icons/globe-2.mjs'),
  "globe": () => import('./icons/globe.mjs'),
  "goal": () => import('./icons/goal.mjs'),
  "grab": () => import('./icons/grab.mjs'),
  "graduation-cap": () => import('./icons/graduation-cap.mjs'),
  "grape": () => import('./icons/grape.mjs'),
  "grid": () => import('./icons/grid.mjs'),
  "grip-horizontal": () => import('./icons/grip-horizontal.mjs'),
  "grip-vertical": () => import('./icons/grip-vertical.mjs'),
  "grip": () => import('./icons/grip.mjs'),
  "group": () => import('./icons/group.mjs'),
  "hammer": () => import('./icons/hammer.mjs'),
  "hand-metal": () => import('./icons/hand-metal.mjs'),
  "hand": () => import('./icons/hand.mjs'),
  "hard-drive-download": () => import('./icons/hard-drive-download.mjs'),
  "hard-drive-upload": () => import('./icons/hard-drive-upload.mjs'),
  "hard-drive": () => import('./icons/hard-drive.mjs'),
  "hard-hat": () => import('./icons/hard-hat.mjs'),
  "hash": () => import('./icons/hash.mjs'),
  "haze": () => import('./icons/haze.mjs'),
  "hdmi-port": () => import('./icons/hdmi-port.mjs'),
  "heading-1": () => import('./icons/heading-1.mjs'),
  "heading-2": () => import('./icons/heading-2.mjs'),
  "heading-3": () => import('./icons/heading-3.mjs'),
  "heading-4": () => import('./icons/heading-4.mjs'),
  "heading-5": () => import('./icons/heading-5.mjs'),
  "heading-6": () => import('./icons/heading-6.mjs'),
  "heading": () => import('./icons/heading.mjs'),
  "headphones": () => import('./icons/headphones.mjs'),
  "heart-crack": () => import('./icons/heart-crack.mjs'),
  "heart-handshake": () => import('./icons/heart-handshake.mjs'),
  "heart-off": () => import('./icons/heart-off.mjs'),
  "heart-pulse": () => import('./icons/heart-pulse.mjs'),
  "heart": () => import('./icons/heart.mjs'),
  "help-circle": () => import('./icons/help-circle.mjs'),
  "helping-hand": () => import('./icons/helping-hand.mjs'),
  "hexagon": () => import('./icons/hexagon.mjs'),
  "highlighter": () => import('./icons/highlighter.mjs'),
  "history": () => import('./icons/history.mjs'),
  "home": () => import('./icons/home.mjs'),
  "hop-off": () => import('./icons/hop-off.mjs'),
  "hop": () => import('./icons/hop.mjs'),
  "hotel": () => import('./icons/hotel.mjs'),
  "hourglass": () => import('./icons/hourglass.mjs'),
  "ice-cream-2": () => import('./icons/ice-cream-2.mjs'),
  "ice-cream": () => import('./icons/ice-cream.mjs'),
  "image-minus": () => import('./icons/image-minus.mjs'),
  "image-off": () => import('./icons/image-off.mjs'),
  "image-plus": () => import('./icons/image-plus.mjs'),
  "image": () => import('./icons/image.mjs'),
  "import": () => import('./icons/import.mjs'),
  "inbox": () => import('./icons/inbox.mjs'),
  "indent": () => import('./icons/indent.mjs'),
  "indian-rupee": () => import('./icons/indian-rupee.mjs'),
  "infinity": () => import('./icons/infinity.mjs'),
  "info": () => import('./icons/info.mjs'),
  "inspect": () => import('./icons/inspect.mjs'),
  "instagram": () => import('./icons/instagram.mjs'),
  "italic": () => import('./icons/italic.mjs'),
  "iteration-ccw": () => import('./icons/iteration-ccw.mjs'),
  "iteration-cw": () => import('./icons/iteration-cw.mjs'),
  "japanese-yen": () => import('./icons/japanese-yen.mjs'),
  "joystick": () => import('./icons/joystick.mjs'),
  "kanban-square-dashed": () => import('./icons/kanban-square-dashed.mjs'),
  "kanban-square": () => import('./icons/kanban-square.mjs'),
  "kanban": () => import('./icons/kanban.mjs'),
  "key-round": () => import('./icons/key-round.mjs'),
  "key-square": () => import('./icons/key-square.mjs'),
  "key": () => import('./icons/key.mjs'),
  "keyboard": () => import('./icons/keyboard.mjs'),
  "lamp-ceiling": () => import('./icons/lamp-ceiling.mjs'),
  "lamp-desk": () => import('./icons/lamp-desk.mjs'),
  "lamp-floor": () => import('./icons/lamp-floor.mjs'),
  "lamp-wall-down": () => import('./icons/lamp-wall-down.mjs'),
  "lamp-wall-up": () => import('./icons/lamp-wall-up.mjs'),
  "lamp": () => import('./icons/lamp.mjs'),
  "landmark": () => import('./icons/landmark.mjs'),
  "languages": () => import('./icons/languages.mjs'),
  "laptop-2": () => import('./icons/laptop-2.mjs'),
  "laptop": () => import('./icons/laptop.mjs'),
  "lasso-select": () => import('./icons/lasso-select.mjs'),
  "lasso": () => import('./icons/lasso.mjs'),
  "laugh": () => import('./icons/laugh.mjs'),
  "layers": () => import('./icons/layers.mjs'),
  "layout-dashboard": () => import('./icons/layout-dashboard.mjs'),
  "layout-grid": () => import('./icons/layout-grid.mjs'),
  "layout-list": () => import('./icons/layout-list.mjs'),
  "layout-panel-left": () => import('./icons/layout-panel-left.mjs'),
  "layout-panel-top": () => import('./icons/layout-panel-top.mjs'),
  "layout-template": () => import('./icons/layout-template.mjs'),
  "layout": () => import('./icons/layout.mjs'),
  "leaf": () => import('./icons/leaf.mjs'),
  "leafy-green": () => import('./icons/leafy-green.mjs'),
  "library": () => import('./icons/library.mjs'),
  "life-buoy": () => import('./icons/life-buoy.mjs'),
  "ligature": () => import('./icons/ligature.mjs'),
  "lightbulb-off": () => import('./icons/lightbulb-off.mjs'),
  "lightbulb": () => import('./icons/lightbulb.mjs'),
  "line-chart": () => import('./icons/line-chart.mjs'),
  "link-2-off": () => import('./icons/link-2-off.mjs'),
  "link-2": () => import('./icons/link-2.mjs'),
  "link": () => import('./icons/link.mjs'),
  "linkedin": () => import('./icons/linkedin.mjs'),
  "list-checks": () => import('./icons/list-checks.mjs'),
  "list-end": () => import('./icons/list-end.mjs'),
  "list-filter": () => import('./icons/list-filter.mjs'),
  "list-minus": () => import('./icons/list-minus.mjs'),
  "list-music": () => import('./icons/list-music.mjs'),
  "list-ordered": () => import('./icons/list-ordered.mjs'),
  "list-plus": () => import('./icons/list-plus.mjs'),
  "list-restart": () => import('./icons/list-restart.mjs'),
  "list-start": () => import('./icons/list-start.mjs'),
  "list-todo": () => import('./icons/list-todo.mjs'),
  "list-tree": () => import('./icons/list-tree.mjs'),
  "list-video": () => import('./icons/list-video.mjs'),
  "list-x": () => import('./icons/list-x.mjs'),
  "list": () => import('./icons/list.mjs'),
  "loader-2": () => import('./icons/loader-2.mjs'),
  "loader": () => import('./icons/loader.mjs'),
  "locate-fixed": () => import('./icons/locate-fixed.mjs'),
  "locate-off": () => import('./icons/locate-off.mjs'),
  "locate": () => import('./icons/locate.mjs'),
  "lock": () => import('./icons/lock.mjs'),
  "log-in": () => import('./icons/log-in.mjs'),
  "log-out": () => import('./icons/log-out.mjs'),
  "lollipop": () => import('./icons/lollipop.mjs'),
  "luggage": () => import('./icons/luggage.mjs'),
  "magnet": () => import('./icons/magnet.mjs'),
  "mail-check": () => import('./icons/mail-check.mjs'),
  "mail-minus": () => import('./icons/mail-minus.mjs'),
  "mail-open": () => import('./icons/mail-open.mjs'),
  "mail-plus": () => import('./icons/mail-plus.mjs'),
  "mail-question": () => import('./icons/mail-question.mjs'),
  "mail-search": () => import('./icons/mail-search.mjs'),
  "mail-warning": () => import('./icons/mail-warning.mjs'),
  "mail-x": () => import('./icons/mail-x.mjs'),
  "mail": () => import('./icons/mail.mjs'),
  "mailbox": () => import('./icons/mailbox.mjs'),
  "mails": () => import('./icons/mails.mjs'),
  "map-pin-off": () => import('./icons/map-pin-off.mjs'),
  "map-pin": () => import('./icons/map-pin.mjs'),
  "map": () => import('./icons/map.mjs'),
  "martini": () => import('./icons/martini.mjs'),
  "maximize-2": () => import('./icons/maximize-2.mjs'),
  "maximize": () => import('./icons/maximize.mjs'),
  "medal": () => import('./icons/medal.mjs'),
  "megaphone-off": () => import('./icons/megaphone-off.mjs'),
  "megaphone": () => import('./icons/megaphone.mjs'),
  "meh": () => import('./icons/meh.mjs'),
  "memory-stick": () => import('./icons/memory-stick.mjs'),
  "menu-square": () => import('./icons/menu-square.mjs'),
  "menu": () => import('./icons/menu.mjs'),
  "merge": () => import('./icons/merge.mjs'),
  "message-circle": () => import('./icons/message-circle.mjs'),
  "message-square-dashed": () => import('./icons/message-square-dashed.mjs'),
  "message-square-plus": () => import('./icons/message-square-plus.mjs'),
  "message-square": () => import('./icons/message-square.mjs'),
  "messages-square": () => import('./icons/messages-square.mjs'),
  "mic-2": () => import('./icons/mic-2.mjs'),
  "mic-off": () => import('./icons/mic-off.mjs'),
  "mic": () => import('./icons/mic.mjs'),
  "microscope": () => import('./icons/microscope.mjs'),
  "microwave": () => import('./icons/microwave.mjs'),
  "milestone": () => import('./icons/milestone.mjs'),
  "milk-off": () => import('./icons/milk-off.mjs'),
  "milk": () => import('./icons/milk.mjs'),
  "minimize-2": () => import('./icons/minimize-2.mjs'),
  "minimize": () => import('./icons/minimize.mjs'),
  "minus-circle": () => import('./icons/minus-circle.mjs'),
  "minus-square": () => import('./icons/minus-square.mjs'),
  "minus": () => import('./icons/minus.mjs'),
  "monitor-check": () => import('./icons/monitor-check.mjs'),
  "monitor-dot": () => import('./icons/monitor-dot.mjs'),
  "monitor-down": () => import('./icons/monitor-down.mjs'),
  "monitor-off": () => import('./icons/monitor-off.mjs'),
  "monitor-pause": () => import('./icons/monitor-pause.mjs'),
  "monitor-play": () => import('./icons/monitor-play.mjs'),
  "monitor-smartphone": () => import('./icons/monitor-smartphone.mjs'),
  "monitor-speaker": () => import('./icons/monitor-speaker.mjs'),
  "monitor-stop": () => import('./icons/monitor-stop.mjs'),
  "monitor-up": () => import('./icons/monitor-up.mjs'),
  "monitor-x": () => import('./icons/monitor-x.mjs'),
  "monitor": () => import('./icons/monitor.mjs'),
  "moon-star": () => import('./icons/moon-star.mjs'),
  "moon": () => import('./icons/moon.mjs'),
  "more-horizontal": () => import('./icons/more-horizontal.mjs'),
  "more-vertical": () => import('./icons/more-vertical.mjs'),
  "mountain-snow": () => import('./icons/mountain-snow.mjs'),
  "mountain": () => import('./icons/mountain.mjs'),
  "mouse-pointer-2": () => import('./icons/mouse-pointer-2.mjs'),
  "mouse-pointer-click": () => import('./icons/mouse-pointer-click.mjs'),
  "mouse-pointer": () => import('./icons/mouse-pointer.mjs'),
  "mouse": () => import('./icons/mouse.mjs'),
  "move-3d": () => import('./icons/move-3d.mjs'),
  "move-diagonal-2": () => import('./icons/move-diagonal-2.mjs'),
  "move-diagonal": () => import('./icons/move-diagonal.mjs'),
  "move-down-left": () => import('./icons/move-down-left.mjs'),
  "move-down-right": () => import('./icons/move-down-right.mjs'),
  "move-down": () => import('./icons/move-down.mjs'),
  "move-horizontal": () => import('./icons/move-horizontal.mjs'),
  "move-left": () => import('./icons/move-left.mjs'),
  "move-right": () => import('./icons/move-right.mjs'),
  "move-up-left": () => import('./icons/move-up-left.mjs'),
  "move-up-right": () => import('./icons/move-up-right.mjs'),
  "move-up": () => import('./icons/move-up.mjs'),
  "move-vertical": () => import('./icons/move-vertical.mjs'),
  "move": () => import('./icons/move.mjs'),
  "music-2": () => import('./icons/music-2.mjs'),
  "music-3": () => import('./icons/music-3.mjs'),
  "music-4": () => import('./icons/music-4.mjs'),
  "music": () => import('./icons/music.mjs'),
  "navigation-2-off": () => import('./icons/navigation-2-off.mjs'),
  "navigation-2": () => import('./icons/navigation-2.mjs'),
  "navigation-off": () => import('./icons/navigation-off.mjs'),
  "navigation": () => import('./icons/navigation.mjs'),
  "network": () => import('./icons/network.mjs'),
  "newspaper": () => import('./icons/newspaper.mjs'),
  "nfc": () => import('./icons/nfc.mjs'),
  "nut-off": () => import('./icons/nut-off.mjs'),
  "nut": () => import('./icons/nut.mjs'),
  "octagon": () => import('./icons/octagon.mjs'),
  "option": () => import('./icons/option.mjs'),
  "orbit": () => import('./icons/orbit.mjs'),
  "outdent": () => import('./icons/outdent.mjs'),
  "package-2": () => import('./icons/package-2.mjs'),
  "package-check": () => import('./icons/package-check.mjs'),
  "package-minus": () => import('./icons/package-minus.mjs'),
  "package-open": () => import('./icons/package-open.mjs'),
  "package-plus": () => import('./icons/package-plus.mjs'),
  "package-search": () => import('./icons/package-search.mjs'),
  "package-x": () => import('./icons/package-x.mjs'),
  "package": () => import('./icons/package.mjs'),
  "paint-bucket": () => import('./icons/paint-bucket.mjs'),
  "paintbrush-2": () => import('./icons/paintbrush-2.mjs'),
  "paintbrush": () => import('./icons/paintbrush.mjs'),
  "palette": () => import('./icons/palette.mjs'),
  "palmtree": () => import('./icons/palmtree.mjs'),
  "panel-bottom-close": () => import('./icons/panel-bottom-close.mjs'),
  "panel-bottom-inactive": () => import('./icons/panel-bottom-inactive.mjs'),
  "panel-bottom-open": () => import('./icons/panel-bottom-open.mjs'),
  "panel-bottom": () => import('./icons/panel-bottom.mjs'),
  "panel-left-close": () => import('./icons/panel-left-close.mjs'),
  "panel-left-inactive": () => import('./icons/panel-left-inactive.mjs'),
  "panel-left-open": () => import('./icons/panel-left-open.mjs'),
  "panel-left": () => import('./icons/panel-left.mjs'),
  "panel-right-close": () => import('./icons/panel-right-close.mjs'),
  "panel-right-inactive": () => import('./icons/panel-right-inactive.mjs'),
  "panel-right-open": () => import('./icons/panel-right-open.mjs'),
  "panel-right": () => import('./icons/panel-right.mjs'),
  "panel-top-close": () => import('./icons/panel-top-close.mjs'),
  "panel-top-inactive": () => import('./icons/panel-top-inactive.mjs'),
  "panel-top-open": () => import('./icons/panel-top-open.mjs'),
  "panel-top": () => import('./icons/panel-top.mjs'),
  "paperclip": () => import('./icons/paperclip.mjs'),
  "parentheses": () => import('./icons/parentheses.mjs'),
  "parking-circle-off": () => import('./icons/parking-circle-off.mjs'),
  "parking-circle": () => import('./icons/parking-circle.mjs'),
  "parking-square-off": () => import('./icons/parking-square-off.mjs'),
  "parking-square": () => import('./icons/parking-square.mjs'),
  "party-popper": () => import('./icons/party-popper.mjs'),
  "pause-circle": () => import('./icons/pause-circle.mjs'),
  "pause-octagon": () => import('./icons/pause-octagon.mjs'),
  "pause": () => import('./icons/pause.mjs'),
  "pc-case": () => import('./icons/pc-case.mjs'),
  "pen-line": () => import('./icons/pen-line.mjs'),
  "pen-square": () => import('./icons/pen-square.mjs'),
  "pen-tool": () => import('./icons/pen-tool.mjs'),
  "pen": () => import('./icons/pen.mjs'),
  "pencil-line": () => import('./icons/pencil-line.mjs'),
  "pencil-ruler": () => import('./icons/pencil-ruler.mjs'),
  "pencil": () => import('./icons/pencil.mjs'),
  "percent": () => import('./icons/percent.mjs'),
  "person-standing": () => import('./icons/person-standing.mjs'),
  "phone-call": () => import('./icons/phone-call.mjs'),
  "phone-forwarded": () => import('./icons/phone-forwarded.mjs'),
  "phone-incoming": () => import('./icons/phone-incoming.mjs'),
  "phone-missed": () => import('./icons/phone-missed.mjs'),
  "phone-off": () => import('./icons/phone-off.mjs'),
  "phone-outgoing": () => import('./icons/phone-outgoing.mjs'),
  "phone": () => import('./icons/phone.mjs'),
  "pi-square": () => import('./icons/pi-square.mjs'),
  "pi": () => import('./icons/pi.mjs'),
  "picture-in-picture-2": () => import('./icons/picture-in-picture-2.mjs'),
  "picture-in-picture": () => import('./icons/picture-in-picture.mjs'),
  "pie-chart": () => import('./icons/pie-chart.mjs'),
  "piggy-bank": () => import('./icons/piggy-bank.mjs'),
  "pilcrow-square": () => import('./icons/pilcrow-square.mjs'),
  "pilcrow": () => import('./icons/pilcrow.mjs'),
  "pill": () => import('./icons/pill.mjs'),
  "pin-off": () => import('./icons/pin-off.mjs'),
  "pin": () => import('./icons/pin.mjs'),
  "pipette": () => import('./icons/pipette.mjs'),
  "pizza": () => import('./icons/pizza.mjs'),
  "plane-landing": () => import('./icons/plane-landing.mjs'),
  "plane-takeoff": () => import('./icons/plane-takeoff.mjs'),
  "plane": () => import('./icons/plane.mjs'),
  "play-circle": () => import('./icons/play-circle.mjs'),
  "play-square": () => import('./icons/play-square.mjs'),
  "play": () => import('./icons/play.mjs'),
  "plug-2": () => import('./icons/plug-2.mjs'),
  "plug-zap-2": () => import('./icons/plug-zap-2.mjs'),
  "plug-zap": () => import('./icons/plug-zap.mjs'),
  "plug": () => import('./icons/plug.mjs'),
  "plus-circle": () => import('./icons/plus-circle.mjs'),
  "plus-square": () => import('./icons/plus-square.mjs'),
  "plus": () => import('./icons/plus.mjs'),
  "pocket-knife": () => import('./icons/pocket-knife.mjs'),
  "pocket": () => import('./icons/pocket.mjs'),
  "podcast": () => import('./icons/podcast.mjs'),
  "pointer": () => import('./icons/pointer.mjs'),
  "popcorn": () => import('./icons/popcorn.mjs'),
  "popsicle": () => import('./icons/popsicle.mjs'),
  "pound-sterling": () => import('./icons/pound-sterling.mjs'),
  "power-off": () => import('./icons/power-off.mjs'),
  "power": () => import('./icons/power.mjs'),
  "presentation": () => import('./icons/presentation.mjs'),
  "printer": () => import('./icons/printer.mjs'),
  "projector": () => import('./icons/projector.mjs'),
  "puzzle": () => import('./icons/puzzle.mjs'),
  "qr-code": () => import('./icons/qr-code.mjs'),
  "quote": () => import('./icons/quote.mjs'),
  "radar": () => import('./icons/radar.mjs'),
  "radiation": () => import('./icons/radiation.mjs'),
  "radio-receiver": () => import('./icons/radio-receiver.mjs'),
  "radio-tower": () => import('./icons/radio-tower.mjs'),
  "radio": () => import('./icons/radio.mjs'),
  "rainbow": () => import('./icons/rainbow.mjs'),
  "rat": () => import('./icons/rat.mjs'),
  "ratio": () => import('./icons/ratio.mjs'),
  "receipt": () => import('./icons/receipt.mjs'),
  "rectangle-horizontal": () => import('./icons/rectangle-horizontal.mjs'),
  "rectangle-vertical": () => import('./icons/rectangle-vertical.mjs'),
  "recycle": () => import('./icons/recycle.mjs'),
  "redo-2": () => import('./icons/redo-2.mjs'),
  "redo-dot": () => import('./icons/redo-dot.mjs'),
  "redo": () => import('./icons/redo.mjs'),
  "refresh-ccw-dot": () => import('./icons/refresh-ccw-dot.mjs'),
  "refresh-ccw": () => import('./icons/refresh-ccw.mjs'),
  "refresh-cw-off": () => import('./icons/refresh-cw-off.mjs'),
  "refresh-cw": () => import('./icons/refresh-cw.mjs'),
  "refrigerator": () => import('./icons/refrigerator.mjs'),
  "regex": () => import('./icons/regex.mjs'),
  "remove-formatting": () => import('./icons/remove-formatting.mjs'),
  "repeat-1": () => import('./icons/repeat-1.mjs'),
  "repeat-2": () => import('./icons/repeat-2.mjs'),
  "repeat": () => import('./icons/repeat.mjs'),
  "replace-all": () => import('./icons/replace-all.mjs'),
  "replace": () => import('./icons/replace.mjs'),
  "reply-all": () => import('./icons/reply-all.mjs'),
  "reply": () => import('./icons/reply.mjs'),
  "rewind": () => import('./icons/rewind.mjs'),
  "rocket": () => import('./icons/rocket.mjs'),
  "rocking-chair": () => import('./icons/rocking-chair.mjs'),
  "roller-coaster": () => import('./icons/roller-coaster.mjs'),
  "rotate-3d": () => import('./icons/rotate-3d.mjs'),
  "rotate-ccw": () => import('./icons/rotate-ccw.mjs'),
  "rotate-cw": () => import('./icons/rotate-cw.mjs'),
  "router": () => import('./icons/router.mjs'),
  "rows": () => import('./icons/rows.mjs'),
  "rss": () => import('./icons/rss.mjs'),
  "ruler": () => import('./icons/ruler.mjs'),
  "russian-ruble": () => import('./icons/russian-ruble.mjs'),
  "sailboat": () => import('./icons/sailboat.mjs'),
  "salad": () => import('./icons/salad.mjs'),
  "sandwich": () => import('./icons/sandwich.mjs'),
  "satellite-dish": () => import('./icons/satellite-dish.mjs'),
  "satellite": () => import('./icons/satellite.mjs'),
  "save-all": () => import('./icons/save-all.mjs'),
  "save": () => import('./icons/save.mjs'),
  "scale-3d": () => import('./icons/scale-3d.mjs'),
  "scale": () => import('./icons/scale.mjs'),
  "scaling": () => import('./icons/scaling.mjs'),
  "scan-face": () => import('./icons/scan-face.mjs'),
  "scan-line": () => import('./icons/scan-line.mjs'),
  "scan": () => import('./icons/scan.mjs'),
  "scatter-chart": () => import('./icons/scatter-chart.mjs'),
  "school-2": () => import('./icons/school-2.mjs'),
  "school": () => import('./icons/school.mjs'),
  "scissors-line-dashed": () => import('./icons/scissors-line-dashed.mjs'),
  "scissors-square-dashed-bottom": () => import('./icons/scissors-square-dashed-bottom.mjs'),
  "scissors-square": () => import('./icons/scissors-square.mjs'),
  "scissors": () => import('./icons/scissors.mjs'),
  "screen-share-off": () => import('./icons/screen-share-off.mjs'),
  "screen-share": () => import('./icons/screen-share.mjs'),
  "scroll-text": () => import('./icons/scroll-text.mjs'),
  "scroll": () => import('./icons/scroll.mjs'),
  "search-check": () => import('./icons/search-check.mjs'),
  "search-code": () => import('./icons/search-code.mjs'),
  "search-slash": () => import('./icons/search-slash.mjs'),
  "search-x": () => import('./icons/search-x.mjs'),
  "search": () => import('./icons/search.mjs'),
  "send-horizonal": () => import('./icons/send-horizonal.mjs'),
  "send-to-back": () => import('./icons/send-to-back.mjs'),
  "send": () => import('./icons/send.mjs'),
  "separator-horizontal": () => import('./icons/separator-horizontal.mjs'),
  "separator-vertical": () => import('./icons/separator-vertical.mjs'),
  "server-cog": () => import('./icons/server-cog.mjs'),
  "server-crash": () => import('./icons/server-crash.mjs'),
  "server-off": () => import('./icons/server-off.mjs'),
  "server": () => import('./icons/server.mjs'),
  "settings-2": () => import('./icons/settings-2.mjs'),
  "settings": () => import('./icons/settings.mjs'),
  "shapes": () => import('./icons/shapes.mjs'),
  "share-2": () => import('./icons/share-2.mjs'),
  "share": () => import('./icons/share.mjs'),
  "sheet": () => import('./icons/sheet.mjs'),
  "shield-alert": () => import('./icons/shield-alert.mjs'),
  "shield-check": () => import('./icons/shield-check.mjs'),
  "shield-close": () => import('./icons/shield-close.mjs'),
  "shield-off": () => import('./icons/shield-off.mjs'),
  "shield-question": () => import('./icons/shield-question.mjs'),
  "shield": () => import('./icons/shield.mjs'),
  "ship": () => import('./icons/ship.mjs'),
  "shirt": () => import('./icons/shirt.mjs'),
  "shopping-bag": () => import('./icons/shopping-bag.mjs'),
  "shopping-basket": () => import('./icons/shopping-basket.mjs'),
  "shopping-cart": () => import('./icons/shopping-cart.mjs'),
  "shovel": () => import('./icons/shovel.mjs'),
  "shower-head": () => import('./icons/shower-head.mjs'),
  "shrink": () => import('./icons/shrink.mjs'),
  "shrub": () => import('./icons/shrub.mjs'),
  "shuffle": () => import('./icons/shuffle.mjs'),
  "sigma-square": () => import('./icons/sigma-square.mjs'),
  "sigma": () => import('./icons/sigma.mjs'),
  "signal-high": () => import('./icons/signal-high.mjs'),
  "signal-low": () => import('./icons/signal-low.mjs'),
  "signal-medium": () => import('./icons/signal-medium.mjs'),
  "signal-zero": () => import('./icons/signal-zero.mjs'),
  "signal": () => import('./icons/signal.mjs'),
  "siren": () => import('./icons/siren.mjs'),
  "skip-back": () => import('./icons/skip-back.mjs'),
  "skip-forward": () => import('./icons/skip-forward.mjs'),
  "skull": () => import('./icons/skull.mjs'),
  "slack": () => import('./icons/slack.mjs'),
  "slice": () => import('./icons/slice.mjs'),
  "sliders-horizontal": () => import('./icons/sliders-horizontal.mjs'),
  "sliders": () => import('./icons/sliders.mjs'),
  "smartphone-charging": () => import('./icons/smartphone-charging.mjs'),
  "smartphone-nfc": () => import('./icons/smartphone-nfc.mjs'),
  "smartphone": () => import('./icons/smartphone.mjs'),
  "smile-plus": () => import('./icons/smile-plus.mjs'),
  "smile": () => import('./icons/smile.mjs'),
  "snowflake": () => import('./icons/snowflake.mjs'),
  "sofa": () => import('./icons/sofa.mjs'),
  "soup": () => import('./icons/soup.mjs'),
  "space": () => import('./icons/space.mjs'),
  "spade": () => import('./icons/spade.mjs'),
  "sparkle": () => import('./icons/sparkle.mjs'),
  "sparkles": () => import('./icons/sparkles.mjs'),
  "speaker": () => import('./icons/speaker.mjs'),
  "spell-check-2": () => import('./icons/spell-check-2.mjs'),
  "spell-check": () => import('./icons/spell-check.mjs'),
  "spline": () => import('./icons/spline.mjs'),
  "split-square-horizontal": () => import('./icons/split-square-horizontal.mjs'),
  "split-square-vertical": () => import('./icons/split-square-vertical.mjs'),
  "split": () => import('./icons/split.mjs'),
  "spray-can": () => import('./icons/spray-can.mjs'),
  "sprout": () => import('./icons/sprout.mjs'),
  "square-asterisk": () => import('./icons/square-asterisk.mjs'),
  "square-code": () => import('./icons/square-code.mjs'),
  "square-dashed-bottom-code": () => import('./icons/square-dashed-bottom-code.mjs'),
  "square-dashed-bottom": () => import('./icons/square-dashed-bottom.mjs'),
  "square-dot": () => import('./icons/square-dot.mjs'),
  "square-equal": () => import('./icons/square-equal.mjs'),
  "square-slash": () => import('./icons/square-slash.mjs'),
  "square-stack": () => import('./icons/square-stack.mjs'),
  "square": () => import('./icons/square.mjs'),
  "squirrel": () => import('./icons/squirrel.mjs'),
  "stamp": () => import('./icons/stamp.mjs'),
  "star-half": () => import('./icons/star-half.mjs'),
  "star-off": () => import('./icons/star-off.mjs'),
  "star": () => import('./icons/star.mjs'),
  "step-back": () => import('./icons/step-back.mjs'),
  "step-forward": () => import('./icons/step-forward.mjs'),
  "stethoscope": () => import('./icons/stethoscope.mjs'),
  "sticker": () => import('./icons/sticker.mjs'),
  "sticky-note": () => import('./icons/sticky-note.mjs'),
  "stop-circle": () => import('./icons/stop-circle.mjs'),
  "store": () => import('./icons/store.mjs'),
  "stretch-horizontal": () => import('./icons/stretch-horizontal.mjs'),
  "stretch-vertical": () => import('./icons/stretch-vertical.mjs'),
  "strikethrough": () => import('./icons/strikethrough.mjs'),
  "subscript": () => import('./icons/subscript.mjs'),
  "subtitles": () => import('./icons/subtitles.mjs'),
  "sun-dim": () => import('./icons/sun-dim.mjs'),
  "sun-medium": () => import('./icons/sun-medium.mjs'),
  "sun-moon": () => import('./icons/sun-moon.mjs'),
  "sun-snow": () => import('./icons/sun-snow.mjs'),
  "sun": () => import('./icons/sun.mjs'),
  "sunrise": () => import('./icons/sunrise.mjs'),
  "sunset": () => import('./icons/sunset.mjs'),
  "superscript": () => import('./icons/superscript.mjs'),
  "swiss-franc": () => import('./icons/swiss-franc.mjs'),
  "switch-camera": () => import('./icons/switch-camera.mjs'),
  "sword": () => import('./icons/sword.mjs'),
  "swords": () => import('./icons/swords.mjs'),
  "syringe": () => import('./icons/syringe.mjs'),
  "table-2": () => import('./icons/table-2.mjs'),
  "table-properties": () => import('./icons/table-properties.mjs'),
  "table": () => import('./icons/table.mjs'),
  "tablet": () => import('./icons/tablet.mjs'),
  "tablets": () => import('./icons/tablets.mjs'),
  "tag": () => import('./icons/tag.mjs'),
  "tags": () => import('./icons/tags.mjs'),
  "tally-1": () => import('./icons/tally-1.mjs'),
  "tally-2": () => import('./icons/tally-2.mjs'),
  "tally-3": () => import('./icons/tally-3.mjs'),
  "tally-4": () => import('./icons/tally-4.mjs'),
  "tally-5": () => import('./icons/tally-5.mjs'),
  "target": () => import('./icons/target.mjs'),
  "tent": () => import('./icons/tent.mjs'),
  "terminal-square": () => import('./icons/terminal-square.mjs'),
  "terminal": () => import('./icons/terminal.mjs'),
  "test-tube-2": () => import('./icons/test-tube-2.mjs'),
  "test-tube": () => import('./icons/test-tube.mjs'),
  "test-tubes": () => import('./icons/test-tubes.mjs'),
  "text-cursor-input": () => import('./icons/text-cursor-input.mjs'),
  "text-cursor": () => import('./icons/text-cursor.mjs'),
  "text-quote": () => import('./icons/text-quote.mjs'),
  "text-select": () => import('./icons/text-select.mjs'),
  "text": () => import('./icons/text.mjs'),
  "thermometer-snowflake": () => import('./icons/thermometer-snowflake.mjs'),
  "thermometer-sun": () => import('./icons/thermometer-sun.mjs'),
  "thermometer": () => import('./icons/thermometer.mjs'),
  "thumbs-down": () => import('./icons/thumbs-down.mjs'),
  "thumbs-up": () => import('./icons/thumbs-up.mjs'),
  "ticket": () => import('./icons/ticket.mjs'),
  "timer-off": () => import('./icons/timer-off.mjs'),
  "timer-reset": () => import('./icons/timer-reset.mjs'),
  "timer": () => import('./icons/timer.mjs'),
  "toggle-left": () => import('./icons/toggle-left.mjs'),
  "toggle-right": () => import('./icons/toggle-right.mjs'),
  "tornado": () => import('./icons/tornado.mjs'),
  "touchpad-off": () => import('./icons/touchpad-off.mjs'),
  "touchpad": () => import('./icons/touchpad.mjs'),
  "tower-control": () => import('./icons/tower-control.mjs'),
  "toy-brick": () => import('./icons/toy-brick.mjs'),
  "train": () => import('./icons/train.mjs'),
  "trash-2": () => import('./icons/trash-2.mjs'),
  "trash": () => import('./icons/trash.mjs'),
  "tree-deciduous": () => import('./icons/tree-deciduous.mjs'),
  "tree-pine": () => import('./icons/tree-pine.mjs'),
  "trees": () => import('./icons/trees.mjs'),
  "trello": () => import('./icons/trello.mjs'),
  "trending-down": () => import('./icons/trending-down.mjs'),
  "trending-up": () => import('./icons/trending-up.mjs'),
  "triangle-right": () => import('./icons/triangle-right.mjs'),
  "triangle": () => import('./icons/triangle.mjs'),
  "trophy": () => import('./icons/trophy.mjs'),
  "truck": () => import('./icons/truck.mjs'),
  "tv-2": () => import('./icons/tv-2.mjs'),
  "tv": () => import('./icons/tv.mjs'),
  "twitch": () => import('./icons/twitch.mjs'),
  "twitter": () => import('./icons/twitter.mjs'),
  "type": () => import('./icons/type.mjs'),
  "umbrella": () => import('./icons/umbrella.mjs'),
  "underline": () => import('./icons/underline.mjs'),
  "undo-2": () => import('./icons/undo-2.mjs'),
  "undo-dot": () => import('./icons/undo-dot.mjs'),
  "undo": () => import('./icons/undo.mjs'),
  "unfold-horizontal": () => import('./icons/unfold-horizontal.mjs'),
  "unfold-vertical": () => import('./icons/unfold-vertical.mjs'),
  "ungroup": () => import('./icons/ungroup.mjs'),
  "unlink-2": () => import('./icons/unlink-2.mjs'),
  "unlink": () => import('./icons/unlink.mjs'),
  "unlock": () => import('./icons/unlock.mjs'),
  "unplug": () => import('./icons/unplug.mjs'),
  "upload-cloud": () => import('./icons/upload-cloud.mjs'),
  "upload": () => import('./icons/upload.mjs'),
  "usb": () => import('./icons/usb.mjs'),
  "user-2": () => import('./icons/user-2.mjs'),
  "user-check-2": () => import('./icons/user-check-2.mjs'),
  "user-check": () => import('./icons/user-check.mjs'),
  "user-circle-2": () => import('./icons/user-circle-2.mjs'),
  "user-circle": () => import('./icons/user-circle.mjs'),
  "user-cog-2": () => import('./icons/user-cog-2.mjs'),
  "user-cog": () => import('./icons/user-cog.mjs'),
  "user-minus-2": () => import('./icons/user-minus-2.mjs'),
  "user-minus": () => import('./icons/user-minus.mjs'),
  "user-plus-2": () => import('./icons/user-plus-2.mjs'),
  "user-plus": () => import('./icons/user-plus.mjs'),
  "user-square-2": () => import('./icons/user-square-2.mjs'),
  "user-square": () => import('./icons/user-square.mjs'),
  "user-x-2": () => import('./icons/user-x-2.mjs'),
  "user-x": () => import('./icons/user-x.mjs'),
  "user": () => import('./icons/user.mjs'),
  "users-2": () => import('./icons/users-2.mjs'),
  "users": () => import('./icons/users.mjs'),
  "utensils-crossed": () => import('./icons/utensils-crossed.mjs'),
  "utensils": () => import('./icons/utensils.mjs'),
  "utility-pole": () => import('./icons/utility-pole.mjs'),
  "variable": () => import('./icons/variable.mjs'),
  "vegan": () => import('./icons/vegan.mjs'),
  "venetian-mask": () => import('./icons/venetian-mask.mjs'),
  "vibrate-off": () => import('./icons/vibrate-off.mjs'),
  "vibrate": () => import('./icons/vibrate.mjs'),
  "video-off": () => import('./icons/video-off.mjs'),
  "video": () => import('./icons/video.mjs'),
  "videotape": () => import('./icons/videotape.mjs'),
  "view": () => import('./icons/view.mjs'),
  "voicemail": () => import('./icons/voicemail.mjs'),
  "volume-1": () => import('./icons/volume-1.mjs'),
  "volume-2": () => import('./icons/volume-2.mjs'),
  "volume-x": () => import('./icons/volume-x.mjs'),
  "volume": () => import('./icons/volume.mjs'),
  "vote": () => import('./icons/vote.mjs'),
  "wallet-2": () => import('./icons/wallet-2.mjs'),
  "wallet-cards": () => import('./icons/wallet-cards.mjs'),
  "wallet": () => import('./icons/wallet.mjs'),
  "wallpaper": () => import('./icons/wallpaper.mjs'),
  "wand-2": () => import('./icons/wand-2.mjs'),
  "wand": () => import('./icons/wand.mjs'),
  "warehouse": () => import('./icons/warehouse.mjs'),
  "watch": () => import('./icons/watch.mjs'),
  "waves": () => import('./icons/waves.mjs'),
  "webcam": () => import('./icons/webcam.mjs'),
  "webhook": () => import('./icons/webhook.mjs'),
  "wheat-off": () => import('./icons/wheat-off.mjs'),
  "wheat": () => import('./icons/wheat.mjs'),
  "whole-word": () => import('./icons/whole-word.mjs'),
  "wifi-off": () => import('./icons/wifi-off.mjs'),
  "wifi": () => import('./icons/wifi.mjs'),
  "wind": () => import('./icons/wind.mjs'),
  "wine-off": () => import('./icons/wine-off.mjs'),
  "wine": () => import('./icons/wine.mjs'),
  "workflow": () => import('./icons/workflow.mjs'),
  "wrap-text": () => import('./icons/wrap-text.mjs'),
  "wrench": () => import('./icons/wrench.mjs'),
  "x-circle": () => import('./icons/x-circle.mjs'),
  "x-octagon": () => import('./icons/x-octagon.mjs'),
  "x-square": () => import('./icons/x-square.mjs'),
  "x": () => import('./icons/x.mjs'),
  "youtube": () => import('./icons/youtube.mjs'),
  "zap-off": () => import('./icons/zap-off.mjs'),
  "zap": () => import('./icons/zap.mjs'),
  "zoom-in": () => import('./icons/zoom-in.mjs'),
  "zoom-out": () => import('./icons/zoom-out.mjs')
};

export { dynamicIconImports as default };
//# sourceMappingURL=dynamicIconImports.mjs.map
