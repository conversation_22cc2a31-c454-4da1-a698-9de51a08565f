{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../../../../src/internal/observable/dom/fetch.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yEAA8E;AAC9E,+CAA8C;AAC9C,wDAAuD;AA4FvD,SAAgB,SAAS,CACvB,KAAuB,EACvB,gBAEM;IAFN,iCAAA,EAAA,qBAEM;IAEE,IAAA,QAAQ,GAAc,gBAAgB,SAA9B,EAAK,IAAI,UAAK,gBAAgB,EAAxC,YAAqB,CAAF,CAAsB;IAC/C,OAAO,IAAI,uBAAU,CAAe,UAAC,UAAU;QAK7C,IAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACjC,IAAA,MAAM,GAAK,UAAU,OAAf,CAAgB;QAK9B,IAAI,SAAS,GAAG,IAAI,CAAC;QAKb,IAAQ,WAAW,GAAK,IAAI,OAAT,CAAU;QACrC,IAAI,WAAW,EAAE;YACf,IAAI,WAAW,CAAC,OAAO,EAAE;gBACvB,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;iBAAM;gBAGL,IAAM,oBAAkB,GAAG;oBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;wBACnB,UAAU,CAAC,KAAK,EAAE,CAAC;qBACpB;gBACH,CAAC,CAAC;gBACF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,oBAAkB,CAAC,CAAC;gBAC1D,UAAU,CAAC,GAAG,CAAC,cAAM,OAAA,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,oBAAkB,CAAC,EAA5D,CAA4D,CAAC,CAAC;aACpF;SACF;QAOD,IAAM,iBAAiB,yBAAqB,IAAI,KAAE,MAAM,QAAA,GAAE,CAAC;QAE3D,IAAM,WAAW,GAAG,UAAC,GAAQ;YAC3B,SAAS,GAAG,KAAK,CAAC;YAClB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC;aAC5B,IAAI,CAAC,UAAC,QAAQ;YACb,IAAI,QAAQ,EAAE;gBAIZ,qBAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CACrC,6CAAwB,CACtB,UAAU,EAEV,SAAS,EAET;oBACE,SAAS,GAAG,KAAK,CAAC;oBAClB,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC,EACD,WAAW,CACZ,CACF,CAAC;aACH;iBAAM;gBACL,SAAS,GAAG,KAAK,CAAC;gBAClB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,UAAU,CAAC,QAAQ,EAAE,CAAC;aACvB;QACH,CAAC,CAAC;aACD,KAAK,CAAC,WAAW,CAAC,CAAC;QAEtB,OAAO;YACL,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AArFD,8BAqFC"}