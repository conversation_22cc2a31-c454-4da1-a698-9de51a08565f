{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../api/error.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,QAAS,SAAQ,KAAK;IACjC;;OAEG;IACa,IAAI,CAAU;IACd,OAAO,CAAc;IAErC,uDAAuD;IACvD,MAAM,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAa;QACxC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,sDAAsD;IACtD,MAAM,CAAC,OAAO,CAAC,GAAW,EAAE,KAAa;QACvC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAED,8DAA8D;IAC9D,MAAM,CAAC,eAAe,CAAC,GAAW,EAAE,KAAa;QAC/C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,+DAA+D;IAC/D,MAAM,CAAC,gBAAgB,CAAC,GAAW,EAAE,KAAa;QAChD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,uDAAuD;IACvD,MAAM,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAa;QACxC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,4DAA4D;IAC5D,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,KAAa;QAC7C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,+DAA+D;IAC/D,MAAM,CAAC,gBAAgB,CAAC,GAAW,EAAE,KAAa;QAChD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,gEAAgE;IAChE,MAAM,CAAC,iBAAiB,CAAC,GAAW,EAAE,KAAa;QACjD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,iEAAiE;IACjE,MAAM,CAAC,kBAAkB,CAAC,GAAW,EAAE,KAAa;QAClD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED,sDAAsD;IACtD,MAAM,CAAC,OAAO,CAAC,GAAW,EAAE,KAAa;QACvC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAED,yDAAyD;IACzD,MAAM,CAAC,UAAU,CAAC,GAAW,EAAE,KAAa;QAC1C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,4DAA4D;IAC5D,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,KAAa;QAC7C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,uDAAuD;IACvD,MAAM,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAa;QACxC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,0DAA0D;IAC1D,MAAM,CAAC,WAAW,CAAC,GAAW,EAAE,KAAa;QAC3C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED,uDAAuD;IACvD,MAAM,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAa;QACxC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,8DAA8D;IAC9D,MAAM,CAAC,eAAe,CAAC,GAAW,EAAE,KAAa;QAC/C,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,4EAA4E;IAC5E,WAAW,CAAC,OAAmB;QAC7B,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAc,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC;IAED,qFAAqF;IACrF,YAAY,IAAa,EAAE,GAAW,EAAE,KAAa,EAAE,OAAoB;QACzE,gGAAgG;QAChG,KAAK,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,2FAA2F;QAC3F,oHAAoH;QACpH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;YAClC,KAAK,EAAE,UAAU;YACjB,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,gDAAgD;QAChD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAChD,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAID,MAAM,CAAN,IAAY,OAiMX;AAjMD,WAAY,OAAO;IACjB;;OAEG;IACH,oBAAS,CAAA;IAET;;;;OAIG;IACH,gCAAqB,CAAA;IAErB;;;;;;;;OAQG;IACH,8BAAmB,CAAA;IAEnB;;;;;;;OAOG;IACH,+CAAoC,CAAA;IAEpC;;;;;;;;;OASG;IACH,iDAAsC,CAAA;IAEtC;;;;;OAKG;IACH,iCAAsB,CAAA;IAEtB;;;;;OAKG;IACH,2CAAgC,CAAA;IAEhC;;;;;;;;;;OAUG;IACH,iDAAsC,CAAA;IAEtC;;;;;;;OAOG;IACH,mDAAwC,CAAA;IAExC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,qDAA0C,CAAA;IAE1C;;;;;;;OAOG;IACH,8BAAmB,CAAA;IAEnB;;;;;;;;;;;;;;;;;;OAkBG;IACH,sCAA2B,CAAA;IAE3B;;;;;;;;;OASG;IACH,0CAA+B,CAAA;IAE/B;;;;;;;OAOG;IACH,gCAAqB,CAAA;IAErB;;;;;;;;;;;OAWG;IACH,sCAA2B,CAAA;IAE3B;;;;OAIG;IACH,iCAAsB,CAAA;IAEtB;;;;;;;OAOG;IACH,8CAAmC,CAAA;AACrC,CAAC,EAjMW,OAAO,KAAP,OAAO,QAiMlB"}