{"version": 3, "file": "merge-configs.mjs", "sources": ["../../src/lib/merge-configs.ts"], "sourcesContent": ["import { Config } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport function mergeConfigs(baseConfig: Config, configExtension: Partial<Config>) {\n    for (const key in configExtension) {\n        mergePropertyRecursively(baseConfig as any, key, configExtension[key as keyof Config])\n    }\n\n    return baseConfig\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst overrideTypes = new Set(['string', 'number', 'boolean'])\n\nfunction mergePropertyRecursively(\n    baseObject: Record<string, unknown>,\n    mergeKey: string,\n    mergeValue: unknown,\n) {\n    if (\n        !hasOwnProperty.call(baseObject, mergeKey) ||\n        overrideTypes.has(typeof mergeValue) ||\n        mergeValue === null\n    ) {\n        baseObject[mergeKey] = mergeValue\n        return\n    }\n\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = (baseObject[mergeKey] as unknown[]).concat(mergeValue)\n        return\n    }\n\n    if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue\n            return\n        }\n\n        for (const nextKey in mergeValue) {\n            mergePropertyRecursively(\n                baseObject[mergeKey] as Record<string, unknown>,\n                nextKey,\n                mergeValue[nextKey as keyof object],\n            )\n        }\n    }\n}\n"], "names": ["mergeConfigs", "baseConfig", "configExtension", "key", "mergePropertyRecursively", "hasOwnProperty", "Object", "prototype", "overrideTypes", "Set", "baseObject", "mergeKey", "mergeValue", "call", "has", "Array", "isArray", "concat", "<PERSON><PERSON><PERSON>"], "mappings": "AAEA;;;AAGG;AACa,SAAAA,YAAY,CAACC,UAAkB,EAAEC,eAAgC,EAAA;AAC7E,EAAA,KAAK,IAAMC,GAAG,IAAID,eAAe,EAAE;IAC/BE,wBAAwB,CAACH,UAAiB,EAAEE,GAAG,EAAED,eAAe,CAACC,GAAmB,CAAC,CAAC,CAAA;AACzF,GAAA;AAED,EAAA,OAAOF,UAAU,CAAA;AACrB,CAAA;AAEA,IAAMI,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc,CAAA;AACtD,IAAMG,aAAa,gBAAG,IAAIC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAA;AAE9D,SAASL,wBAAwB,CAC7BM,UAAmC,EACnCC,QAAgB,EAChBC,UAAmB,EAAA;EAEnB,IACI,CAACP,cAAc,CAACQ,IAAI,CAACH,UAAU,EAAEC,QAAQ,CAAC,IAC1CH,aAAa,CAACM,GAAG,CAAC,OAAOF,UAAU,CAAC,IACpCA,UAAU,KAAK,IAAI,EACrB;AACEF,IAAAA,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU,CAAA;AACjC,IAAA,OAAA;AACH,GAAA;AAED,EAAA,IAAIG,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,IAAIG,KAAK,CAACC,OAAO,CAACN,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE;AAClED,IAAAA,UAAU,CAACC,QAAQ,CAAC,GAAID,UAAU,CAACC,QAAQ,CAAe,CAACM,MAAM,CAACL,UAAU,CAAC,CAAA;AAC7E,IAAA,OAAA;AACH,GAAA;AAED,EAAA,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAI,OAAOF,UAAU,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;AAC5E,IAAA,IAAID,UAAU,CAACC,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC/BD,MAAAA,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU,CAAA;AACjC,MAAA,OAAA;AACH,KAAA;AAED,IAAA,KAAK,IAAMM,OAAO,IAAIN,UAAU,EAAE;AAC9BR,MAAAA,wBAAwB,CACpBM,UAAU,CAACC,QAAQ,CAA4B,EAC/CO,OAAO,EACPN,UAAU,CAACM,OAAuB,CAAC,CACtC,CAAA;AACJ,KAAA;AACJ,GAAA;AACL;;;;"}