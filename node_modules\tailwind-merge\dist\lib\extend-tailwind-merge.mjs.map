{"version": 3, "file": "extend-tailwind-merge.mjs", "sources": ["../../src/lib/extend-tailwind-merge.ts"], "sourcesContent": ["import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { Config } from './types'\n\ntype CreateConfigSubsequent = (config: Config) => Config\n\nexport function extendTailwindMerge(\n    configExtension: Partial<Config> | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) {\n    return typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n}\n"], "names": ["extendTailwindMerge", "configExtension", "createConfig", "createTailwindMerge", "getDefaultConfig", "mergeConfigs"], "mappings": ";;;;SAOgBA,mBAAmB,CAC/BC,eAAyD,EAChB;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAAtCC,YAAsC,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAAtCA,YAAsC,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;AAEzC,EAAA,OAAO,OAAOD,eAAe,KAAK,UAAU,GACtCE,mBAAmB,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAACC,gBAAgB,EAAEH,eAAe,CAAKC,CAAAA,MAAAA,CAAAA,YAAY,CAAC,CAAA,GACvEC,mBAAmB,CACf,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,YAAA;AAAA,IAAA,OAAME,YAAY,CAACD,gBAAgB,EAAE,EAAEH,eAAe,CAAC,CAAA;AAAA,GAAA,CAAA,CAAA,MAAA,CACpDC,YAAY,CAClB,CAAA,CAAA;AACX;;;;"}