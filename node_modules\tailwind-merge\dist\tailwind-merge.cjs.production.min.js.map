{"version": 3, "file": "tailwind-merge.cjs.production.min.js", "sources": ["../src/lib/tw-join.ts", "../src/lib/class-utils.ts", "../src/lib/lru-cache.ts", "../src/lib/modifier-utils.ts", "../src/lib/merge-classlist.ts", "../src/lib/create-tailwind-merge.ts", "../src/lib/config-utils.ts", "../src/lib/from-theme.ts", "../src/lib/validators.ts", "../src/lib/default-config.ts", "../src/lib/merge-configs.ts", "../src/lib/tw-merge.ts", "../src/index.ts", "../src/lib/extend-tailwind-merge.ts"], "sourcesContent": ["/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nfunction toValue(mix: ClassNameArray | string) {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { ClassGroup, ClassGroupId, ClassValidator, Config, ThemeGetter, ThemeObject } from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: ClassGroupId\n}\n\ninterface ClassValidatorObject {\n    classGroupId: ClassGroupId\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport function createClassUtils(config: Config) {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers = {} } = config\n\n    function getClassGroupId(className: string) {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    function getConflictingClassGroupIds(classGroupId: ClassGroupId, hasPostfixModifier: boolean) {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nfunction getGroupRecursive(\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): ClassGroupId | undefined {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nfunction getGroupIdForArbitraryProperty(className: string) {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport function createClassMap(config: Config) {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nfunction processClassesRecursively(\n    classGroup: ClassGroup,\n    classPartObject: ClassPartObject,\n    classGroupId: ClassGroupId,\n    theme: ThemeObject,\n) {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nfunction getPart(classPartObject: ClassPartObject, path: string) {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nfunction isThemeGetter(func: ClassValidator | ThemeGetter): func is ThemeGetter {\n    return (func as ThemeGetter).isThemeGetter\n}\n\nfunction getPrefixedClassGroupEntries(\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup]> {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport function createLruCache<Key, Value>(maxCacheSize: number): LruCache<Key, Value> {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    function update(key: Key, value: Value) {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { Config } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport function createSplitModifiers(config: Config) {\n    const separator = config.separator || ':'\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className: string) {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport function sortModifiers(modifiers: string[]) {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './modifier-utils'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport function mergeClassList(classList: string, configUtils: ConfigUtils) {\n    const { splitModifiers, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict = new Set<string>()\n\n    return (\n        classList\n            .trim()\n            .split(SPLIT_CLASSES_REGEX)\n            .map((originalClassName) => {\n                const {\n                    modifiers,\n                    hasImportantModifier,\n                    baseClassName,\n                    maybePostfixModifierPosition,\n                } = splitModifiers(originalClassName)\n\n                let classGroupId = getClassGroupId(\n                    maybePostfixModifierPosition\n                        ? baseClassName.substring(0, maybePostfixModifierPosition)\n                        : baseClassName,\n                )\n\n                let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n\n                if (!classGroupId) {\n                    if (!maybePostfixModifierPosition) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    classGroupId = getClassGroupId(baseClassName)\n\n                    if (!classGroupId) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    hasPostfixModifier = false\n                }\n\n                const variantModifier = sortModifiers(modifiers).join(':')\n\n                const modifierId = hasImportantModifier\n                    ? variantModifier + IMPORTANT_MODIFIER\n                    : variantModifier\n\n                return {\n                    isTailwindClass: true as const,\n                    modifierId,\n                    classGroupId,\n                    originalClassName,\n                    hasPostfixModifier,\n                }\n            })\n            .reverse()\n            // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n            .filter((parsed) => {\n                if (!parsed.isTailwindClass) {\n                    return true\n                }\n\n                const { modifierId, classGroupId, hasPostfixModifier } = parsed\n\n                const classId = modifierId + classGroupId\n\n                if (classGroupsInConflict.has(classId)) {\n                    return false\n                }\n\n                classGroupsInConflict.add(classId)\n\n                getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach((group) =>\n                    classGroupsInConflict.add(modifierId + group),\n                )\n\n                return true\n            })\n            .reverse()\n            .map((parsed) => parsed.originalClassName)\n            .join(' ')\n    )\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { Config } from './types'\n\ntype CreateConfigFirst = () => Config\ntype CreateConfigSubsequent = (config: Config) => Config\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    ...createConfig: [CreateConfigFirst, ...CreateConfigSubsequent[]]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const [firstCreateConfig, ...restCreateConfig] = createConfig\n\n        const config = restCreateConfig.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            firstCreateConfig(),\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { createClassUtils } from './class-utils'\nimport { createLruCache } from './lru-cache'\nimport { createSplitModifiers } from './modifier-utils'\nimport { Config } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createConfigUtils(config: Config) {\n    return {\n        cache: createLruCache<string, string>(config.cacheSize),\n        splitModifiers: createSplitModifiers(config),\n        ...createClassUtils(config),\n    }\n}\n", "import { ThemeGetter, ThemeObject } from './types'\n\nexport function fromTheme(key: string): ThemeGetter {\n    const themeGetter = (theme: ThemeObject) => theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\n// Shadow always begins with x and y offset separated by underscore\nconst shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\n\nexport function isLength(value: string) {\n    return (\n        isNumber(value) ||\n        stringLengths.has(value) ||\n        fractionRegex.test(value) ||\n        isArbitraryLength(value)\n    )\n}\n\nexport function isArbitraryLength(value: string) {\n    return getIsArbitraryValue(value, 'length', isLengthOnly)\n}\n\nexport function isArbitrarySize(value: string) {\n    return getIsArbitraryValue(value, 'size', isNever)\n}\n\nexport function isArbitraryPosition(value: string) {\n    return getIsArbitraryValue(value, 'position', isNever)\n}\n\nexport function isArbitraryUrl(value: string) {\n    return getIsArbitraryValue(value, 'url', isUrl)\n}\n\nexport function isArbitraryNumber(value: string) {\n    return getIsArbitraryValue(value, 'number', isNumber)\n}\n\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nexport const isArbitraryWeight = isArbitraryNumber\n\nexport function isNumber(value: string) {\n    return !Number.isNaN(Number(value))\n}\n\nexport function isPercent(value: string) {\n    return value.endsWith('%') && isNumber(value.slice(0, -1))\n}\n\nexport function isInteger(value: string) {\n    return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly)\n}\n\nexport function isArbitraryValue(value: string) {\n    return arbitraryValueRegex.test(value)\n}\n\nexport function isAny() {\n    return true\n}\n\nexport function isTshirtSize(value: string) {\n    return tshirtUnitRegex.test(value)\n}\n\nexport function isArbitraryShadow(value: string) {\n    return getIsArbitraryValue(value, '', isShadow)\n}\n\nfunction getIsArbitraryValue(value: string, label: string, testValue: (value: string) => boolean) {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return result[1] === label\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nfunction isLengthOnly(value: string) {\n    return lengthUnitRegex.test(value)\n}\n\nfunction isNever() {\n    return false\n}\n\nfunction isUrl(value: string) {\n    return value.startsWith('url(')\n}\n\nfunction isIntegerOnly(value: string) {\n    return Number.isInteger(Number(value))\n}\n\nfunction isShadow(value: string) {\n    return shadowRegex.test(value)\n}\n", "import { fromTheme } from './from-theme'\nimport { Config } from './types'\nimport {\n    isAny,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryUrl,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport function getDefaultConfig() {\n    const colors = fromTheme('colors')\n    const spacing = fromTheme('spacing')\n    const blur = fromTheme('blur')\n    const brightness = fromTheme('brightness')\n    const borderColor = fromTheme('borderColor')\n    const borderRadius = fromTheme('borderRadius')\n    const borderSpacing = fromTheme('borderSpacing')\n    const borderWidth = fromTheme('borderWidth')\n    const contrast = fromTheme('contrast')\n    const grayscale = fromTheme('grayscale')\n    const hueRotate = fromTheme('hueRotate')\n    const invert = fromTheme('invert')\n    const gap = fromTheme('gap')\n    const gradientColorStops = fromTheme('gradientColorStops')\n    const gradientColorStopPositions = fromTheme('gradientColorStopPositions')\n    const inset = fromTheme('inset')\n    const margin = fromTheme('margin')\n    const opacity = fromTheme('opacity')\n    const padding = fromTheme('padding')\n    const saturate = fromTheme('saturate')\n    const scale = fromTheme('scale')\n    const sepia = fromTheme('sepia')\n    const skew = fromTheme('skew')\n    const space = fromTheme('space')\n    const translate = fromTheme('translate')\n\n    const getOverscroll = () => ['auto', 'contain', 'none'] as const\n    const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing] as const\n    const getSpacingWithArbitrary = () => [isArbitraryValue, spacing] as const\n    const getLengthWithEmpty = () => ['', isLength] as const\n    const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue] as const\n    const getPositions = () =>\n        [\n            'bottom',\n            'center',\n            'left',\n            'left-bottom',\n            'left-top',\n            'right',\n            'right-bottom',\n            'right-top',\n            'top',\n        ] as const\n    const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'] as const\n    const getBlendModes = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n            'plus-lighter',\n        ] as const\n    const getAlign = () =>\n        ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'] as const\n    const getZeroAndEmpty = () => ['', '0', isArbitraryValue] as const\n    const getBreaks = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const getNumber = () => [isNumber, isArbitraryNumber]\n    const getNumberAndArbitrary = () => [isNumber, isArbitraryValue]\n\n    return {\n        cacheSize: 500,\n        theme: {\n            colors: [isAny],\n            spacing: [isLength],\n            blur: ['none', '', isTshirtSize, isArbitraryValue],\n            brightness: getNumber(),\n            borderColor: [colors],\n            borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmpty(),\n            contrast: getNumber(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [colors],\n            gradientColorStopPositions: [isPercent, isArbitraryLength],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumber(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumber(),\n            scale: getNumber(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary(),\n        },\n        classGroups: {\n            // Layout\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [{ aspect: ['auto', 'square', 'video', isArbitraryValue] }],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [{ columns: [isTshirtSize] }],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': getBreaks() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': getBreaks() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: [...getPositions(), isArbitraryValue] }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: getOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': getOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': getOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: getOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': getOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': getOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: [inset] }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': [inset] }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': [inset] }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: [inset] }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: [inset] }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: [inset] }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: [inset] }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: [inset] }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: [inset] }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: ['auto', isInteger] }],\n            // Flexbox and Grid\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [{ basis: getSpacingWithAutoAndArbitrary() }],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['wrap', 'wrap-reverse', 'nowrap'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: ['1', 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: getZeroAndEmpty() }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: getZeroAndEmpty() }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [{ order: ['first', 'last', 'none', isInteger] }],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': [isAny] }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: ['auto', { span: ['full', isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': [isAny] }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: ['auto', { span: [isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: [gap] }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': [gap] }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': [gap] }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: ['normal', ...getAlign()] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': ['start', 'end', 'center', 'stretch'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...getAlign(), 'baseline'] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [{ self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline'] }],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': [...getAlign(), 'baseline'] }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: [padding] }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: [padding] }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: [padding] }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: [padding] }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: [padding] }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: [padding] }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: [padding] }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: [padding] }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: [padding] }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: [margin] }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: [margin] }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: [margin] }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: [margin] }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: [margin] }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: [margin] }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: [margin] }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: [margin] }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: [margin] }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x': [{ 'space-x': [space] }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y': [{ 'space-y': [space] }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y-reverse': ['space-y-reverse'],\n            // Sizing\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: ['auto', 'min', 'max', 'fit', isArbitraryValue, spacing] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [{ 'min-w': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        '0',\n                        'none',\n                        'full',\n                        'min',\n                        'max',\n                        'fit',\n                        'prose',\n                        { screen: [isTshirtSize] },\n                        isTshirtSize,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit'] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit'] }],\n            // Typography\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [{ text: ['base', isTshirtSize, isArbitraryLength] }],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [\n                {\n                    font: [\n                        'thin',\n                        'extralight',\n                        'light',\n                        'normal',\n                        'medium',\n                        'semibold',\n                        'bold',\n                        'extrabold',\n                        'black',\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isAny] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [\n                {\n                    tracking: [\n                        'tighter',\n                        'tight',\n                        'normal',\n                        'wide',\n                        'wider',\n                        'widest',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [{ 'line-clamp': ['none', isNumber, isArbitraryNumber] }],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        'none',\n                        'tight',\n                        'snug',\n                        'normal',\n                        'relaxed',\n                        'loose',\n                        isArbitraryValue,\n                        isLength,\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryValue] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [{ list: ['none', 'disc', 'decimal', isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: [colors] }],\n            /**\n             * Placeholder Opacity\n             * @see https://tailwindcss.com/docs/placeholder-opacity\n             */\n            'placeholder-opacity': [{ 'placeholder-opacity': [opacity] }],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: [colors] }],\n            /**\n             * Text Opacity\n             * @see https://tailwindcss.com/docs/text-opacity\n             */\n            'text-opacity': [{ 'text-opacity': [opacity] }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...getLineStyles(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [{ decoration: ['auto', 'from-font', isLength] }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [{ 'underline-offset': ['auto', isArbitraryValue, isLength] }],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: [colors] }],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: getSpacingWithArbitrary() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryValue] }],\n            // Backgrounds\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Opacity\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/background-opacity\n             */\n            'bg-opacity': [{ 'bg-opacity': [opacity] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: [...getPositions(), isArbitraryPosition] }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: ['no-repeat', { repeat: ['', 'x', 'y', 'round', 'space'] }] }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: ['auto', 'cover', 'contain', isArbitrarySize] }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        { 'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                        isArbitraryUrl,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: [colors] }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: [gradientColorStops] }],\n            // Borders\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: [borderRadius] }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': [borderRadius] }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': [borderRadius] }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': [borderRadius] }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': [borderRadius] }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': [borderRadius] }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': [borderRadius] }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': [borderRadius] }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': [borderRadius] }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': [borderRadius] }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': [borderRadius] }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': [borderRadius] }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': [borderRadius] }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': [borderRadius] }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': [borderRadius] }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: [borderWidth] }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': [borderWidth] }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': [borderWidth] }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': [borderWidth] }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': [borderWidth] }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': [borderWidth] }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': [borderWidth] }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': [borderWidth] }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': [borderWidth] }],\n            /**\n             * Border Opacity\n             * @see https://tailwindcss.com/docs/border-opacity\n             */\n            'border-opacity': [{ 'border-opacity': [opacity] }],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...getLineStyles(), 'hidden'] }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x': [{ 'divide-x': [borderWidth] }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y': [{ 'divide-y': [borderWidth] }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Divide Opacity\n             * @see https://tailwindcss.com/docs/divide-opacity\n             */\n            'divide-opacity': [{ 'divide-opacity': [opacity] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/divide-style\n             */\n            'divide-style': [{ divide: getLineStyles() }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: [borderColor] }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': [borderColor] }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': [borderColor] }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': [borderColor] }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': [borderColor] }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': [borderColor] }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': [borderColor] }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: [borderColor] }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: ['', ...getLineStyles()] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [{ 'outline-offset': [isArbitraryValue, isLength] }],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [{ outline: [isLength] }],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: [colors] }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w': [{ ring: getLengthWithEmpty() }],\n            /**\n             * Ring Width Inset\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/ring-color\n             */\n            'ring-color': [{ ring: [colors] }],\n            /**\n             * Ring Opacity\n             * @see https://tailwindcss.com/docs/ring-opacity\n             */\n            'ring-opacity': [{ 'ring-opacity': [opacity] }],\n            /**\n             * Ring Offset Width\n             * @see https://tailwindcss.com/docs/ring-offset-width\n             */\n            'ring-offset-w': [{ 'ring-offset': [isLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://tailwindcss.com/docs/ring-offset-color\n             */\n            'ring-offset-color': [{ 'ring-offset': [colors] }],\n            // Effects\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [{ shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow] }],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow-color\n             */\n            'shadow-color': [{ shadow: [isAny] }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [opacity] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': getBlendModes() }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': getBlendModes() }],\n            // Filters\n            /**\n             * Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [{ filter: ['', 'none'] }],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: [blur] }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [brightness] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [contrast] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [{ 'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue] }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: [grayscale] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [hueRotate] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: [invert] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [saturate] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: [sepia] }],\n            /**\n             * Backdrop Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [{ 'backdrop-filter': ['', 'none'] }],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': [blur] }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [{ 'backdrop-brightness': [brightness] }],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [{ 'backdrop-contrast': [contrast] }],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [{ 'backdrop-grayscale': [grayscale] }],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [{ 'backdrop-hue-rotate': [hueRotate] }],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [{ 'backdrop-invert': [invert] }],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [{ 'backdrop-opacity': [opacity] }],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [{ 'backdrop-saturate': [saturate] }],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [{ 'backdrop-sepia': [sepia] }],\n            // Tables\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': [borderSpacing] }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': [borderSpacing] }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': [borderSpacing] }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n            // Transitions and Animation\n            /**\n             * Tranisition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        'none',\n                        'all',\n                        '',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: getNumberAndArbitrary() }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [{ ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue] }],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: getNumberAndArbitrary() }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue] }],\n            // Transforms\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [{ transform: ['', 'gpu', 'none'] }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: [scale] }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': [scale] }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': [scale] }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: [isInteger, isArbitraryValue] }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': [translate] }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': [translate] }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': [skew] }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': [skew] }],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [\n                {\n                    origin: [\n                        'center',\n                        'top',\n                        'top-right',\n                        'right',\n                        'bottom-right',\n                        'bottom',\n                        'bottom-left',\n                        'left',\n                        'top-left',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            // Interactivity\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: ['auto', colors] }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: ['appearance-none'],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: [colors] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['none', 'auto'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', 'y', 'x', ''] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'pinch-zoom',\n                        'manipulation',\n                        { pan: ['x', 'left', 'right', 'y', 'up', 'down'] },\n                    ],\n                },\n            ],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                { 'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue] },\n            ],\n            // SVG\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: [colors, 'none'] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [{ stroke: [isLength, isArbitraryNumber] }],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: [colors, 'none'] }],\n            // Accessibility\n            /**\n             * Screen Readers\n             * @see https://tailwindcss.com/docs/screen-readers\n             */\n            sr: ['sr-only', 'not-sr-only'],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n    } as const satisfies Config\n}\n", "import { Config } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport function mergeConfigs(baseConfig: Config, configExtension: Partial<Config>) {\n    for (const key in configExtension) {\n        mergePropertyRecursively(baseConfig as any, key, configExtension[key as keyof Config])\n    }\n\n    return baseConfig\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst overrideTypes = new Set(['string', 'number', 'boolean'])\n\nfunction mergePropertyRecursively(\n    baseObject: Record<string, unknown>,\n    mergeKey: string,\n    mergeValue: unknown,\n) {\n    if (\n        !hasOwnProperty.call(baseObject, mergeKey) ||\n        overrideTypes.has(typeof mergeValue) ||\n        mergeValue === null\n    ) {\n        baseObject[mergeKey] = mergeValue\n        return\n    }\n\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = (baseObject[mergeKey] as unknown[]).concat(mergeValue)\n        return\n    }\n\n    if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue\n            return\n        }\n\n        for (const nextKey in mergeValue) {\n            mergePropertyRecursively(\n                baseObject[mergeKey] as Record<string, unknown>,\n                nextKey,\n                mergeValue[nextKey as keyof object],\n            )\n        }\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n", "import { twJoin } from './lib/tw-join'\n\nexport { createTailwindMerge } from './lib/create-tailwind-merge'\nexport { getDefaultConfig } from './lib/default-config'\nexport { extendTailwindMerge } from './lib/extend-tailwind-merge'\nexport { fromTheme } from './lib/from-theme'\nexport { mergeConfigs } from './lib/merge-configs'\nexport { twJoin, type ClassNameValue } from './lib/tw-join'\nexport { twMerge } from './lib/tw-merge'\nexport type { Config } from './lib/types'\nexport * as validators from './lib/validators'\n\n/**\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\n */\nexport const join = twJoin\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { Config } from './types'\n\ntype CreateConfigSubsequent = (config: Config) => Config\n\nexport function extendTailwindMerge(\n    configExtension: Partial<Config> | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) {\n    return typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n}\n"], "names": ["twJoin", "argument", "resolvedValue", "index", "string", "arguments", "length", "toValue", "mix", "k", "CLASS_PART_SEPARATOR", "createClassUtils", "config", "classMap", "theme", "prefix", "nextPart", "Map", "validators", "prefixedClassGroupEntries", "classGroupEntries", "map", "_ref4", "classDefinition", "Object", "fromEntries", "entries", "_ref5", "getPrefixedClassGroupEntries", "classGroups", "for<PERSON>ach", "_ref2", "processClassesRecursively", "createClassMap", "conflictingClassGroups", "_config$conflictingCl", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "shift", "getGroupRecursive", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "concat", "classPartObject", "nextClassPartObject", "get", "classGroupFromNextClassPart", "slice", "undefined", "classRest", "join", "find", "_ref", "validator", "classGroup", "isThemeGetter", "push", "_ref3", "get<PERSON>art", "path", "currentClassPartObject", "pathPart", "has", "set", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "key", "value", "IMPORTANT_MODIFIER", "createSplitModifiers", "separator", "isSeparatorSingleCharacter", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "postfixModifierPosition", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "currentCharacter", "baseClassNameWithImportantModifier", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "SPLIT_CLASSES_REGEX", "createTailwindMerge", "_len", "createConfig", "Array", "_key", "configUtils", "cacheGet", "cacheSet", "functionToCall", "classList", "firstCreateConfig", "reduce", "previousConfig", "createConfigCurrent", "splitModifiers", "createConfigUtils", "tailwindMerge", "cachedResult", "result", "classGroupsInConflict", "Set", "trim", "originalClassName", "Boolean", "isTailwindClass", "variantModifier", "sortedModifiers", "unsortedModifiers", "modifier", "apply", "sort", "sortModifiers", "modifierId", "reverse", "filter", "parsed", "classId", "add", "group", "mergeClassList", "fromTheme", "themeGetter", "arbitraryValueRegex", "fractionRegex", "stringLengths", "tshirtUnitRegex", "lengthUnitRegex", "shadowRegex", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isArbitrarySize", "isNever", "isArbitraryPosition", "isArbitraryUrl", "isUrl", "isArbitraryNumber", "Number", "isNaN", "isPercent", "endsWith", "isInteger", "isIntegerOnly", "isArbitraryValue", "isAny", "isTshirtSize", "isArbitraryShadow", "is<PERSON><PERSON>ow", "label", "testValue", "getDefaultConfig", "colors", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getSpacingWithAutoAndArbitrary", "getSpacingWithArbitrary", "getLengthWithEmpty", "getNumberWithAutoAndArbitrary", "getZeroAndEmpty", "getNumber", "getNumberAndArbitrary", "aspect", "container", "columns", "box", "display", "float", "clear", "isolation", "object", "getPositions", "overflow", "overscroll", "position", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "getAlign", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "getLineStyles", "indent", "align", "whitespace", "break", "hyphens", "bg", "repeat", "from", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "table", "caption", "transition", "duration", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "pan", "select", "fill", "stroke", "sr", "mergeConfigs", "baseConfig", "configExtension", "mergePropertyRecursively", "hasOwnProperty", "prototype", "overrideTypes", "baseObject", "mergeKey", "mergeValue", "call", "isArray", "<PERSON><PERSON><PERSON>", "twMerge"], "mappings": "sBAcgBA,IAMZ,IALA,IACIC,EACAC,EAFAC,EAAQ,EAGRC,EAAS,GAEND,EAAQE,UAAUC,SAChBL,EAAWI,UAAUF,QACjBD,EAAgBK,EAAQN,MACzBG,IAAWA,GAAU,KACrBA,GAAUF,GAItB,OAAOE,CACX,CAEA,SAASG,EAAQC,GACb,GAAmB,iBAARA,EACP,OAAOA,EAMX,IAHA,IAAIN,EACAE,EAAS,GAEJK,EAAI,EAAGA,EAAID,EAAIF,OAAQG,IACxBD,EAAIC,KACCP,EAAgBK,EAAQC,EAAIC,OAC7BL,IAAWA,GAAU,KACrBA,GAAUF,GAKtB,OAAOE,CACX,wDCpCA,IAAMM,EAAuB,IAEvB,SAAUC,EAAiBC,GAC7B,IAAMC,EA6EJ,SAAyBD,GAC3B,IAAQE,EAAkBF,EAAlBE,MAAOC,EAAWH,EAAXG,OACTF,EAA4B,CAC9BG,SAAU,IAAIC,IACdC,WAAY,IAGVC,EA6EV,SACIC,EACAL,GAEA,OAAKA,EAIEK,EAAkBC,KAAI,SAA+BC,GAexD,MAAO,CAfgCA,EAAA,GAAYA,EAAA,GACbD,KAAI,SAACE,GACvC,MAA+B,iBAApBA,EACAR,EAASQ,EAGW,iBAApBA,EACAC,OAAOC,YACVD,OAAOE,QAAQH,GAAiBF,KAAI,SAAAM,GAAY,MAAM,CAACZ,EAAdY,EAAA,GAAOA,EAAA,GAA2B,KAI5EJ,CACX,IAGJ,IAnBWH,CAoBf,CAtGsCQ,CAC9BJ,OAAOE,QAAQd,EAAOiB,aACtBd,GAOJ,OAJAI,EAA0BW,SAAQ,SAA+BC,GAC7DC,EADwDD,EAAA,GAClBlB,EADMkB,EAAA,GACkBjB,EAClE,IAEOD,CACX,CA9FqBoB,CAAerB,GACxBsB,EAAgEtB,EAAhEsB,uBAAsBC,EAA0CvB,EAAxCwB,+BAAAA,OAA8B,IAAAD,EAAG,CAAA,EAAEA,EAuBnE,MAAO,CACHE,gBAtBJ,SAAyBC,GACrB,IAAMC,EAAaD,EAAUE,MAAM9B,GAOnC,MAJsB,KAAlB6B,EAAW,IAAmC,IAAtBA,EAAWjC,QACnCiC,EAAWE,QAGRC,EAAkBH,EAAY1B,IAgD7C,SAAwCyB,GACpC,GAAIK,EAAuBC,KAAKN,GAAY,CACxC,IAAMO,EAA6BF,EAAuBG,KAAKR,GAAY,GACrES,EAAWF,GAA4BG,UACzC,EACAH,EAA2BI,QAAQ,MAGvC,GAAIF,EAEA,MAAO,cAAgBA,CAE9B,CACL,CA7D0DG,CAA+BZ,EACrF,EAcIa,4BAZJ,SAAqCC,EAA4BC,GAC7D,IAAMC,EAAYpB,EAAuBkB,IAAiB,GAE1D,OAAIC,GAAsBjB,EAA+BgB,GACrD,GAAAG,OAAWD,EAAclB,EAA+BgB,IAGrDE,CACX,EAMJ,CAEA,SAASZ,EACLH,EACAiB,GAEA,GAA0B,IAAtBjB,EAAWjC,OACX,OAAOkD,EAAgBJ,aAG3B,IACMK,EAAsBD,EAAgBxC,SAAS0C,IAD5BnB,EAAW,IAE9BoB,EAA8BF,EAC9Bf,EAAkBH,EAAWqB,MAAM,GAAIH,QACvCI,EAEN,GAAIF,EACA,OAAOA,EAGX,GAA0C,IAAtCH,EAAgBtC,WAAWZ,OAA/B,CAIA,IAAMwD,EAAYvB,EAAWwB,KAAKrD,GAElC,OAAO8C,EAAgBtC,WAAW8C,MAAK,SAAAC,GAAY,OAAOC,IAAhBA,WAA0BJ,EAAU,KAAGV,YAJhF,CAKL,CAEA,IAAMT,EAAyB,aAuC/B,SAASX,EACLmC,EACAX,EACAJ,EACAtC,GAEAqD,EAAWrC,SAAQ,SAACP,GAChB,GAA+B,iBAApBA,EAAX,CAOA,GAA+B,mBAApBA,EACP,OAAkBA,EA+CG6C,mBA9CjBpC,EACIT,EAAgBT,GAChB0C,EACAJ,EACAtC,QAKR0C,EAAgBtC,WAAWmD,KAAK,CAC5BH,UAAW3C,EACX6B,aAAAA,IAMR5B,OAAOE,QAAQH,GAAiBO,SAAQ,SAAsBwC,GAC1DtC,EADqDsC,EAAA,GAGjDC,EAAQf,EAH6Bc,EAAA,IAIrClB,EACAtC,EAER,GA5BC,MAH2B,KAApBS,EAAyBiC,EAAkBe,EAAQf,EAAiBjC,IAClD6B,aAAeA,CA+B7C,GACJ,CAEA,SAASmB,EAAQf,EAAkCgB,GAC/C,IAAIC,EAAyBjB,EAa7B,OAXAgB,EAAKhC,MAAM9B,GAAsBoB,SAAQ,SAAC4C,GACjCD,EAAuBzD,SAAS2D,IAAID,IACrCD,EAAuBzD,SAAS4D,IAAIF,EAAU,CAC1C1D,SAAU,IAAIC,IACdC,WAAY,KAIpBuD,EAAyBA,EAAuBzD,SAAS0C,IAAIgB,EACjE,IAEOD,CACX,CCnKM,SAAUI,EAA2BC,GACvC,GAAIA,EAAe,EACf,MAAO,CACHpB,IAAK,WAAe,EACpBkB,IAAK,WAAO,GAIpB,IAAIG,EAAY,EACZC,EAAQ,IAAI/D,IACZgE,EAAgB,IAAIhE,IAExB,SAASiE,EAAOC,EAAUC,GACtBJ,EAAMJ,IAAIO,EAAKC,KACfL,EAEgBD,IACZC,EAAY,EACZE,EAAgBD,EAChBA,EAAQ,IAAI/D,IAEpB,CAEA,MAAO,CACHyC,IAAG,SAACyB,GACA,IAAIC,EAAQJ,EAAMtB,IAAIyB,GAEtB,YAActB,IAAVuB,EACOA,OAE8BvB,KAApCuB,EAAQH,EAAcvB,IAAIyB,KAC3BD,EAAOC,EAAKC,GACLA,QAFX,CAIH,EACDR,IAAIO,SAAAA,EAAKC,GACDJ,EAAML,IAAIQ,GACVH,EAAMJ,IAAIO,EAAKC,GAEfF,EAAOC,EAAKC,EAEpB,EAER,CCjDO,IAAMC,EAAqB,IAE5B,SAAUC,EAAqB1E,GACjC,IAAM2E,EAAY3E,EAAO2E,WAAa,IAChCC,EAAkD,IAArBD,EAAUjF,OACvCmF,EAA0BF,EAAU,GACpCG,EAAkBH,EAAUjF,OAGlC,OAAO,SAAwBgC,GAO3B,IANA,IAIIqD,EAJEC,EAAY,GAEdC,EAAe,EACfC,EAAgB,EAGX3F,EAAQ,EAAGA,EAAQmC,EAAUhC,OAAQH,IAAS,CACnD,IAAI4F,EAAmBzD,EAAUnC,GAEjC,GAAqB,IAAjB0F,EAAoB,CACpB,GACIE,IAAqBN,IACpBD,GACGlD,EAAUsB,MAAMzD,EAAOA,EAAQuF,KAAqBH,GAC1D,CACEK,EAAUvB,KAAK/B,EAAUsB,MAAMkC,EAAe3F,IAC9C2F,EAAgB3F,EAAQuF,EACxB,QACH,CAED,GAAyB,MAArBK,EAA0B,CAC1BJ,EAA0BxF,EAC1B,QACH,CACJ,CAEwB,MAArB4F,EACAF,IAC4B,MAArBE,GACPF,GAEP,CAED,IAAMG,EACmB,IAArBJ,EAAUtF,OAAegC,EAAYA,EAAUU,UAAU8C,GACvDG,EACFD,EAAmCE,WAAWb,GAUlD,MAAO,CACHO,UAAAA,EACAK,qBAAAA,EACAE,cAZkBF,EAChBD,EAAmChD,UAAU,GAC7CgD,EAWFI,6BARAT,GAA2BA,EAA0BG,EAC/CH,EAA0BG,OAC1BjC,GASlB,CC9DA,IAAMwC,EAAsB,MCOZ,SAAAC,IACqD,IAAA,IAAAC,EAAAlG,UAAAC,OAA9DkG,EAA8D,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAA9DF,EAA8DE,GAAArG,UAAAqG,GAEjE,IAAIC,EACAC,EACAC,EACAC,EAEJ,SAA2BC,GACvB,IAAOC,EAA0CR,EAAY,GAEvD5F,EAF2C4F,EAAY5C,MAAA,GAE7BqD,QAC5B,SAACC,EAAgBC,GAAmB,OAAKA,EAAoBD,KAC7DF,KAQJ,OALAL,ECnBF,SAA4B/F,GAC9B,MAAO,CACHoE,MAAOH,EAA+BjE,EAAOmE,WAC7CqC,eAAgB9B,EAAqB1E,MAClCD,EAAiBC,GAE5B,CDasByG,CAAkBzG,GAChCgG,EAAWD,EAAY3B,MAAMtB,IAC7BmD,EAAWF,EAAY3B,MAAMJ,IAC7BkC,EAAiBQ,EAEVA,EAAcP,EACzB,EAEA,SAASO,EAAcP,GACnB,IAAMQ,EAAeX,EAASG,GAE9B,GAAIQ,EACA,OAAOA,EAGX,IAAMC,EDpCE,SAAeT,EAAmBJ,GAC9C,IAAQS,EAAiET,EAAjES,eAAgB/E,EAAiDsE,EAAjDtE,gBAAiBc,EAAgCwD,EAAhCxD,4BASnCsE,EAAwB,IAAIC,IAElC,OACIX,EACKY,OACAnF,MAAM6D,GACNhF,KAAI,SAACuG,GACF,IAKIR,EAAAA,EAAeQ,GAJfhC,IAAAA,UACAK,IAAAA,qBACAE,IAAAA,cACAC,IAAAA,6BAGAhD,EAAef,EACf+D,EACMD,EAAcnD,UAAU,EAAGoD,GAC3BD,GAGN9C,EAAqBwE,QAAQzB,GAEjC,IAAKhD,EAAc,CACf,IAAKgD,EACD,MAAO,CACH0B,iBAAiB,EACjBF,kBAAAA,GAMR,KAFAxE,EAAef,EAAgB8D,IAG3B,MAAO,CACH2B,iBAAiB,EACjBF,kBAAAA,GAIRvE,GAAqB,CACxB,CAED,IAAM0E,EDehB,SAAwBnC,GAC1B,GAAIA,EAAUtF,QAAU,EACpB,OAAOsF,EAGX,IAAMoC,EAA4B,GAC9BC,EAA8B,GAelC,OAbArC,EAAU9D,SAAQ,SAACoG,GAC4B,MAAhBA,EAAS,IAGhCF,EAAgB3D,KAAI8D,MAApBH,EAAwBC,EAAkBG,OAAQF,OAAAA,CAAAA,KAClDD,EAAoB,IAEpBA,EAAkB5D,KAAK6D,EAE/B,IAEAF,EAAgB3D,KAAhB2D,MAAAA,EAAwBC,EAAkBG,QAEnCJ,CACX,CCrCwCK,CAAczC,GAAW7B,KAAK,KAMtD,MAAO,CACH+D,iBAAiB,EACjBQ,WANerC,EACb8B,EAAkB1C,EAClB0C,EAKF3E,aAAAA,EACAwE,kBAAAA,EACAvE,mBAAAA,EAEP,IACAkF,UAEAC,QAAO,SAACC,GACL,IAAKA,EAAOX,gBACR,OAAO,EAGX,IAAQQ,EAAiDG,EAAjDH,WAAYlF,EAAqCqF,EAArCrF,aAAcC,EAAuBoF,EAAvBpF,mBAE5BqF,EAAUJ,EAAalF,EAE7B,OAAIqE,EAAsB9C,IAAI+D,KAI9BjB,EAAsBkB,IAAID,GAE1BvF,EAA4BC,EAAcC,GAAoBvB,SAAQ,SAAC8G,GAAK,OACxEnB,EAAsBkB,IAAIL,EAAaM,OAGpC,EACV,IACAL,UACAlH,KAAI,SAACoH,GAAM,OAAKA,EAAOb,iBAAiB,IACxC7D,KAAK,IAElB,CCzDuB8E,CAAe9B,EAAWJ,GAGzC,OAFAE,EAASE,EAAWS,GAEbA,CACX,CAEA,OAAO,WACH,OAAOV,EAAe9G,EAAOmI,MAAM,KAAM9H,YAEjD,CEhDM,SAAUyI,EAAU3D,GACtB,IAAM4D,EAAc,SAACjI,GAAkB,OAAKA,EAAMqE,IAAQ,EAAE,EAI5D,OAFA4D,EAAY3E,eAAgB,EAErB2E,CACX,CCRA,IAAMC,EAAsB,6BACtBC,EAAgB,aAChBC,EAAgB,IAAIxB,IAAI,CAAC,KAAM,OAAQ,WACvCyB,EAAkB,mCAClBC,EACF,4HAEEC,EAAc,yDAEd,SAAUC,EAASlE,GACrB,OACImE,EAASnE,IACT8D,EAAcvE,IAAIS,IAClB6D,EAAcrG,KAAKwC,IACnBoE,EAAkBpE,EAE1B,CAEM,SAAUoE,EAAkBpE,GAC9B,OAAOqE,EAAoBrE,EAAO,SAAUsE,EAChD,CAEM,SAAUC,EAAgBvE,GAC5B,OAAOqE,EAAoBrE,EAAO,OAAQwE,EAC9C,CAEM,SAAUC,EAAoBzE,GAChC,OAAOqE,EAAoBrE,EAAO,WAAYwE,EAClD,CAEM,SAAUE,EAAe1E,GAC3B,OAAOqE,EAAoBrE,EAAO,MAAO2E,EAC7C,CAEM,SAAUC,EAAkB5E,GAC9B,OAAOqE,EAAoBrE,EAAO,SAAUmE,EAChD,CAOM,SAAUA,EAASnE,GACrB,OAAQ6E,OAAOC,MAAMD,OAAO7E,GAChC,CAEM,SAAU+E,EAAU/E,GACtB,OAAOA,EAAMgF,SAAS,MAAQb,EAASnE,EAAMxB,MAAM,GAAI,GAC3D,CAEM,SAAUyG,EAAUjF,GACtB,OAAOkF,EAAclF,IAAUqE,EAAoBrE,EAAO,SAAUkF,EACxE,CAEM,SAAUC,EAAiBnF,GAC7B,OAAO4D,EAAoBpG,KAAKwC,EACpC,UAEgBoF,IACZ,OAAO,CACX,CAEM,SAAUC,EAAarF,GACzB,OAAO+D,EAAgBvG,KAAKwC,EAChC,CAEM,SAAUsF,EAAkBtF,GAC9B,OAAOqE,EAAoBrE,EAAO,GAAIuF,EAC1C,CAEA,SAASlB,EAAoBrE,EAAewF,EAAeC,GACvD,IAAMrD,EAASwB,EAAoBlG,KAAKsC,GAExC,QAAIoC,IACIA,EAAO,GACAA,EAAO,KAAOoD,EAGlBC,EAAUrD,EAAO,IAIhC,CAEA,SAASkC,EAAatE,GAClB,OAAOgE,EAAgBxG,KAAKwC,EAChC,CAEA,SAASwE,IACL,OAAO,CACX,CAEA,SAASG,EAAM3E,GACX,OAAOA,EAAMc,WAAW,OAC5B,CAEA,SAASoE,EAAclF,GACnB,OAAO6E,OAAOI,UAAUJ,OAAO7E,GACnC,CAEA,SAASuF,EAASvF,GACd,OAAOiE,EAAYzG,KAAKwC,EAC5B,yLA9DiC4E,yECvBjBc,IACZ,IAAMC,EAASjC,EAAU,UACnBkC,EAAUlC,EAAU,WACpBmC,EAAOnC,EAAU,QACjBoC,EAAapC,EAAU,cACvBqC,EAAcrC,EAAU,eACxBsC,EAAetC,EAAU,gBACzBuC,EAAgBvC,EAAU,iBAC1BwC,EAAcxC,EAAU,eACxByC,EAAWzC,EAAU,YACrB0C,EAAY1C,EAAU,aACtB2C,EAAY3C,EAAU,aACtB4C,EAAS5C,EAAU,UACnB6C,EAAM7C,EAAU,OAChB8C,EAAqB9C,EAAU,sBAC/B+C,EAA6B/C,EAAU,8BACvCgD,EAAQhD,EAAU,SAClBiD,EAASjD,EAAU,UACnBkD,EAAUlD,EAAU,WACpBmD,EAAUnD,EAAU,WACpBoD,EAAWpD,EAAU,YACrBqD,EAAQrD,EAAU,SAClBsD,EAAQtD,EAAU,SAClBuD,EAAOvD,EAAU,QACjBwD,EAAQxD,EAAU,SAClByD,EAAYzD,EAAU,aAItB0D,EAAiC,WAAH,MAAS,CAAC,OAAQjC,EAAkBS,EAAiB,EACnFyB,EAA0B,WAAH,MAAS,CAAClC,EAAkBS,EAAiB,EACpE0B,EAAqB,WAAH,MAAS,CAAC,GAAIpD,EAAkB,EAClDqD,EAAgC,WAAH,MAAS,CAAC,OAAQpD,EAAUgB,EAA0B,EAoCnFqC,EAAkB,WAAH,MAAS,CAAC,GAAI,IAAKrC,EAA0B,EAG5DsC,EAAY,WAAH,MAAS,CAACtD,EAAUS,EAAkB,EAC/C8C,EAAwB,WAAH,MAAS,CAACvD,EAAUgB,EAAiB,EAEhE,MAAO,CACHxF,UAAW,IACXjE,MAAO,CACHiK,OAAQ,CAACP,GACTQ,QAAS,CAAC1B,GACV2B,KAAM,CAAC,OAAQ,GAAIR,EAAcF,GACjCW,WAAY2B,IACZ1B,YAAa,CAACJ,GACdK,aAAc,CAAC,OAAQ,GAAI,OAAQX,EAAcF,GACjDc,cAAeoB,IACfnB,YAAaoB,IACbnB,SAAUsB,IACVrB,UAAWoB,IACXnB,UAAWqB,IACXpB,OAAQkB,IACRjB,IAAKc,IACLb,mBAAoB,CAACb,GACrBc,2BAA4B,CAAC1B,EAAWX,GACxCsC,MAAOU,IACPT,OAAQS,IACRR,QAASa,IACTZ,QAASQ,IACTP,SAAUW,IACVV,MAAOU,IACPT,MAAOQ,IACPP,KAAMS,IACNR,MAAOG,IACPF,UAAWE,KAEf5K,YAAa,CAMTkL,OAAQ,CAAC,CAAEA,OAAQ,CAAC,OAAQ,SAAU,QAASxC,KAK/CyC,UAAW,CAAC,aAKZC,QAAS,CAAC,CAAEA,QAAS,CAACxC,KAKtB,cAAe,CAAC,CAAE,cAtDtB,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,YA2D5D,eAAgB,CAAC,CAAE,eA3DvB,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,YAgE5D,eAAgB,CAAC,CAAE,eAAgB,CAAC,OAAQ,QAAS,aAAc,kBAKnE,iBAAkB,CAAC,CAAE,iBAAkB,CAAC,QAAS,WAKjDyC,IAAK,CAAC,CAAEA,IAAK,CAAC,SAAU,aAKxBC,QAAS,CACL,QACA,eACA,SACA,OACA,cACA,QACA,eACA,gBACA,aACA,eACA,qBACA,qBACA,qBACA,kBACA,YACA,YACA,OACA,cACA,WACA,YACA,UAMJC,MAAO,CAAC,CAAEA,MAAO,CAAC,QAAS,OAAQ,UAKnCC,MAAO,CAAC,CAAEA,MAAO,CAAC,OAAQ,QAAS,OAAQ,UAK3CC,UAAW,CAAC,UAAW,kBAKvB,aAAc,CAAC,CAAEC,OAAQ,CAAC,UAAW,QAAS,OAAQ,OAAQ,gBAK9D,kBAAmB,CAAC,CAAEA,OAAYC,GAAAA,OAlKtC,CACI,SACA,SACA,OACA,cACA,WACA,QACA,eACA,YACA,OAyJgD,CAAEjD,MAKlDkD,SAAU,CAAC,CAAEA,SA7KK,CAAC,OAAQ,SAAU,OAAQ,UAAW,YAkLxD,aAAc,CAAC,CAAE,aAlLC,CAAC,OAAQ,SAAU,OAAQ,UAAW,YAuLxD,aAAc,CAAC,CAAE,aAvLC,CAAC,OAAQ,SAAU,OAAQ,UAAW,YA4LxDC,WAAY,CAAC,CAAEA,WA7LK,CAAC,OAAQ,UAAW,UAkMxC,eAAgB,CAAC,CAAE,eAlMC,CAAC,OAAQ,UAAW,UAuMxC,eAAgB,CAAC,CAAE,eAvMC,CAAC,OAAQ,UAAW,UA4MxCC,SAAU,CAAC,SAAU,QAAS,WAAY,WAAY,UAKtD7B,MAAO,CAAC,CAAEA,MAAO,CAACA,KAKlB,UAAW,CAAC,CAAE,UAAW,CAACA,KAK1B,UAAW,CAAC,CAAE,UAAW,CAACA,KAK1B8B,MAAO,CAAC,CAAEA,MAAO,CAAC9B,KAKlB+B,IAAK,CAAC,CAAEA,IAAK,CAAC/B,KAKdgC,IAAK,CAAC,CAAEA,IAAK,CAAChC,KAKdiC,MAAO,CAAC,CAAEA,MAAO,CAACjC,KAKlBkC,OAAQ,CAAC,CAAEA,OAAQ,CAAClC,KAKpBmC,KAAM,CAAC,CAAEA,KAAM,CAACnC,KAKhBoC,WAAY,CAAC,UAAW,YAAa,YAKrCC,EAAG,CAAC,CAAEA,EAAG,CAAC,OAAQ9D,KAMlB+D,MAAO,CAAC,CAAEA,MAAO5B,MAKjB,iBAAkB,CAAC,CAAE6B,KAAM,CAAC,MAAO,cAAe,MAAO,iBAKzD,YAAa,CAAC,CAAEA,KAAM,CAAC,OAAQ,eAAgB,YAK/CA,KAAM,CAAC,CAAEA,KAAM,CAAC,IAAK,OAAQ,UAAW,OAAQ9D,KAKhD+D,KAAM,CAAC,CAAEA,KAAM1B,MAKf2B,OAAQ,CAAC,CAAEA,OAAQ3B,MAKnB4B,MAAO,CAAC,CAAEA,MAAO,CAAC,QAAS,OAAQ,OAAQnE,KAK3C,YAAa,CAAC,CAAE,YAAa,CAACG,KAK9B,gBAAiB,CAAC,CAAEiE,IAAK,CAAC,OAAQ,CAAEC,KAAM,CAAC,OAAQrE,IAAcE,KAKjE,YAAa,CAAC,CAAE,YAAaoC,MAK7B,UAAW,CAAC,CAAE,UAAWA,MAKzB,YAAa,CAAC,CAAE,YAAa,CAACnC,KAK9B,gBAAiB,CAAC,CAAEmE,IAAK,CAAC,OAAQ,CAAED,KAAM,CAACrE,IAAcE,KAKzD,YAAa,CAAC,CAAE,YAAaoC,MAK7B,UAAW,CAAC,CAAE,UAAWA,MAKzB,YAAa,CAAC,CAAE,YAAa,CAAC,MAAO,MAAO,QAAS,YAAa,eAKlE,YAAa,CAAC,CAAE,YAAa,CAAC,OAAQ,MAAO,MAAO,KAAMpC,KAK1D,YAAa,CAAC,CAAE,YAAa,CAAC,OAAQ,MAAO,MAAO,KAAMA,KAK1DoB,IAAK,CAAC,CAAEA,IAAK,CAACA,KAKd,QAAS,CAAC,CAAE,QAAS,CAACA,KAKtB,QAAS,CAAC,CAAE,QAAS,CAACA,KAKtB,kBAAmB,CAAC,CAAEiD,QAAU,CAAA,UAAaC,OA1UjD,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,cA+UtD,gBAAiB,CAAC,CAAE,gBAAiB,CAAC,QAAS,MAAO,SAAU,aAKhE,eAAgB,CAAC,CAAE,eAAgB,CAAC,OAAQ,QAAS,MAAO,SAAU,aAKtE,gBAAiB,CAAC,CAAEC,SAAU,UAAQvL,OAzV1C,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,YAyVC,eAKvD,cAAe,CAAC,CAAEwL,MAAO,CAAC,QAAS,MAAO,SAAU,WAAY,aAKhE,aAAc,CAAC,CAAEC,KAAM,CAAC,OAAQ,QAAS,MAAO,SAAU,UAAW,cAKrE,gBAAiB,CAAC,CAAE,gBAAqBH,GAAAA,OAxW7C,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,WAwWH,CAAE,eAKrD,cAAe,CAAC,CAAE,cAAe,CAAC,QAAS,MAAO,SAAU,WAAY,aAKxE,aAAc,CAAC,CAAE,aAAc,CAAC,OAAQ,QAAS,MAAO,SAAU,aAMlEI,EAAG,CAAC,CAAEA,EAAG,CAAChD,KAKViD,GAAI,CAAC,CAAEA,GAAI,CAACjD,KAKZkD,GAAI,CAAC,CAAEA,GAAI,CAAClD,KAKZmD,GAAI,CAAC,CAAEA,GAAI,CAACnD,KAKZoD,GAAI,CAAC,CAAEA,GAAI,CAACpD,KAKZqD,GAAI,CAAC,CAAEA,GAAI,CAACrD,KAKZsD,GAAI,CAAC,CAAEA,GAAI,CAACtD,KAKZuD,GAAI,CAAC,CAAEA,GAAI,CAACvD,KAKZwD,GAAI,CAAC,CAAEA,GAAI,CAACxD,KAKZyD,EAAG,CAAC,CAAEA,EAAG,CAAC3D,KAKV4D,GAAI,CAAC,CAAEA,GAAI,CAAC5D,KAKZ6D,GAAI,CAAC,CAAEA,GAAI,CAAC7D,KAKZ8D,GAAI,CAAC,CAAEA,GAAI,CAAC9D,KAKZ+D,GAAI,CAAC,CAAEA,GAAI,CAAC/D,KAKZgE,GAAI,CAAC,CAAEA,GAAI,CAAChE,KAKZiE,GAAI,CAAC,CAAEA,GAAI,CAACjE,KAKZkE,GAAI,CAAC,CAAEA,GAAI,CAAClE,KAKZmE,GAAI,CAAC,CAAEA,GAAI,CAACnE,KAKZ,UAAW,CAAC,CAAE,UAAW,CAACO,KAK1B,kBAAmB,CAAC,mBAKpB,UAAW,CAAC,CAAE,UAAW,CAACA,KAK1B,kBAAmB,CAAC,mBAMpB6D,EAAG,CAAC,CAAEA,EAAG,CAAC,OAAQ,MAAO,MAAO,MAAO5F,EAAkBS,KAKzD,QAAS,CAAC,CAAE,QAAS,CAAC,MAAO,MAAO,MAAOT,EAAkBjB,KAK7D,QAAS,CACL,CACI,QAAS,CACL,IACA,OACA,OACA,MACA,MACA,MACA,QACA,CAAE8G,OAAQ,CAAC3F,IACXA,EACAF,KAQZ8F,EAAG,CAAC,CAAEA,EAAG,CAAC9F,EAAkBS,EAAS,OAAQ,MAAO,MAAO,SAK3D,QAAS,CAAC,CAAE,QAAS,CAAC,MAAO,MAAO,MAAOT,EAAkBjB,KAK7D,QAAS,CAAC,CAAE,QAAS,CAACiB,EAAkBS,EAAS,MAAO,MAAO,SAM/D,YAAa,CAAC,CAAEsF,KAAM,CAAC,OAAQ7F,EAAcjB,KAK7C,iBAAkB,CAAC,cAAe,wBAKlC,aAAc,CAAC,SAAU,cAKzB,cAAe,CACX,CACI+G,KAAM,CACF,OACA,aACA,QACA,SACA,SACA,WACA,OACA,YACA,QACAvG,KAQZ,cAAe,CAAC,CAAEuG,KAAM,CAAC/F,KAKzB,aAAc,CAAC,eAKf,cAAe,CAAC,WAKhB,mBAAoB,CAAC,gBAKrB,aAAc,CAAC,cAAe,iBAK9B,cAAe,CAAC,oBAAqB,gBAKrC,eAAgB,CAAC,qBAAsB,oBAKvCgG,SAAU,CACN,CACIA,SAAU,CACN,UACA,QACA,SACA,OACA,QACA,SACAjG,KAQZ,aAAc,CAAC,CAAE,aAAc,CAAC,OAAQhB,EAAUS,KAKlDyG,QAAS,CACL,CACIA,QAAS,CACL,OACA,QACA,OACA,SACA,UACA,QACAlG,EACAjB,KAQZ,aAAc,CAAC,CAAE,aAAc,CAAC,OAAQiB,KAKxC,kBAAmB,CAAC,CAAEmG,KAAM,CAAC,OAAQ,OAAQ,UAAWnG,KAKxD,sBAAuB,CAAC,CAAEmG,KAAM,CAAC,SAAU,aAM3C,oBAAqB,CAAC,CAAEC,YAAa,CAAC5F,KAKtC,sBAAuB,CAAC,CAAE,sBAAuB,CAACiB,KAKlD,iBAAkB,CAAC,CAAEsE,KAAM,CAAC,OAAQ,SAAU,QAAS,UAAW,QAAS,SAK3E,aAAc,CAAC,CAAEA,KAAM,CAACvF,KAKxB,eAAgB,CAAC,CAAE,eAAgB,CAACiB,KAKpC,kBAAmB,CAAC,YAAa,WAAY,eAAgB,gBAK7D,wBAAyB,CAAC,CAAE4E,WAAgBC,GAAAA,OAvsBxB,CAAC,QAAS,SAAU,SAAU,SAAU,QAusBD,CAAE,WAK7D,4BAA6B,CAAC,CAAED,WAAY,CAAC,OAAQ,YAAatH,KAKlE,mBAAoB,CAAC,CAAE,mBAAoB,CAAC,OAAQiB,EAAkBjB,KAKtE,wBAAyB,CAAC,CAAEsH,WAAY,CAAC7F,KAKzC,iBAAkB,CAAC,YAAa,YAAa,aAAc,eAK3D,gBAAiB,CAAC,WAAY,gBAAiB,aAK/C+F,OAAQ,CAAC,CAAEA,OAAQrE,MAKnB,iBAAkB,CACd,CACIsE,MAAO,CACH,WACA,MACA,SACA,SACA,WACA,cACA,MACA,QACAxG,KAQZyG,WAAY,CACR,CAAEA,WAAY,CAAC,SAAU,SAAU,MAAO,WAAY,WAAY,kBAMtEC,MAAO,CAAC,CAAEA,MAAO,CAAC,SAAU,QAAS,MAAO,UAK5CC,QAAS,CAAC,CAAEA,QAAS,CAAC,OAAQ,SAAU,UAKxCpC,QAAS,CAAC,CAAEA,QAAS,CAAC,OAAQvE,KAM9B,gBAAiB,CAAC,CAAE4G,GAAI,CAAC,QAAS,QAAS,YAK3C,UAAW,CAAC,CAAE,UAAW,CAAC,SAAU,UAAW,UAAW,UAM1D,aAAc,CAAC,CAAE,aAAc,CAACnF,KAKhC,YAAa,CAAC,CAAE,YAAa,CAAC,SAAU,UAAW,aAKnD,cAAe,CAAC,CAAEmF,GAAQ3D,GAAAA,OApzB9B,CACI,SACA,SACA,OACA,cACA,WACA,QACA,eACA,YACA,OA2yBwC,CAAE3D,MAK1C,YAAa,CAAC,CAAEsH,GAAI,CAAC,YAAa,CAAEC,OAAQ,CAAC,GAAI,IAAK,IAAK,QAAS,aAKpE,UAAW,CAAC,CAAED,GAAI,CAAC,OAAQ,QAAS,UAAWxH,KAK/C,WAAY,CACR,CACIwH,GAAI,CACA,OACA,CAAE,cAAe,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,OACxDrH,KAQZ,WAAY,CAAC,CAAEqH,GAAI,CAACpG,KAKpB,oBAAqB,CAAC,CAAEsG,KAAM,CAACxF,KAK/B,mBAAoB,CAAC,CAAEyF,IAAK,CAACzF,KAK7B,kBAAmB,CAAC,CAAE0F,GAAI,CAAC1F,KAK3B,gBAAiB,CAAC,CAAEwF,KAAM,CAACzF,KAK3B,eAAgB,CAAC,CAAE0F,IAAK,CAAC1F,KAKzB,cAAe,CAAC,CAAE2F,GAAI,CAAC3F,KAMvB4F,QAAS,CAAC,CAAEA,QAAS,CAACpG,KAKtB,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,YAAa,CAAC,CAAE,YAAa,CAACA,KAK9B,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,aAAc,CAAC,CAAE,aAAc,CAACA,KAKhC,WAAY,CAAC,CAAEqG,OAAQ,CAACnG,KAKxB,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,aAAc,CAAC,CAAE,WAAY,CAACA,KAK9B,iBAAkB,CAAC,CAAE,iBAAkB,CAACU,KAKxC,eAAgB,CAAC,CAAEyF,OAAYZ,GAAAA,OAt+BX,CAAC,QAAS,SAAU,SAAU,SAAU,QAs+Bd,CAAE,aAKhD,WAAY,CAAC,CAAE,WAAY,CAACvF,KAK5B,mBAAoB,CAAC,oBAKrB,WAAY,CAAC,CAAE,WAAY,CAACA,KAK5B,mBAAoB,CAAC,oBAKrB,iBAAkB,CAAC,CAAE,iBAAkB,CAACU,KAKxC,eAAgB,CAAC,CAAE0F,OApgCC,CAAC,QAAS,SAAU,SAAU,SAAU,UAygC5D,eAAgB,CAAC,CAAED,OAAQ,CAACtG,KAK5B,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,iBAAkB,CAAC,CAAE,WAAY,CAACA,KAKlC,eAAgB,CAAC,CAAEuG,OAAQ,CAACvG,KAK5B,gBAAiB,CAAC,CAAEwG,QAAU,CAAA,IAAOd,OAjjCjB,CAAC,QAAS,SAAU,SAAU,SAAU,WAsjC5D,iBAAkB,CAAC,CAAE,iBAAkB,CAACtG,EAAkBjB,KAK1D,YAAa,CAAC,CAAEqI,QAAS,CAACrI,KAK1B,gBAAiB,CAAC,CAAEqI,QAAS,CAAC5G,KAK9B,SAAU,CAAC,CAAE6G,KAAMlF,MAKnB,eAAgB,CAAC,cAKjB,aAAc,CAAC,CAAEkF,KAAM,CAAC7G,KAKxB,eAAgB,CAAC,CAAE,eAAgB,CAACiB,KAKpC,gBAAiB,CAAC,CAAE,cAAe,CAAC1C,KAKpC,oBAAqB,CAAC,CAAE,cAAe,CAACyB,KAMxC8G,OAAQ,CAAC,CAAEA,OAAQ,CAAC,GAAI,QAAS,OAAQpH,EAAcC,KAKvD,eAAgB,CAAC,CAAEmH,OAAQ,CAACrH,KAK5BwB,QAAS,CAAC,CAAEA,QAAS,CAACA,KAKtB,YAAa,CAAC,CAAE,YAjnCpB,CACI,SACA,WACA,SACA,UACA,SACA,UACA,cACA,aACA,aACA,aACA,aACA,YACA,MACA,aACA,QACA,aACA,kBAqmCA,WAAY,CAAC,CAAE,WAtnCnB,CACI,SACA,WACA,SACA,UACA,SACA,UACA,cACA,aACA,aACA,aACA,aACA,YACA,MACA,aACA,QACA,aACA,kBA4mCAxD,OAAQ,CAAC,CAAEA,OAAQ,CAAC,GAAI,UAKxByC,KAAM,CAAC,CAAEA,KAAM,CAACA,KAKhBC,WAAY,CAAC,CAAEA,WAAY,CAACA,KAK5BK,SAAU,CAAC,CAAEA,SAAU,CAACA,KAKxB,cAAe,CAAC,CAAE,cAAe,CAAC,GAAI,OAAQd,EAAcF,KAK5DiB,UAAW,CAAC,CAAEA,UAAW,CAACA,KAK1B,aAAc,CAAC,CAAE,aAAc,CAACC,KAKhCC,OAAQ,CAAC,CAAEA,OAAQ,CAACA,KAKpBQ,SAAU,CAAC,CAAEA,SAAU,CAACA,KAKxBE,MAAO,CAAC,CAAEA,MAAO,CAACA,KAMlB,kBAAmB,CAAC,CAAE,kBAAmB,CAAC,GAAI,UAK9C,gBAAiB,CAAC,CAAE,gBAAiB,CAACnB,KAKtC,sBAAuB,CAAC,CAAE,sBAAuB,CAACC,KAKlD,oBAAqB,CAAC,CAAE,oBAAqB,CAACK,KAK9C,qBAAsB,CAAC,CAAE,qBAAsB,CAACC,KAKhD,sBAAuB,CAAC,CAAE,sBAAuB,CAACC,KAKlD,kBAAmB,CAAC,CAAE,kBAAmB,CAACC,KAK1C,mBAAoB,CAAC,CAAE,mBAAoB,CAACM,KAK5C,oBAAqB,CAAC,CAAE,oBAAqB,CAACE,KAK9C,iBAAkB,CAAC,CAAE,iBAAkB,CAACE,KAMxC,kBAAmB,CAAC,CAAEqF,OAAQ,CAAC,WAAY,cAK3C,iBAAkB,CAAC,CAAE,iBAAkB,CAACpG,KAKxC,mBAAoB,CAAC,CAAE,mBAAoB,CAACA,KAK5C,mBAAoB,CAAC,CAAE,mBAAoB,CAACA,KAK5C,eAAgB,CAAC,CAAEyG,MAAO,CAAC,OAAQ,WAKnCC,QAAS,CAAC,CAAEA,QAAS,CAAC,MAAO,YAM7BC,WAAY,CACR,CACIA,WAAY,CACR,OACA,MACA,GACA,SACA,UACA,SACA,YACAzH,KAQZ0H,SAAU,CAAC,CAAEA,SAAUnF,MAKvBoF,KAAM,CAAC,CAAEA,KAAM,CAAC,SAAU,KAAM,MAAO,SAAU3H,KAKjD4H,MAAO,CAAC,CAAEA,MAAOrF,MAKjBsF,QAAS,CAAC,CAAEA,QAAS,CAAC,OAAQ,OAAQ,OAAQ,QAAS,SAAU7H,KAMjE8H,UAAW,CAAC,CAAEA,UAAW,CAAC,GAAI,MAAO,UAKrClG,MAAO,CAAC,CAAEA,MAAO,CAACA,KAKlB,UAAW,CAAC,CAAE,UAAW,CAACA,KAK1B,UAAW,CAAC,CAAE,UAAW,CAACA,KAK1BmG,OAAQ,CAAC,CAAEA,OAAQ,CAACjI,EAAWE,KAK/B,cAAe,CAAC,CAAE,cAAe,CAACgC,KAKlC,cAAe,CAAC,CAAE,cAAe,CAACA,KAKlC,SAAU,CAAC,CAAE,SAAU,CAACF,KAKxB,SAAU,CAAC,CAAE,SAAU,CAACA,KAKxB,mBAAoB,CAChB,CACIkG,OAAQ,CACJ,SACA,MACA,YACA,QACA,eACA,SACA,cACA,OACA,WACAhI,KASZiI,OAAQ,CAAC,CAAEA,OAAQ,CAAC,OAAQzH,KAK5B0H,WAAY,CAAC,mBAKbC,OAAQ,CACJ,CACIA,OAAQ,CACJ,OACA,UACA,UACA,OACA,OACA,OACA,OACA,cACA,OACA,eACA,WACA,OACA,YACA,gBACA,QACA,OACA,UACA,OACA,WACA,aACA,aACA,aACA,WACA,WACA,WACA,WACA,YACA,YACA,YACA,YACA,YACA,YACA,cACA,cACA,UACA,WACAnI,KAQZ,cAAe,CAAC,CAAEoI,MAAO,CAAC5H,KAK1B,iBAAkB,CAAC,CAAE,iBAAkB,CAAC,OAAQ,UAKhD6H,OAAQ,CAAC,CAAEA,OAAQ,CAAC,OAAQ,IAAK,IAAK,MAKtC,kBAAmB,CAAC,CAAEC,OAAQ,CAAC,OAAQ,YAKvC,WAAY,CAAC,CAAE,WAAYpG,MAK3B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,WAAY,CAAC,CAAE,WAAYA,MAK3B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,YAAa,CAAC,CAAE,YAAaA,MAK7B,aAAc,CAAC,CAAEqG,KAAM,CAAC,QAAS,MAAO,SAAU,gBAKlD,YAAa,CAAC,CAAEA,KAAM,CAAC,SAAU,YAKjC,YAAa,CAAC,CAAEA,KAAM,CAAC,OAAQ,IAAK,IAAK,UAKzC,kBAAmB,CAAC,CAAEA,KAAM,CAAC,YAAa,eAK1CC,MAAO,CACH,CACIA,MAAO,CACH,OACA,OACA,aACA,eACA,CAAEC,IAAK,CAAC,IAAK,OAAQ,QAAS,IAAK,KAAM,YAQrDC,OAAQ,CAAC,CAAEA,OAAQ,CAAC,OAAQ,OAAQ,MAAO,UAK3C,cAAe,CACX,CAAE,cAAe,CAAC,OAAQ,SAAU,WAAY,YAAa1I,KAOjE2I,KAAM,CAAC,CAAEA,KAAM,CAACnI,EAAQ,UAKxB,WAAY,CAAC,CAAEoI,OAAQ,CAAC7J,EAAUU,KAKlCmJ,OAAQ,CAAC,CAAEA,OAAQ,CAACpI,EAAQ,UAM5BqI,GAAI,CAAC,UAAW,gBAEpBlR,uBAAwB,CACpBuL,SAAU,CAAC,aAAc,cACzBC,WAAY,CAAC,eAAgB,gBAC7B5B,MAAO,CAAC,UAAW,UAAW,QAAS,MAAO,MAAO,QAAS,SAAU,QACxE,UAAW,CAAC,QAAS,QACrB,UAAW,CAAC,MAAO,UACnBuC,KAAM,CAAC,QAAS,OAAQ,UACxB1C,IAAK,CAAC,QAAS,SACfsD,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC9CC,GAAI,CAAC,KAAM,MACXC,GAAI,CAAC,KAAM,MACXO,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC9CC,GAAI,CAAC,KAAM,MACXC,GAAI,CAAC,KAAM,MACX,YAAa,CAAC,WACd,aAAc,CACV,cACA,mBACA,aACA,cACA,gBAEJ,cAAe,CAAC,cAChB,mBAAoB,CAAC,cACrB,aAAc,CAAC,cACf,cAAe,CAAC,cAChB,eAAgB,CAAC,cACjB4B,QAAS,CACL,YACA,YACA,YACA,YACA,YACA,YACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,cAEJ,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,YAAa,CAAC,aAAc,cAC5B,iBAAkB,CAAC,mBAAoB,oBACvC,WAAY,CACR,aACA,aACA,aACA,aACA,aACA,cAEJ,aAAc,CAAC,aAAc,cAC7B,aAAc,CAAC,aAAc,cAC7B,eAAgB,CACZ,iBACA,iBACA,iBACA,kBAEJ,iBAAkB,CAAC,iBAAkB,kBACrC,iBAAkB,CAAC,iBAAkB,kBACrC,WAAY,CACR,YACA,YACA,YACA,YACA,YACA,YACA,YACA,aAEJ,YAAa,CAAC,YAAa,aAC3B,YAAa,CAAC,YAAa,aAC3B,WAAY,CACR,YACA,YACA,YACA,YACA,YACA,YACA,YACA,aAEJ,YAAa,CAAC,YAAa,aAC3B,YAAa,CAAC,YAAa,cAE/BpP,+BAAgC,CAC5B,YAAa,CAAC,YAG1B,CChvDgB,SAAAiR,EAAaC,EAAoBC,GAC7C,IAAK,IAAMpO,KAAOoO,EACdC,EAAyBF,EAAmBnO,EAAKoO,EAAgBpO,IAGrE,OAAOmO,CACX,CAEA,IAAMG,EAAiBjS,OAAOkS,UAAUD,eAClCE,EAAgB,IAAIjM,IAAI,CAAC,SAAU,SAAU,YAEnD,SAAS8L,EACLI,EACAC,EACAC,GAEA,GACKL,EAAeM,KAAKH,EAAYC,KACjCF,EAAchP,WAAWmP,IACV,OAAfA,GAMJ,GAAIrN,MAAMuN,QAAQF,IAAerN,MAAMuN,QAAQJ,EAAWC,IACtDD,EAAWC,GAAaD,EAAWC,GAAwBtQ,OAAOuQ,QAItE,GAA0B,iBAAfA,GAA2D,iBAAzBF,EAAWC,GAAwB,CAC5E,GAA6B,OAAzBD,EAAWC,GAEX,YADAD,EAAWC,GAAYC,GAI3B,IAAK,IAAMG,KAAWH,EAClBN,EACII,EAAWC,GACXI,EACAH,EAAWG,GAGtB,OAtBGL,EAAWC,GAAYC,CAuB/B,KC/CaI,EAAU5N,EAAoBwE,GCY9B/G,EAAO/D,qECPhBuT,GACyC,IAAA,IAAAhN,EAAAlG,UAAAC,OAAtCkG,EAAsC,IAAAC,MAAAF,EAAA,EAAAA,EAAA,EAAA,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAtCF,EAAsCE,EAAA,GAAArG,UAAAqG,GAEzC,OACMJ,EAAmB6B,WAAA,EADS,mBAApBoL,EACW,CAACzI,EAAkByI,GAAoB/M,OAAAA,GAEtD,CAAA,WAAA,OAAM6M,EAAavI,IAAoByI,EAAgB,GAAAhQ,OACpDiD,GAEjB"}