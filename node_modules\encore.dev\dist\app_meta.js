import * as runtime from "./internal/runtime/mod.js";
// Returns metadata about the running Encore application.
//
// The metadata is cached and is the same object each call,
// and therefore must not be modified by the caller.
export function appMeta() {
    // Compute the metadata on first use.
    if (cached === null) {
        let rt = runtime.RT.appMeta();
        cached = {
            appId: rt.appId,
            apiBaseUrl: rt.apiBaseUrl,
            environment: {
                name: rt.environment.name,
                type: envType(rt.environment.type),
                cloud: cloudProvider(rt.environment.cloud)
            },
            build: {
                revision: rt.build.revision,
                uncommittedChanges: rt.build.uncommittedChanges
            },
            deploy: {
                id: rt.deploy.id,
                hostedServices: rt.deploy.hostedServices
            }
        };
    }
    return cached;
}
function envType(rtType) {
    switch (rtType) {
        case runtime.EnvironmentType.Production:
            return "production";
        case runtime.EnvironmentType.Development:
            return "development";
        case runtime.EnvironmentType.Ephemeral:
            return "ephemeral";
        case runtime.EnvironmentType.Test:
            return "test";
        default:
            return "development";
    }
}
function cloudProvider(rtType) {
    switch (rtType) {
        case runtime.CloudProvider.AWS:
            return "aws";
        case runtime.CloudProvider.GCP:
            return "gcp";
        case runtime.CloudProvider.Azure:
            return "azure";
        case runtime.CloudProvider.Encore:
            return "encore";
        case runtime.CloudProvider.Local:
            return "local";
        default:
            return "local";
    }
}
// The cached app metadata. Set on first use.
let cached = null;
//# sourceMappingURL=app_meta.js.map