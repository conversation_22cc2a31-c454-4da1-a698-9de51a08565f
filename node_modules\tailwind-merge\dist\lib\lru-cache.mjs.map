{"version": 3, "file": "lru-cache.mjs", "sources": ["../../src/lib/lru-cache.ts"], "sourcesContent": ["// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport function createLruCache<Key, Value>(maxCacheSize: number): LruCache<Key, Value> {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    function update(key: Key, value: Value) {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n"], "names": ["createLruCache", "maxCacheSize", "get", "undefined", "set", "cacheSize", "cache", "Map", "previousCache", "update", "key", "value", "has"], "mappings": "AAOA;AACM,SAAUA,cAAc,CAAaC,YAAoB,EAAA;EAC3D,IAAIA,YAAY,GAAG,CAAC,EAAE;IAClB,OAAO;AACHC,MAAAA,GAAG,EAAE,SAAA,GAAA,GAAA;AAAA,QAAA,OAAMC,SAAS,CAAA;AAAA,OAAA;MACpBC,GAAG,EAAE,eAAK,EAAE;KACf,CAAA;AACJ,GAAA;EAED,IAAIC,SAAS,GAAG,CAAC,CAAA;AACjB,EAAA,IAAIC,KAAK,GAAG,IAAIC,GAAG,EAAc,CAAA;AACjC,EAAA,IAAIC,aAAa,GAAG,IAAID,GAAG,EAAc,CAAA;AAEzC,EAAA,SAASE,MAAM,CAACC,GAAQ,EAAEC,KAAY,EAAA;AAClCL,IAAAA,KAAK,CAACF,GAAG,CAACM,GAAG,EAAEC,KAAK,CAAC,CAAA;AACrBN,IAAAA,SAAS,EAAE,CAAA;IAEX,IAAIA,SAAS,GAAGJ,YAAY,EAAE;AAC1BI,MAAAA,SAAS,GAAG,CAAC,CAAA;AACbG,MAAAA,aAAa,GAAGF,KAAK,CAAA;MACrBA,KAAK,GAAG,IAAIC,GAAG,EAAE,CAAA;AACpB,KAAA;AACL,GAAA;EAEA,OAAO;IACHL,GAAG,EAAA,SAAA,GAAA,CAACQ,GAAG,EAAA;AACH,MAAA,IAAIC,KAAK,GAAGL,KAAK,CAACJ,GAAG,CAACQ,GAAG,CAAC,CAAA;MAE1B,IAAIC,KAAK,KAAKR,SAAS,EAAE;AACrB,QAAA,OAAOQ,KAAK,CAAA;AACf,OAAA;MACD,IAAI,CAACA,KAAK,GAAGH,aAAa,CAACN,GAAG,CAACQ,GAAG,CAAC,MAAMP,SAAS,EAAE;AAChDM,QAAAA,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAA;AAClB,QAAA,OAAOA,KAAK,CAAA;AACf,OAAA;KACJ;AACDP,IAAAA,GAAG,EAACM,SAAAA,GAAAA,CAAAA,GAAG,EAAEC,KAAK,EAAA;AACV,MAAA,IAAIL,KAAK,CAACM,GAAG,CAACF,GAAG,CAAC,EAAE;AAChBJ,QAAAA,KAAK,CAACF,GAAG,CAACM,GAAG,EAAEC,KAAK,CAAC,CAAA;AACxB,OAAA,MAAM;AACHF,QAAAA,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAA;AACrB,OAAA;AACL,KAAA;GACH,CAAA;AACL;;;;"}