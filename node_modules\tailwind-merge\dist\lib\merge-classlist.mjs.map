{"version": 3, "file": "merge-classlist.mjs", "sources": ["../../src/lib/merge-classlist.ts"], "sourcesContent": ["import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './modifier-utils'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport function mergeClassList(classList: string, configUtils: ConfigUtils) {\n    const { splitModifiers, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict = new Set<string>()\n\n    return (\n        classList\n            .trim()\n            .split(SPLIT_CLASSES_REGEX)\n            .map((originalClassName) => {\n                const {\n                    modifiers,\n                    hasImportantModifier,\n                    baseClassName,\n                    maybePostfixModifierPosition,\n                } = splitModifiers(originalClassName)\n\n                let classGroupId = getClassGroupId(\n                    maybePostfixModifierPosition\n                        ? baseClassName.substring(0, maybePostfixModifierPosition)\n                        : baseClassName,\n                )\n\n                let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n\n                if (!classGroupId) {\n                    if (!maybePostfixModifierPosition) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    classGroupId = getClassGroupId(baseClassName)\n\n                    if (!classGroupId) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    hasPostfixModifier = false\n                }\n\n                const variantModifier = sortModifiers(modifiers).join(':')\n\n                const modifierId = hasImportantModifier\n                    ? variantModifier + IMPORTANT_MODIFIER\n                    : variantModifier\n\n                return {\n                    isTailwindClass: true as const,\n                    modifierId,\n                    classGroupId,\n                    originalClassName,\n                    hasPostfixModifier,\n                }\n            })\n            .reverse()\n            // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n            .filter((parsed) => {\n                if (!parsed.isTailwindClass) {\n                    return true\n                }\n\n                const { modifierId, classGroupId, hasPostfixModifier } = parsed\n\n                const classId = modifierId + classGroupId\n\n                if (classGroupsInConflict.has(classId)) {\n                    return false\n                }\n\n                classGroupsInConflict.add(classId)\n\n                getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach((group) =>\n                    classGroupsInConflict.add(modifierId + group),\n                )\n\n                return true\n            })\n            .reverse()\n            .map((parsed) => parsed.originalClassName)\n            .join(' ')\n    )\n}\n"], "names": ["SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "splitModifiers", "getClassGroupId", "getConflictingClassGroupIds", "classGroupsInConflict", "Set", "trim", "split", "map", "originalClassName", "modifiers", "hasImportantModifier", "baseClassName", "maybePostfixModifierPosition", "classGroupId", "substring", "hasPostfixModifier", "Boolean", "isTailwindClass", "variantModifier", "sortModifiers", "join", "modifierId", "IMPORTANT_MODIFIER", "reverse", "filter", "parsed", "classId", "has", "add", "for<PERSON>ach", "group"], "mappings": ";;AAGA,IAAMA,mBAAmB,GAAG,KAAK,CAAA;AAEjB,SAAAC,cAAc,CAACC,SAAiB,EAAEC,WAAwB,EAAA;AACtE,EAAA,IAAQC,cAAc,GAAmDD,WAAW,CAA5EC,cAAc;IAAEC,eAAe,GAAkCF,WAAW,CAA5DE,eAAe;IAAEC,2BAA2B,GAAKH,WAAW,CAA3CG,2BAA2B,CAAA;AAEpE;;;;;;AAMG;AACH,EAAA,IAAMC,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAE/C,EAAA,OACIN,SAAS,CACJO,IAAI,EAAE,CACNC,KAAK,CAACV,mBAAmB,CAAC,CAC1BW,GAAG,CAAC,UAACC,iBAAiB,EAAI;IACvB,IAKIR,eAAAA,GAAAA,cAAc,CAACQ,iBAAiB,CAAC;AAJjCC,MAAAA,SAAS,mBAATA,SAAS;AACTC,MAAAA,oBAAoB,mBAApBA,oBAAoB;AACpBC,MAAAA,aAAa,mBAAbA,aAAa;AACbC,MAAAA,4BAA4B,mBAA5BA,4BAA4B,CAAA;AAGhC,IAAA,IAAIC,YAAY,GAAGZ,eAAe,CAC9BW,4BAA4B,GACtBD,aAAa,CAACG,SAAS,CAAC,CAAC,EAAEF,4BAA4B,CAAC,GACxDD,aAAa,CACtB,CAAA;AAED,IAAA,IAAII,kBAAkB,GAAGC,OAAO,CAACJ,4BAA4B,CAAC,CAAA;IAE9D,IAAI,CAACC,YAAY,EAAE;MACf,IAAI,CAACD,4BAA4B,EAAE;QAC/B,OAAO;AACHK,UAAAA,eAAe,EAAE,KAAc;AAC/BT,UAAAA,iBAAiB,EAAjBA,iBAAAA;SACH,CAAA;AACJ,OAAA;AAEDK,MAAAA,YAAY,GAAGZ,eAAe,CAACU,aAAa,CAAC,CAAA;MAE7C,IAAI,CAACE,YAAY,EAAE;QACf,OAAO;AACHI,UAAAA,eAAe,EAAE,KAAc;AAC/BT,UAAAA,iBAAiB,EAAjBA,iBAAAA;SACH,CAAA;AACJ,OAAA;AAEDO,MAAAA,kBAAkB,GAAG,KAAK,CAAA;AAC7B,KAAA;IAED,IAAMG,eAAe,GAAGC,aAAa,CAACV,SAAS,CAAC,CAACW,IAAI,CAAC,GAAG,CAAC,CAAA;IAE1D,IAAMC,UAAU,GAAGX,oBAAoB,GACjCQ,eAAe,GAAGI,kBAAkB,GACpCJ,eAAe,CAAA;IAErB,OAAO;AACHD,MAAAA,eAAe,EAAE,IAAa;AAC9BI,MAAAA,UAAU,EAAVA,UAAU;AACVR,MAAAA,YAAY,EAAZA,YAAY;AACZL,MAAAA,iBAAiB,EAAjBA,iBAAiB;AACjBO,MAAAA,kBAAkB,EAAlBA,kBAAAA;KACH,CAAA;GACJ,CAAC,CACDQ,OAAO,EAAA;AACR;AAAA,GACCC,MAAM,CAAC,UAACC,MAAM,EAAI;AACf,IAAA,IAAI,CAACA,MAAM,CAACR,eAAe,EAAE;AACzB,MAAA,OAAO,IAAI,CAAA;AACd,KAAA;AAED,IAAA,IAAQI,UAAU,GAAuCI,MAAM,CAAvDJ,UAAU;MAAER,YAAY,GAAyBY,MAAM,CAA3CZ,YAAY;MAAEE,kBAAkB,GAAKU,MAAM,CAA7BV,kBAAkB,CAAA;AAEpD,IAAA,IAAMW,OAAO,GAAGL,UAAU,GAAGR,YAAY,CAAA;AAEzC,IAAA,IAAIV,qBAAqB,CAACwB,GAAG,CAACD,OAAO,CAAC,EAAE;AACpC,MAAA,OAAO,KAAK,CAAA;AACf,KAAA;AAEDvB,IAAAA,qBAAqB,CAACyB,GAAG,CAACF,OAAO,CAAC,CAAA;IAElCxB,2BAA2B,CAACW,YAAY,EAAEE,kBAAkB,CAAC,CAACc,OAAO,CAAC,UAACC,KAAK,EAAA;AAAA,MAAA,OACxE3B,qBAAqB,CAACyB,GAAG,CAACP,UAAU,GAAGS,KAAK,CAAC,CAAA;KAChD,CAAA,CAAA;AAED,IAAA,OAAO,IAAI,CAAA;GACd,CAAC,CACDP,OAAO,EAAE,CACThB,GAAG,CAAC,UAACkB,MAAM,EAAA;IAAA,OAAKA,MAAM,CAACjB,iBAAiB,CAAA;AAAA,GAAA,CAAC,CACzCY,IAAI,CAAC,GAAG,CAAC,CAAA;AAEtB;;;;"}