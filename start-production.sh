#!/bin/bash

# Script para iniciar o SmartV em produção
echo "🚀 Iniciando SmartV em modo produção..."

# Função para matar processos existentes
kill_existing_processes() {
    echo "🔄 Parando processos existentes..."
    
    # Matar processos nas portas específicas
    pkill -f "node.*simple-server.cjs" 2>/dev/null || true
    pkill -f "node.*proxy-server.cjs" 2>/dev/null || true
    pkill -f "vite.*preview" 2>/dev/null || true
    
    # Aguardar um pouco para os processos terminarem
    sleep 2
    
    echo "✅ Processos existentes finalizados"
}

# Função para iniciar o backend
start_backend() {
    echo "🔧 Iniciando backend API (porta 4000)..."
    cd api
    nohup node simple-server.cjs > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../pids/backend.pid
    cd ..
    echo "✅ Backend iniciado (PID: $BACKEND_PID)"
}

# Função para iniciar o proxy
start_proxy() {
    echo "🔧 Iniciando proxy server (porta 3001)..."
    nohup node proxy-server.cjs > logs/proxy.log 2>&1 &
    PROXY_PID=$!
    echo $PROXY_PID > pids/proxy.pid
    echo "✅ Proxy iniciado (PID: $PROXY_PID)"
}

# Função para fazer build e iniciar frontend
start_frontend() {
    echo "🔧 Fazendo build do frontend..."
    npm run build
    
    echo "🔧 Iniciando frontend em modo preview (porta 5173)..."
    nohup npm run preview > logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > pids/frontend.pid
    echo "✅ Frontend iniciado (PID: $FRONTEND_PID)"
}

# Criar diretórios necessários
mkdir -p logs pids

# Executar funções
kill_existing_processes
start_backend
sleep 3
start_proxy
sleep 3
start_frontend

echo ""
echo "🎉 SmartV iniciado com sucesso!"
echo "📱 Frontend: http://smartv.shop (porta 5173)"
echo "🔗 Proxy: http://localhost:3001"
echo "⚙️  Backend: http://localhost:4000"
echo ""
echo "📋 Logs disponíveis em:"
echo "   - Frontend: logs/frontend.log"
echo "   - Proxy: logs/proxy.log"
echo "   - Backend: logs/backend.log"
echo ""
echo "🛑 Para parar todos os serviços, execute: ./stop-production.sh"
