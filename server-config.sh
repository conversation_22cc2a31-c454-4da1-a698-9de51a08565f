#!/bin/bash

# =============================================================================
# SMARTV.SHOP - Configuração Completa do Servidor
# =============================================================================
# Este script configura o ambiente completo para o domínio https://smartv.shop/
# Inclui: Frontend (Vite), Backend (Proxy), Nginx, SSL e gerenciamento de processos
# =============================================================================

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações
DOMAIN="smartv.shop"
PROJECT_PATH="/www/wwwroot/smarttv"
FRONTEND_PORT=5173
BACKEND_PORT=3001
ENCORE_PORT=4000
SSL_CERT_PATH="/www/server/panel/vhost/cert/ssmartv"
NGINX_CONFIG_PATH="/www/server/panel/vhost/nginx"
LOG_PATH="/www/wwwlogs"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}🚀 SMARTV.SHOP - Configuração Completa do Servidor${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# 1. PREPARAR DIRETÓRIOS E PERMISSÕES
log "📁 Preparando diretórios e permissões..."

# Criar diretórios necessários
mkdir -p "$PROJECT_PATH"
mkdir -p "$LOG_PATH"
mkdir -p "$SSL_CERT_PATH"
mkdir -p "$NGINX_CONFIG_PATH"

# Definir permissões
chown -R www:www "$PROJECT_PATH"
chmod -R 755 "$PROJECT_PATH"

# 2. CONFIGURAR NGINX
log "🌐 Configurando Nginx para $DOMAIN..."

cat > "$NGINX_CONFIG_PATH/${DOMAIN}.conf" << 'EOF'
server {
    listen 80;
    listen 443 ssl http2;
    server_name smartv.shop www.smartv.shop;
    index index.html index.htm default.htm default.html;
    root /www/wwwroot/smarttv/dist;
    
    # SSL Configuration
    ssl_certificate /www/server/panel/vhost/cert/ssmartv/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/ssmartv/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000" always;
    
    # Force HTTPS
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ @fallback;
    }
    
    # API Proxy - Backend em Node.js
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 86400s;
    }
    
    # Frontend SPA - Vite Development/Production
    location / {
        try_files $uri $uri/ @fallback;
    }
    
    # Fallback para SPA (Single Page Application)
    location @fallback {
        proxy_pass http://127.0.0.1:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 86400s;
    }
    
    # Forbidden files and directories
    location ~ ^/(\.user\.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README\.md|package\.json|package-lock\.json|\.env|node_modules|api/node_modules) {
        deny all;
        return 404;
    }
    
    # SSL certificate verification
    location /.well-known/ {
        root /www/wwwroot/smarttv/;
        allow all;
    }
    
    # Logs
    access_log /www/wwwlogs/smartv.log;
    error_log /www/wwwlogs/smartv.error.log;
}
EOF

# 3. CONFIGURAR VITE PARA PRODUÇÃO
log "⚡ Configurando Vite para produção..."

cd "$PROJECT_PATH"

# Atualizar vite.config.ts para produção
cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => ({
  resolve: {
    alias: {
      '@': path.resolve(__dirname),
      '~backend/client': path.resolve(__dirname, './client'),
      '~backend': path.resolve(__dirname, './api'),
    },
  },
  server: {
    port: 5173,
    host: '0.0.0.0',
    strictPort: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  preview: {
    port: 5173,
    host: '0.0.0.0',
    strictPort: true,
    allowedHosts: ['smartv.shop', 'www.smartv.shop', 'localhost']
  },
  plugins: [
    react({
      jsxRuntime: 'automatic'
    }),
  ],
  css: {
    postcss: './postcss.config.js',
  },
  mode: mode || "production",
  build: {
    minify: mode === 'production',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  },
  define: {
    'import.meta.env.VITE_CLIENT_TARGET': JSON.stringify(
      mode === 'development' ? 'http://localhost:5173/api' : 'https://smartv.shop/api'
    ),
  },
}))
EOF

# 4. CONFIGURAR PROXY SERVER PARA PRODUÇÃO
log "🔄 Configurando Proxy Server..."

cat > proxy-server.cjs << 'EOF'
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Configurar CORS para produção
app.use((req, res, next) => {
  const origin = req.headers.origin;
  
  // Permitir origens específicas para produção
  const allowedOrigins = [
    'https://smartv.shop',
    'https://www.smartv.shop',
    'http://localhost:5173',
    'http://127.0.0.1:5173'
  ];
  
  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', 'https://smartv.shop');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Middleware para logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Proxy para o Encore backend
app.use('/', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  logLevel: 'info',
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Backend não disponível' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('Proxying request to:', proxyReq.path);
  }
}));

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Proxy server rodando em http://0.0.0.0:${PORT}`);
  console.log(`🔗 Proxying para: http://localhost:4000`);
  console.log(`🌐 Configurado para: https://smartv.shop`);
});
EOF

log "✅ Configuração completa criada com sucesso!"
echo -e "${BLUE}==============================================================================${NC}"
echo -e "${GREEN}📋 PRÓXIMOS PASSOS:${NC}"
echo -e "${YELLOW}1. Execute: chmod +x server-config.sh${NC}"
echo -e "${YELLOW}2. Execute: sudo ./server-config.sh${NC}"
echo -e "${YELLOW}3. Execute: ./start-smartv.sh (será criado a seguir)${NC}"
echo -e "${BLUE}==============================================================================${NC}"
