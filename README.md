# 🎬 SmartV - Teste Grátis IPTV

**Frontend + Backend Unificado em um só diretório**

## 🚀 Inicialização Rápida

### Windows:
```bash
start.bat
```

### Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

### Ou usando npm:
```bash
npm run dev
```

## 📁 Estrutura do Projeto

```
frontend/
├── 📱 **FRONTEND (React + Vite)**
│   ├── App.tsx                 # Componente principal
│   ├── main.tsx               # Entry point
│   ├── pages/                 # Páginas da aplicação
│   │   ├── LandingPage.tsx    # Página principal
│   │   └── PaymentPage.tsx    # Página de pagamento
│   ├── components/            # Componentes UI
│   ├── src/                   # Código fonte
│   └── dist/                  # Build de produção
│
├── 🔧 **BACKEND (Encore.dev)**
│   └── api/                   # Backend movido para cá
│       ├── encore.ts          # Configuração Encore
│       ├── proxy-server.cjs   # Servidor proxy
│       ├── test/              # API de testes IPTV
│       ├── payment/           # API de pagamentos
│       └── whatsapp/          # API WhatsApp
│
├── 🚀 **SCRIPTS DE INICIALIZAÇÃO**
│   ├── start.bat              # Windows
│   ├── start.sh               # Linux/Mac
│   └── build-production.bat   # Build de produção
│
└── 📦 **CONFIGURAÇÃO**
    ├── package.json           # Dependências unificadas
    ├── vite.config.ts         # Config Vite + Proxy
    └── tsconfig.json          # Config TypeScript
```

## 🔧 Comandos Disponíveis

### Desenvolvimento:
```bash
npm run dev              # Inicia frontend + backend
npm run dev:frontend     # Apenas frontend (porta 5173)
npm run dev:backend      # Apenas backend (porta 4000)
```

### Build:
```bash
npm run build            # Build completo
npm run build:frontend   # Build apenas frontend
npm run build:backend    # Build apenas backend
```

### Produção:
```bash
npm run start            # Serve build de produção
npm run serve:frontend   # Serve frontend (porta 3000)
npm run serve:backend    # Serve backend (porta 4000)
```

### Utilitários:
```bash
npm run install:all      # Instala todas as dependências
npm run clean            # Limpa builds e node_modules
```

## 🌐 URLs de Desenvolvimento

- **Frontend:** http://localhost:5173
- **Backend:** http://localhost:4000
- **API Proxy:** http://localhost:5173/api (proxy para backend)

## 🏗️ Build de Produção

### Automático:
```bash
./build-production.bat   # Windows
```

### Manual:
```bash
npm run install:all      # Instalar dependências
npm run build           # Criar build
```

## 📤 Deploy no Servidor

1. **Build local:**
   ```bash
   npm run build
   ```

2. **Copiar arquivos:**
   ```bash
   # Frontend
   dist/ → /www/wwwroot/smarttv/
   
   # Backend
   api/ → /www/wwwroot/smarttv/backend/
   ```

3. **No servidor:**
   ```bash
   cd /www/wwwroot/smarttv/backend
   npm install
   pm2 start proxy-server.cjs --name smartv-backend
   ```

## ✨ Funcionalidades

- ✅ **Frontend React** com Vite
- ✅ **Backend Encore.dev** integrado
- ✅ **Proxy automático** para API
- ✅ **Hot reload** em desenvolvimento
- ✅ **Build otimizado** para produção
- ✅ **Scripts unificados** para inicialização
- ✅ **Estrutura limpa** em um só diretório

## 🎯 Vantagens da Nova Estrutura

1. **Tudo em um lugar:** Frontend e backend no mesmo diretório
2. **Inicialização simples:** Um comando inicia tudo
3. **Desenvolvimento ágil:** Hot reload para ambos
4. **Deploy facilitado:** Estrutura organizada
5. **Manutenção fácil:** Configuração centralizada

---

**🚀 Projeto SmartV unificado e otimizado!**
